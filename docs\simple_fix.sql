-- 最简单的修复方案 - 直接添加字段
-- 如果字段已存在会报错，但不影响功能

-- 添加remark字段
ALTER TABLE `ddwx_warehouse_voucher` 
ADD COLUMN `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注信息';

-- 添加order_id字段  
ALTER TABLE `ddwx_warehouse_voucher` 
ADD COLUMN `order_id` int(11) NOT NULL DEFAULT '0' COMMENT '关联订单ID';

-- 添加updatetime字段
ALTER TABLE `ddwx_warehouse_voucher` 
ADD COLUMN `updatetime` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间';

-- 添加name字段
ALTER TABLE `ddwx_warehouse_voucher` 
ADD COLUMN `name` varchar(255) NOT NULL DEFAULT '' COMMENT '单据名称';

-- 检查结果
DESCRIBE `ddwx_warehouse_voucher`;
