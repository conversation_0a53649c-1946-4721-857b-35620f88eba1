-- 修复warehouse_voucher表缺失字段的SQL脚本
-- 解决 "fields not exists:[remark]" 等字段缺失错误

-- 1. 检查当前warehouse_voucher表结构
SELECT 'warehouse_voucher表当前结构：' as info;
DESCRIBE `ddwx_warehouse_voucher`;

-- 2. 添加缺失的remark字段
ALTER TABLE `ddwx_warehouse_voucher` 
ADD COLUMN IF NOT EXISTS `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注信息';

-- 3. 添加缺失的order_id字段
ALTER TABLE `ddwx_warehouse_voucher` 
ADD COLUMN IF NOT EXISTS `order_id` int(11) NOT NULL DEFAULT '0' COMMENT '关联订单ID';

-- 4. 添加缺失的updatetime字段
ALTER TABLE `ddwx_warehouse_voucher` 
ADD COLUMN IF NOT EXISTS `updatetime` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间';

-- 5. 添加缺失的shop_order_id字段
ALTER TABLE `ddwx_warehouse_voucher` 
ADD COLUMN IF NOT EXISTS `shop_order_id` int(11) NOT NULL DEFAULT '0' COMMENT '商城订单ID';

-- 6. 添加缺失的cashier_order_id字段
ALTER TABLE `ddwx_warehouse_voucher` 
ADD COLUMN IF NOT EXISTS `cashier_order_id` int(11) NOT NULL DEFAULT '0' COMMENT '收银台订单ID';

-- 7. 确保name字段存在
ALTER TABLE `ddwx_warehouse_voucher` 
ADD COLUMN IF NOT EXISTS `name` varchar(255) NOT NULL DEFAULT '' COMMENT '单据名称';

-- 8. 检查修复后的表结构
SELECT 'warehouse_voucher表修复后结构：' as info;
DESCRIBE `ddwx_warehouse_voucher`;

-- 9. 创建warehouse_stock_log表（如果不存在）
CREATE TABLE IF NOT EXISTS `ddwx_warehouse_stock_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `aid` int(11) NOT NULL DEFAULT '0',
  `bid` int(11) NOT NULL DEFAULT '0',
  `product_id` int(11) NOT NULL DEFAULT '0',
  `product_name` varchar(255) NOT NULL DEFAULT '',
  `before_stock` int(11) NOT NULL DEFAULT '0',
  `after_stock` int(11) NOT NULL DEFAULT '0',
  `adjust_num` int(11) NOT NULL DEFAULT '0',
  `type` tinyint(1) NOT NULL DEFAULT '1',
  `remark` varchar(255) NOT NULL DEFAULT '',
  `voucher_id` int(11) NOT NULL DEFAULT '0',
  `operator` varchar(50) NOT NULL DEFAULT '',
  `createtime` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`),
  KEY `voucher_id` (`voucher_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='仓库库存调整日志表';

-- 10. 验证所有必要字段是否存在
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'ddwx_warehouse_voucher'
ORDER BY ORDINAL_POSITION;

SELECT '所有字段修复完成，请重新测试出库单保存功能' as message;
