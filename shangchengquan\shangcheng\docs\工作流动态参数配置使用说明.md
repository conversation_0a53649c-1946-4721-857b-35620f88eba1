# 工作流动态参数配置使用说明

## 📋 **功能概述**

工作流动态参数配置功能允许管理员为每个工作流自定义参数字段，前端会根据配置自动生成对应的表单界面，支持多种参数类型和验证规则。

## 🚀 **使用步骤**

### 1. **执行数据库初始化**

首先执行SQL文件创建必要的数据表：

```sql
-- 执行参数配置表创建脚本
source sql/workflow_params_config.sql
```

### 2. **后台配置工作流参数**

#### 2.1 进入参数配置页面
1. 登录后台管理系统
2. 进入：扩展功能 → Coze API → 工作流管理
3. 在工作流列表中点击对应工作流的"参数配置"按钮

#### 2.2 添加参数字段
点击"添加参数"按钮，填写以下信息：

- **参数键名**: API调用时使用的参数名（如：BOT_USER_INPUT）
- **显示名称**: 前端表单显示的标签（如：用户输入内容）
- **参数类型**: 选择合适的输入控件类型
- **默认值**: 预设的参数值
- **输入提示**: placeholder文本
- **参数说明**: 详细的使用说明
- **是否必填**: 表单验证控制
- **验证规则**: 数据验证要求
- **排序**: 字段显示顺序

#### 2.3 支持的参数类型

| 类型 | 说明 | 前端控件 | 适用场景 |
|------|------|----------|----------|
| text | 文本 | input | 短文本输入 |
| number | 数字 | input[number] | 数值参数 |
| textarea | 多行文本 | textarea | 长文本内容 |
| select | 选择 | picker | 固定选项选择 |
| image | 图片 | 图片上传 | 图片资源 |
| file | 文件 | 文件选择 | 文件上传 |
| date | 日期 | 日期选择器 | 时间参数 |
| switch | 开关 | switch | 布尔值参数 |

### 3. **设置工作流参数模式**

#### 3.1 编辑工作流
1. 在工作流管理页面点击"编辑"按钮
2. 在"参数配置模式"中选择"自定义字段模式"
3. 保存工作流设置

#### 3.2 参数配置模式说明
- **JSON模式**: 使用传统的JSON格式参数配置
- **自定义字段模式**: 使用动态参数配置生成表单

## 🔧 **API调用方法**

### 1. **获取工作流参数配置**

```javascript
// 获取参数配置
const res = await this.$http.get('/apiCoze/getWorkflowParams', {
    workflow_id: '工作流ID'
});

if (res.code === 1) {
    const { workflow, params, mode } = res.data;
    
    if (mode === 'custom') {
        // 自定义字段模式，使用params数组生成表单
        this.paramList = params;
    } else {
        // JSON模式，使用workflow.default_params
        this.jsonParams = params;
    }
}
```

### 2. **执行工作流（支持动态参数验证）**

```javascript
// 执行工作流
const res = await this.$http.post('/apiCoze/runWorkflowWithParams', {
    workflow_id: '工作流ID',
    params: formData, // 表单数据对象
    is_async: false
});

if (res.code === 1) {
    console.log('执行成功:', res.data);
} else {
    console.log('执行失败:', res.msg);
}
```

## 📱 **前端组件使用**

### 1. **动态表单组件**

```vue
<template>
    <dynamic-form 
        :params="paramList" 
        title="工作流参数"
        submit-text="执行工作流"
        @submit="handleSubmit"
        @cancel="handleCancel"
    />
</template>

<script>
import DynamicForm from '@/pagesB/coze/dynamic-form.vue'

export default {
    components: { DynamicForm },
    data() {
        return {
            paramList: [] // 从API获取的参数配置
        }
    },
    methods: {
        async loadParams() {
            const res = await this.$http.get('/apiCoze/getWorkflowParams', {
                workflow_id: 'your_workflow_id'
            });
            if (res.code === 1) {
                this.paramList = res.data.params;
            }
        },
        
        handleSubmit(formData) {
            // formData 包含所有参数的值
            this.executeWorkflow(formData);
        }
    }
}
</script>
```

### 2. **完整示例页面**

参考文件：`pagesB/coze/workflow-demo.vue`

这个示例页面包含：
- 工作流选择
- 动态参数表单
- JSON模式支持
- 执行结果显示
- 执行历史记录

## 🎯 **配置示例**

### 示例1：文本输入参数
```json
{
    "param_key": "BOT_USER_INPUT",
    "param_name": "用户输入内容",
    "param_type": "textarea",
    "placeholder": "请输入您的问题或需求",
    "description": "用户向AI提出的问题或需求描述",
    "is_required": 1,
    "validation_rule": "required"
}
```

### 示例2：选择参数
```json
{
    "param_key": "gender",
    "param_name": "性别",
    "param_type": "select",
    "param_options": {
        "options": [
            {"value": "男", "label": "男"},
            {"value": "女", "label": "女"}
        ]
    },
    "default_value": "男",
    "is_required": 1
}
```

### 示例3：图片上传参数
```json
{
    "param_key": "image_url",
    "param_name": "图片链接",
    "param_type": "image",
    "placeholder": "请上传或输入图片链接",
    "description": "相关的图片资源链接",
    "validation_rule": "url"
}
```

## ✅ **功能特点**

1. **🔧 灵活配置** - 无需修改代码即可调整参数结构
2. **🎨 用户友好** - 直观的表单界面，支持多种输入类型
3. **✅ 数据验证** - 完整的前后端验证机制
4. **📊 统一管理** - 集中的参数配置管理界面
5. **🔄 向下兼容** - 保持原有JSON模式的完整支持
6. **📱 响应式设计** - 适配各种设备和屏幕尺寸

## 🔍 **故障排除**

### 常见问题

1. **参数配置页面无法访问**
   - 检查工作流ID是否正确
   - 确认已添加参数配置菜单权限

2. **表单验证失败**
   - 检查必填参数是否已填写
   - 确认参数格式是否符合验证规则

3. **工作流执行失败**
   - 检查参数配置是否正确
   - 确认工作流状态是否启用

### 调试方法

1. **查看API返回数据**
```javascript
console.log('参数配置:', res.data);
```

2. **检查表单数据**
```javascript
console.log('提交的参数:', formData);
```

3. **查看执行日志**
   - 后台 → 扩展功能 → Coze API → 执行日志

## 📞 **技术支持**

如有问题，请查看：
- 后台操作日志
- 工作流执行日志
- 浏览器控制台错误信息

---

## 🔄 **更新日志**

### v1.1 (2025-01-31)
**修复的问题**：
- ✅ 修复了当工作流设置为"自定义字段模式"时，执行工作流的接口调用还是使用原来的JSON模式，导致后端无法验证参数的问题
- ✅ 修复了前端表单与后端验证逻辑不匹配的问题
- ✅ 修复了参数配置模式切换时界面显示错误的问题

**新增功能**：
- 🆕 后端控制器 `runWorkflowDemo` 方法现在能够根据工作流的 `param_config_mode` 自动选择参数处理方式
- 🆕 后台工作流演示页面支持动态表单生成，包含8种参数类型的HTML控件
- 🆕 增强的参数验证机制，支持必填字段验证和默认值自动设置
- 🆕 完整的错误提示和用户体验优化

**使用方法更新**：
现在当您在后台配置工作流参数后：
1. 工作流演示页面会自动检测参数配置模式
2. 如果是自定义字段模式，会动态生成对应的表单控件
3. 执行时会根据配置的验证规则进行参数检查
4. 支持参数默认值和必填验证

### v1.0 (2025-01-31)
- 🎉 初始版本发布
- 📝 完整的动态参数配置系统
- 🎨 支持8种参数类型
- 🔧 前后端完整集成

---

**当前版本**: v1.1
**更新时间**: 2025-01-31
**适用系统**: ThinkPHP 6.0+ 商城系统
