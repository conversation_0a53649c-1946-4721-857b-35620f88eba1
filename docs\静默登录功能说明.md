# 静默登录功能完整说明文档

## 概述

静默登录功能允许用户在不显式进行登录操作的情况下，系统自动创建会员账号并完成登录过程。该功能主要用于提升用户体验，减少登录步骤，特别适用于买单、支付等场景。

## 核心组件

### 1. 静默登录控制器文件

#### ApiMaidan.php - 买单模块静默登录
```php
位置：app/controller/ApiMaidan.php
功能：处理买单页面的静默登录逻辑
```

**关键代码段：**
```php
// 第14-22行：买单强制登录控制
$maidan_login = $this->sysset['maidan_login']; // 收款强制登录 1开启 0关闭
if($action!='maidan' && $action!='maidanlog'){
    $this->checklogin();
}

if($action=='maidan' && ($maidan_login==1 || !in_array(platform,['wx','alipay','mp']))){
    $params = [];
    // 买单页面后台开启了强制登录
    $this->checklogin(0,$params);
}
```

```php
// 第235-245行：前端静默注册标识
// 判断是否登录返回给前端，没登录的话前端静默注册
$mid = mid;
$need_login = 0;
$have_login = 1;
$login_tip = '';
if(!$mid){
    $need_login = 1;
}
$rdata['need_login'] = $need_login;
$rdata['have_login'] = $have_login;
$rdata['login_tip'] = $login_tip;
```

#### ApiIndex.php - 主要静默登录逻辑
```php
位置：app/controller/ApiIndex.php
功能：处理各种平台的静默授权登录
```

**关键方法：**

1. **公众号静默授权登录** (第973-988行)
```php
public function mpbaselogin(){
    $fromid = input('param.pid/d');
    
    // 授权登录
    if(input('param.state') && input('param.state') == 'baseauthlogin' && input('param.code')){
        $code = input('param.code');
        $rs = \app\common\Wechat::getAccessTokenByCode(aid,$code);
        // 处理静默授权逻辑
    }
}
```

2. **支付宝静默授权登录** (第2564行)
```php
public function alipaylogin(){
    $silent = input('silent')?:0; // 是否要求静默授权登录
    // 静默授权处理逻辑
}
```

3. **自动添加登录** (第4462-4503行)
```php
public function autoaddlogin(){
    if(getcustom('member_auto_addlogin')){
        if(mid){
            return $this->json(['status'=>1,'msg'=>'登录成功']);
        }
        // 自动创建会员并登录
    }
}
```

#### ApiCommon.php - 登录检查基类
```php
位置：app/controller/ApiCommon.php
功能：提供通用的登录检查方法
```

**checklogin方法** (第178-197行)：
```php
public function checklogin($authlogin = 0,$params=[]){
    if(!$this->member){
        // 是否直接用授权登录
        $logintype = $this->sysset['logintype_'.platform];
        if($logintype == 3){
            $xieyi = Db::name('admin_set_xieyi')->where('aid',aid)->find();
            if(!$xieyi || $xieyi['status'] == 0){
                $authlogin = 1;
            }
        }
        return $this->json(['status'=>-1,'msg'=>'请先登录','authlogin'=>$authlogin,'data'=>$params],1);
    }
    // 其他登录状态检查
}
```

### 2. 静默登录核心类库

#### Member.php - 会员自动注册
```php
位置：app/common/Member.php
功能：处理会员的自动注册和静默登录
```

**autoReg方法** (第5244-5284行)：
```php
public static function autoReg($aid,$sessionid,$platform)
{
    $member = Db::name('member')->where('aid',$aid)->where('session_id',$sessionid)->find();
    if(!$member){
        // 创建新会员数据
        $data = [];
        $data['aid'] = $aid;
        $data['sex'] = 3;
        $data['nickname'] = '用户'.random(6);
        $data['headimg'] = PRE_URL.'/static/img/touxiang.png';
        $data['createtime'] = time();
        $data['session_id'] = $sessionid;
        $data['last_visittime'] = time();
        $data['platform'] = $platform;

        // 添加会员并更新session
        $mid = \app\model\Member::add($aid,$data);
        Db::name('session')->where('aid',$aid)->where('session_id',$sessionid)->update([
            'mid' => $mid,
            'login_time' => time()
        ]);
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->find();
    }
    
    // 设置缓存时间
    $sessionid_time = 7*86400;
    if(getcustom('system_nologin_day')){
        $nologin_day = Db::name('admin_set')->where('aid',$aid)->value('nologin_day');
        if($nologin_day>0){
            $sessionid_time = $nologin_day*86400;
        }
    }
    cache($sessionid.'_mid',$mid,$sessionid_time);
    return $member;
}
```

## 配置项说明

### 1. 数据库配置字段

#### admin_set 表相关字段：

| 字段名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `maidan_login` | tinyint(1) | 1 | 买单收款页是否强制登录：1=开启强制登录，0=允许静默登录 |
| `maidan_auto_reg` | tinyint(1) | 0 | 买单自动注册会员：1=开启，0=关闭 |
| `maidan_bind_tel` | tinyint(1) | 0 | 买单静默登录绑定手机号：1=需要，0=不需要 |
| `maidan_login_tip` | varchar(100) | '' | 买单付款登录提示文字 |
| `member_auto_reg` | tinyint(1) | 0 | 游客自动注册会员：1=开启，0=关闭 |
| `is_member_auto_addlogin` | tinyint(1) | 0 | 游客自动注册登录：1=开启，0=关闭 |

#### 平台登录类型配置：

| 字段名 | 说明 |
|--------|------|
| `logintype_wx` | 微信平台登录类型 |
| `logintype_alipay` | 支付宝平台登录类型 |
| `logintype_mp` | 小程序平台登录类型 |

**登录类型值说明：**
- 1：手机号密码登录
- 2：手机号验证码登录  
- 3：直接授权登录（静默登录）

### 2. 自定义功能开关

#### getcustom() 函数控制的功能：

| 功能标识 | 说明 |
|----------|------|
| `maidan_auto_reg` | 买单自动注册功能 |
| `member_auto_reg` | 会员自动注册功能 |
| `member_auto_addlogin` | 会员自动添加登录功能 |
| `system_nologin_day` | 系统免登录天数设置 |
| `member_register_notel` | 会员注册无需手机号 |
| `coupon_maidan_cashdesk` | 优惠券适用于买单收银台 |

#### 功能开关配置位置详解：

**1. 系统级配置文件：`custom.php`**
```php
位置：根目录/custom.php
作用：定义系统支持的所有自定义功能标识
示例内容：
<?php return [
    'image_ai', 'map_mark', 'video_spider', 'commission2moneypercent', 
    'partner_jiaquan', 'partner_gongxian', 'teamfenhong_pingji', 
    'business_hexiaoplatform', 'money_transfer', 'score_transfer',
    'maidan_auto_reg', 'member_auto_reg', 'member_auto_addlogin',
    // ... 更多功能标识
];
```

**2. 数据库配置表：`custom_set`**
```sql
表名：ddwx_custom_set
字段结构：
- id: 主键ID
- aid: 账户ID
- custom: 功能标识名称
- desc: 功能描述
- disabled: 是否禁用（0=开启，1=禁用）
```

**3. 数据库通用配置：`database.custom`**
```php
位置：config/database.php
作用：在数据库配置中定义基础功能列表
```

#### getcustom() 函数工作原理：

**函数定义位置：`app/common.php` 第1017行**

```php
function getcustom($name=null,$aid=0){
    // 1. 从数据库配置获取基础功能列表
    $custom = \think\facade\Config::get('database.custom');
    if(!$custom) $custom = [];
    
    // 2. 默认添加restaurant功能
    $custom[] = 'restaurant';
    
    // 3. 从custom.php文件加载功能列表
    if(file_exists(ROOT_PATH.'custom.php')){
        $custom2 = include(ROOT_PATH.'custom.php');
        if(is_array($custom2) && !empty($custom2)) $custom = array_merge($custom,$custom2);
    }
    
    // 4. 如果没有指定功能名，返回所有功能列表
    if(!$name) return $custom;
    
    // 5. 检查功能是否存在
    $exit = in_array($name,$custom);
    
    // 6. 如果启用了独立控制，检查数据库设置
    if($exit && in_array('custom_control',$custom)){
        if(!$aid){
            $aid = session('ADMIN_AID')?:input('aid');
        }
        $is_dianda = in_array('custom_control_dianda',$custom);
        return checkcustom($name,$aid,$is_dianda);
    }
    
    return $exit;
}
```

#### 功能开关的配置方法：

**方法1：系统配置文件**
- 编辑根目录的 `custom.php` 文件
- 在返回的数组中添加或删除功能标识
- 重启系统生效

**方法2：数据库配置（如果启用custom_control）**
- 登录后台管理系统
- 查找相关功能设置页面
- 通过界面开启或关闭功能

**方法3：直接操作数据库**
```sql
-- 开启某个功能
INSERT INTO ddwx_custom_set (aid, custom, desc, disabled) 
VALUES (1, 'maidan_auto_reg', '买单自动注册功能', 0);

-- 关闭某个功能
UPDATE ddwx_custom_set SET disabled = 1 
WHERE aid = 1 AND custom = 'maidan_auto_reg';

-- 查看功能状态
SELECT * FROM ddwx_custom_set WHERE aid = 1;
```

#### 特殊功能标识说明：

| 标识 | 在代码中的使用位置 | 具体作用 |
|------|-------------------|----------|
| `maidan_auto_reg` | ApiIndex.php 多处 | 控制买单是否自动注册会员 |
| `member_auto_reg` | 后台设置页面 | 控制游客是否自动注册为会员 |
| `member_auto_addlogin` | ApiIndex.php:4462 | 控制会员自动添加登录功能 |
| `system_nologin_day` | 多个登录相关文件 | 控制系统免登录天数设置 |
| `member_register_notel` | 注册相关页面 | 控制会员注册是否需要手机号 |
| `coupon_maidan_cashdesk` | ApiMaidan.php:184 | 控制优惠券是否适用于买单收银台 |
| `custom_control` | common.php:1028 | 启用功能的独立控制开关 |
| `custom_control_dianda` | common.php:1031 | 点大系统的特殊控制标识 |

## 静默登录工作流程

### 1. 买单静默登录流程

```mermaid
graph TD
    A[用户访问买单页面] --> B{检查maidan_login配置}
    B -->|值为1| C[强制登录检查]
    B -->|值为0| D{检查用户是否已登录}
    D -->|已登录| E[正常访问]
    D -->|未登录| F{检查平台类型}
    F -->|wx/alipay/mp| G[执行静默登录]
    F -->|其他平台| C
    G --> H[自动创建会员]
    H --> I[设置session和缓存]
    I --> J[返回登录成功]
    C --> K{用户登录验证}
    K -->|成功| E
    K -->|失败| L[跳转登录页面]
```

### 2. 支付宝静默授权流程

```mermaid
graph TD
    A[支付宝用户访问] --> B[获取授权码code]
    B --> C[调用支付宝API获取用户信息]
    C --> D{用户是否已存在}
    D -->|存在| E[更新session登录]
    D -->|不存在| F[创建新会员]
    F --> G[设置会员基本信息]
    G --> H[保存到数据库]
    H --> I[设置session和缓存]
    I --> J[返回登录成功]
    E --> J
```

### 3. 微信公众号静默授权流程

```mermaid
graph TD
    A[微信用户访问] --> B[检查是否有授权码]
    B -->|有code| C[获取access_token]
    B -->|无code| D[引导用户授权]
    C --> E[获取用户基本信息]
    E --> F{检查是否快照页模式}
    F -->|是| G[返回授权失败]
    F -->|否| H{用户是否已存在}
    H -->|存在| I[登录现有用户]
    H -->|不存在| J[创建新会员]
    J --> K[设置微信用户信息]
    K --> L[保存并登录]
    I --> M[返回登录成功]
    L --> M
```

## 关键技术点

### 1. Session管理

- **Session表结构**：维护用户会话信息
- **缓存机制**：使用 `cache($sessionid.'_mid', $mid, $time)` 存储用户ID
- **过期时间**：默认7天，可通过后台设置调整

### 2. 平台识别

通过 `platform` 常量识别用户来源：
- `wx`：微信
- `alipay`：支付宝
- `mp`：小程序
- `h5`：H5页面
- `app`：APP应用

### 3. 授权方式

#### 微信公众号：
- **静默授权**：`snsapi_base`，只获取openid
- **用户信息授权**：`snsapi_userinfo`，获取用户详细信息

#### 支付宝：
- **静默参数**：`silent=1` 表示静默授权
- **授权范围**：根据应用配置决定获取信息范围

### 4. 错误处理

- **授权失败**：返回相应错误信息
- **用户拒绝**：引导重新授权
- **网络异常**：提供重试机制

## 使用场景

### 1. 买单收款

- **场景**：用户扫码进入买单页面
- **需求**：快速完成支付，减少登录步骤
- **实现**：根据 `maidan_login` 配置决定是否静默登录

### 2. 商品购买

- **场景**：游客浏览商品并下单
- **需求**：不注册也能完成购买
- **实现**：通过 `member_auto_addlogin` 自动创建临时账号

### 3. 活动参与

- **场景**：用户参与各种营销活动
- **需求**：降低参与门槛
- **实现**：静默创建账号记录用户行为

## 注意事项

### 1. 隐私保护

- 静默登录创建的账号信息最小化
- 用户头像使用默认图片
- 昵称采用随机生成方式

### 2. 数据一致性

- 确保session表和member表数据同步
- 缓存过期时间与数据库设置保持一致
- 多端登录状态统一管理

### 3. 安全考虑

- 验证授权来源的合法性
- 防止重复创建相同用户
- 设置合理的缓存过期时间

### 4. 性能优化

- 使用缓存减少数据库查询
- 批量处理用户创建操作
- 异步处理非关键用户信息更新

## 故障排查

### 1. 静默登录失败

**可能原因：**
- 配置项设置错误
- 平台授权配置问题
- 网络连接异常
- 数据库连接问题

**排查步骤：**
1. 检查 `maidan_login` 等配置项
2. 验证平台授权配置是否正确
3. 查看日志文件确认错误信息
4. 测试数据库连接是否正常

### 2. 用户信息异常

**可能原因：**
- 平台返回数据格式变化
- 用户授权范围不足
- 数据库字段长度限制

**排查步骤：**
1. 检查平台API返回数据
2. 确认授权范围设置
3. 验证数据库表结构

### 3. Session管理问题

**可能原因：**
- 缓存服务异常
- Session表数据不一致
- 并发访问冲突

**排查步骤：**
1. 检查缓存服务状态
2. 对比缓存和数据库数据
3. 查看并发访问日志

## 扩展功能

### 1. 多平台支持

可扩展支持更多第三方平台：
- QQ登录
- 微博登录
- 其他社交平台

### 2. 用户信息补全

静默登录后引导用户补全信息：
- 手机号绑定
- 头像上传
- 个人资料完善

### 3. 行为分析

基于静默登录数据进行用户行为分析：
- 访问路径跟踪
- 停留时间统计
- 转化率分析

## 总结

静默登录功能是提升用户体验的重要功能，通过合理的配置和实现，可以在保证安全性的前提下，大大降低用户的使用门槛。在实际使用中，需要根据具体业务场景选择合适的静默登录策略，并做好相应的监控和维护工作。 