-- 快速修复出库单保存失败问题的SQL脚本
-- 执行此脚本解决 "fields not exists:[amount]" 错误

-- 1. 确保warehouse_stock_log表存在并包含所需字段
CREATE TABLE IF NOT EXISTS `ddwx_warehouse_stock_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `aid` int(11) NOT NULL DEFAULT '0',
  `bid` int(11) NOT NULL DEFAULT '0',
  `product_id` int(11) NOT NULL DEFAULT '0',
  `product_name` varchar(255) NOT NULL DEFAULT '',
  `before_stock` int(11) NOT NULL DEFAULT '0',
  `after_stock` int(11) NOT NULL DEFAULT '0',
  `adjust_num` int(11) NOT NULL DEFAULT '0',
  `type` tinyint(1) NOT NULL DEFAULT '1',
  `remark` varchar(255) NOT NULL DEFAULT '',
  `voucher_id` int(11) NOT NULL DEFAULT '0',
  `operator` varchar(50) NOT NULL DEFAULT '',
  `createtime` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`),
  KEY `voucher_id` (`voucher_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 2. 确保warehouse_voucher表的order_id字段存在
ALTER TABLE `ddwx_warehouse_voucher` 
ADD COLUMN IF NOT EXISTS `order_id` int(11) NOT NULL DEFAULT '0' COMMENT '关联订单ID';

-- 3. 检查表结构
DESCRIBE `ddwx_warehouse_voucher`;
DESCRIBE `ddwx_warehouse_stock_log`;

SELECT '修复完成，请重新测试出库单保存功能' as message;
