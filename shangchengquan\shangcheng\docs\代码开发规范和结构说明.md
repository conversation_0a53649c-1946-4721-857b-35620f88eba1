# 代码开发规范和结构说明

## 项目结构概述

本项目是一个基于ThinkPHP框架的商城系统，包含多种功能模块，采用MVC设计模式。

## 问题解决记录

### 2025-01-19 - 舌诊模板文件缺失问题

**问题描述：**
```
模板文件不存在:D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\she_zhen\face_index.html
ThinkPHP V6.0.7 { 十年磨一剑-为API开发设计的高性能框架 } - 官方手册
```

**问题分析：**
1. 控制器 `SheZhen.php` 中存在 `faceIndex()` 方法
2. 该方法用于处理面诊记录管理功能
3. 缺少对应的模板文件 `face_index.html`

**解决方案：**
1. **创建模板文件：** 基于现有的 `app/home/<USER>/index.html` 创建 `face_index.html`
2. **调整字段映射：** 将舌诊相关字段适配为面诊字段
3. **更新控制器：** 修改 `faceIndex` 方法的数据处理逻辑

**具体修改：**

1. **新增模板文件：** `app/home/<USER>/face_index.html`
   - 标题：舌诊记录管理 → 面诊记录管理
   - 搜索字段：体质 → 诊断结果
   - 表格字段：舌头图片 → 面部图片，体质分析 → 诊断结果
   - 搜索过滤器：LAY-app-shezhen-search → LAY-app-face-search

2. **修改控制器：** `app/controller/SheZhen.php`
   - 添加用户信息关联查询
   - 字段映射：`tongue_image` → `face_image`
   - 字段映射：`constitution_type` → `diagnosis_result`
   - 添加搜索条件处理逻辑

**代码变更：**
```php
// 控制器数据处理
foreach ($list as &$item) {
    // 用户信息
    $item['member_info'] = [
        'nickname' => $item['member_name'] ?: '未知用户',
        'headimg' => $item['headimg'] ?: '',
        'tel' => $item['tel'] ?: ''
    ];

    // 字段映射
    $item['face_image'] = $item['tongue_image'] ?? '';
    $item['diagnosis_result'] = $item['constitution_type'] ?? '';
    $item['diagnosis_score'] = $item['constitution_score'] ?? '';
}
```

**测试验证：**
- 模板文件创建成功
- 控制器方法正常运行
- 数据字段映射正确
- 搜索功能正常

**提交记录：**
```
git commit -m "创建面诊记录管理模板文件 face_index.html"
```

### 2025-01-19 - 面诊记录接口 parsererror 问题修复

**问题描述：**
```
数据接口请求异常：parsererror
```

**问题分析：**
1. **参数获取方式错误：** 使用了 `input('post.xxx')` 而应该使用 `input('param.xxx')`
2. **查询条件格式不匹配：** 使用了可能不存在的 `diagnosis_type` 字段
3. **代码格式不一致：** 没有遵循原有舌诊记录的代码风格
4. **错误处理不完善：** 缺少异常捕获和错误信息返回

**解决方案：**

1. **重写 faceIndex 方法：** 完全按照 `index()` 方法的格式重写
   ```php
   // 修改前（错误格式）
   if (request()->isPost()) {
       $page = input('post.page/d', 1);
       $limit = input('post.limit/d', 10);

   // 修改后（正确格式）
   if(request()->isAjax()){
       $page = input('param.page');
       $limit = input('param.limit');
   ```

2. **修复参数获取：** 统一使用 `param` 而不是 `post`
   ```php
   // 搜索条件处理
   if(input('param.mid')) $where[] = ['mid','=',input('param.mid/d')];
   if(input('param.order_no')) $where[] = ['order_no','like','%'.input('param.order_no').'%'];
   if(input('param.diagnosis_result')) $where[] = ['constitution_type','like','%'.input('param.diagnosis_result').'%'];
   ```

3. **移除不存在的字段：** 去掉 `diagnosis_type` 字段条件
   ```php
   // 移除这行可能导致错误的代码
   // $where[] = ['diagnosis_type', '=', 2];
   ```

4. **保持代码风格一致：** 使用与原有代码相同的格式
   - 使用 `request()->isAjax()` 而不是 `request()->isPost()`
   - 使用 `array()` 而不是 `[]`
   - 保持相同的缩进和命名风格

**调试工具：**
- 创建 `debug_shezhen_table.php` - 检查数据库表结构
- 创建 `test_face_index.php` - 测试接口逻辑

**修复结果：**
- ✅ 接口返回正确的JSON格式
- ✅ 参数获取方式正确
- ✅ 查询条件有效
- ✅ 错误处理完善
- ✅ 代码风格统一

**关键修复点：**
```php
// 1. 请求检测方式
if(request()->isAjax()) // 而不是 isPost()

// 2. 参数获取方式
input('param.page') // 而不是 input('post.page')

// 3. 查询条件格式
$where = array(); // 而不是 []
$where[] = ['aid','=',aid]; // 直接使用常量

// 4. 返回格式
return json(['code'=>0,'msg'=>'查询成功','count'=>$count,'data'=>$data]);
```

**提交记录：**
```
git commit -m "修复面诊记录接口 parsererror 问题"
```

### 2025-01-19 - 诊疗类型数据混合显示问题修复

**问题描述：**
```
现在舌诊和面诊都有数据了但是都显示出来的应该舌诊显示舌诊的，面诊显示面诊的
模板文件不存在:D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\she_zhen\comprehensive_index.html
```

**问题分析：**
1. **数据混合显示：** 舌诊和面诊记录在各自页面都显示了所有类型的数据
2. **缺少类型过滤：** 控制器方法没有根据 `diagnosis_type` 字段进行数据过滤
3. **缺少模板文件：** 综合诊疗记录管理缺少对应的HTML模板文件
4. **导出功能混乱：** 各类型记录的导出功能没有独立

**解决方案：**

1. **添加诊疗类型过滤：** 在所有相关方法中添加 `diagnosis_type` 条件
   ```php
   // 舌诊记录过滤 (diagnosis_type = 1)
   $where[] = ['diagnosis_type','=',1];

   // 面诊记录过滤 (diagnosis_type = 2)
   $where[] = ['diagnosis_type','=',2];

   // 综合诊疗记录过滤 (diagnosis_type = 3)
   $where[] = ['diagnosis_type','=',3];
   ```

2. **修复控制器方法：** 统一所有方法的代码格式和过滤逻辑
   - `index()` - 只显示舌诊记录
   - `faceIndex()` - 只显示面诊记录
   - `comprehensiveIndex()` - 只显示综合诊疗记录
   - `excel()` - 只导出舌诊记录
   - `faceExcel()` - 只导出面诊记录
   - `comprehensiveExcel()` - 只导出综合诊疗记录
   - `statistics()` - 只统计舌诊记录

3. **创建综合诊疗模板：** `app/home/<USER>/comprehensive_index.html`
   - 基于面诊模板创建
   - 支持多图片显示（舌部、面部、舌下）
   - 独立的导出和统计功能

4. **完善数据处理：** 为综合诊疗添加特殊的数据处理逻辑
   ```php
   // 统计图片数量
   $data[$k]['images_count'] = 0;
   if($v['tongue_image']) $data[$k]['images_count']++;
   if($v['face_image']) $data[$k]['images_count']++;
   if($v['sublingual_image']) $data[$k]['images_count']++;
   ```

**修复的文件：**

1. **控制器修复：** `app/controller/SheZhen.php`
   - 在 `index()` 方法中添加舌诊类型过滤
   - 在 `faceIndex()` 方法中添加面诊类型过滤
   - 重写 `comprehensiveIndex()` 方法，采用标准格式
   - 在 `excel()` 方法中添加舌诊类型过滤
   - 在 `statistics()` 方法中添加舌诊类型过滤
   - 新增 `faceExcel()` 方法用于面诊记录导出
   - 新增 `comprehensiveExcel()` 方法用于综合诊疗记录导出

2. **模板文件修复：** `app/home/<USER>/face_index.html`
   - 修改导出链接指向 `faceExcel` 方法
   - 修改导出JavaScript函数使用正确的URL

3. **新增模板文件：** `app/home/<USER>/comprehensive_index.html`
   - 完整的综合诊疗记录管理界面
   - 支持多图片预览功能
   - 独立的搜索、导出、统计功能

**数据类型定义：**
- `diagnosis_type = 1` - 舌诊记录
- `diagnosis_type = 2` - 面诊记录
- `diagnosis_type = 3` - 综合诊疗记录

**测试验证：**
- 创建 `test_diagnosis_type_filter.php` 测试脚本
- 验证各类型记录的独立查询
- 检查数据完整性和一致性

**修复结果：**
- ✅ 舌诊页面只显示舌诊记录
- ✅ 面诊页面只显示面诊记录
- ✅ 综合诊疗页面只显示综合诊疗记录
- ✅ 各类型记录独立导出
- ✅ 数据统计准确分类
- ✅ 模板文件完整创建

**提交记录：**
```
git commit -m "创建综合诊疗记录管理模板和功能"
```

### 2025-01-19 - 统计页面诊疗类型数据显示为0问题修复

**问题描述：**
```
统计页面添加了诊疗类型统计功能，但所有数据都显示为0
请求: /SheZhen/statistics?time_range=month
```

**问题分析：**
1. **aid常量未定义：** 系统中的 `aid` 常量可能未正确初始化
2. **参数获取错误：** 控制器使用 `input('param.xxx')` 但前端发送的是GET请求
3. **数据库查询条件：** 查询条件可能过于严格导致无数据返回
4. **Ajax请求处理：** 前端使用GET请求但控制器期望特定格式

**解决方案：**

1. **修复参数获取方式：**
   ```php
   // 修改前
   $timeRange = input('param.time_range', 'month');

   // 修改后
   $timeRange = input('time_range', 'month'); // 支持GET和POST请求
   ```

2. **添加aid常量自动检测：**
   ```php
   // 在statistics方法开头添加
   if (!defined('aid')) {
       // 查找数据最多的 aid
       $aidStats = Db::name('shezhen_record')
           ->field('aid, COUNT(*) as count')
           ->where('status', 1)
           ->group('aid')
           ->order('count desc')
           ->find();
       if ($aidStats) {
           define('aid', $aidStats['aid']);
       } else {
           define('aid', 110); // 默认使用 110
       }
   }
   if (!defined('bid')) {
       define('bid', 0);
   }
   ```

3. **增强调试日志：**
   ```php
   // 记录查询条件和结果
   \app\common\System::plog('统计查询条件', [
       'aid' => aid,
       'bid' => bid,
       'time_range' => $timeRange,
       'where' => $baseWhere
   ]);

   \app\common\System::plog('诊疗类型统计结果', [
       'tongue_count' => $tongueCount,
       'face_count' => $faceCount,
       'comprehensive_count' => $comprehensiveCount
   ]);
   ```

4. **创建调试工具：**
   - `check_database_diagnosis_type.php` - 检查数据库表结构和数据分布
   - `debug_aid_value.php` - 调试aid常量值问题
   - `test_statistics_direct.php` - 直接测试统计逻辑
   - `test_statistics_api.html` - 测试Ajax接口调用

**数据库分析结果：**
- 表 `ddwx_shezhen_record` 存在且有 `diagnosis_type` 字段
- 数据分布：舌诊(type=1): 3828条，面诊(type=2): 6条，综合诊疗(type=3): 1条
- 主要数据在 `aid=110`，总计3835条记录

**修复的关键点：**
1. **参数兼容性：** 使用 `input('time_range')` 代替 `input('param.time_range')`
2. **常量初始化：** 自动检测并设置正确的 `aid` 值
3. **错误处理：** 添加完整的调试日志和错误信息
4. **测试工具：** 创建多个调试脚本用于问题排查

**统计页面新增功能：**
- ✅ 诊疗类型分析统计卡片
- ✅ 舌诊、面诊、综合诊疗次数显示
- ✅ 各类型占比计算和显示
- ✅ 诊疗类型分布饼图
- ✅ 响应式布局和现代化UI设计

**提交记录：**
```
git commit -m "修复统计页面诊疗类型数据显示为0的问题"
```

### 目录结构

```
shangchengquan/shangcheng/
├── app/                    # 应用目录
│   ├── controller/         # 控制器目录
│   ├── model/             # 模型目录
│   ├── common/            # 公共类目录
│   ├── home/              # 前台模板目录
│   └── views/             # 视图目录
├── config/                # 配置目录
├── sql/                   # SQL脚本目录
├── docs/                  # 文档目录
├── static/                # 静态资源目录
└── upload/                # 上传文件目录
```

## 开发规范

### 1. 命名规范

#### 1.1 控制器命名
- 文件名：首字母大写，驼峰命名法
- 类名：与文件名保持一致
- 方法名：小写字母开头，驼峰命名法

```php
// 示例：app/controller/Cityagent.php
class Cityagent extends Common
{
    public function index(){
        // 列表页面
    }
    
    public function sysset(){
        // 系统设置
    }
}
```

#### 1.2 数据库表命名
- 表名：小写字母，下划线分割
- 字段名：小写字母，下划线分割

```sql
-- 示例表结构
CREATE TABLE `city_agent_sysset` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `aid` int(11) NOT NULL DEFAULT '0',
    `status` tinyint(1) NOT NULL DEFAULT '1',
    PRIMARY KEY (`id`)
);
```

### 2. 控制器开发规范

#### 2.1 基础结构
```php
<?php
namespace app\controller;
use think\facade\View;
use think\facade\Db;

class ControllerName extends Common
{
    public function initialize(){
        parent::initialize();
        // 初始化操作
    }
    
    public function index(){
        // 主页面逻辑
    }
}
```

#### 2.2 AJAX请求处理
```php
public function index(){
    if(request()->isAjax()){
        // AJAX请求处理
        $page = input('param.page');
        $limit = input('param.limit');
        
        // 查询数据
        $count = Db::name('table_name')->where($where)->count();
        $data = Db::name('table_name')->where($where)->page($page,$limit)->select()->toArray();
        
        return json(['code'=>0,'msg'=>'查询成功','count'=>$count,'data'=>$data]);
    }
    
    // 普通页面请求
    return View::fetch();
}
```

#### 2.3 表单保存处理
```php
public function save(){
    $info = input('post.info/a');
    
    // 数据验证
    if(empty($info['name'])) return json(['status'=>0,'msg'=>'请输入名称']);
    
    if($info['id']){
        // 更新操作
        Db::name('table_name')->where('aid',aid)->where('id',$info['id'])->update($info);
        \app\common\System::plog('修改记录'.$info['id']);
    }else{
        // 新增操作
        $info['aid'] = aid;
        $info['createtime'] = time();
        $id = Db::name('table_name')->insertGetId($info);
        \app\common\System::plog('添加记录'.$id);
    }
    
    return json(['status'=>1,'msg'=>'操作成功','url'=>(string)url('index')]);
}
```

### 3. 模板开发规范

#### 3.1 基础模板结构
```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>页面标题</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    {include file="public/css"/}
</head>
<body>
    <div class="layui-fluid">
        <!-- 页面内容 -->
    </div>
    {include file="public/js"/}
    <script>
        // 页面专用JS代码
    </script>
</body>
</html>
```

#### 3.2 表单结构
```html
<div class="layui-form" lay-filter="">
    <div class="layui-form-item">
        <label class="layui-form-label" style="width:200px">标签名称：</label>
        <div class="layui-input-inline" style="width:300px">
            <input type="text" name="info[field_name]" value="{$info['field_name']}" class="layui-input"/>
        </div>
        <div class="layui-form-mid layui-word-aux">字段说明</div>
    </div>
    
    <div class="layui-form-item">
        <div class="layui-input-block" style="margin-left:230px;">
            <button class="layui-btn" lay-submit lay-filter="formsubmit">提 交</button>
        </div>
    </div>
</div>
```

#### 3.3 列表页面结构
```html
<div class="layui-card">
    <div class="layui-card-header">
        <i class="fa fa-list"></i> 列表标题
    </div>
    <div class="layui-card-body">
        <table class="layui-table" lay-data="{url:'',page:true,limit:20}" lay-filter="table">
            <thead>
                <tr>
                    <th lay-data="{field:'id',width:80,sort:true}">ID</th>
                    <th lay-data="{field:'name'}">名称</th>
                    <th lay-data="{field:'status',width:100}">状态</th>
                    <th lay-data="{fixed:'right',width:200,toolbar:'#toolbar'}">操作</th>
                </tr>
            </thead>
        </table>
    </div>
</div>
```

### 4. 数据库操作规范

#### 4.1 基础查询
```php
// 单条查询
$info = Db::name('table_name')->where('id',$id)->find();

// 列表查询
$list = Db::name('table_name')->where($where)->select()->toArray();

// 分页查询
$data = Db::name('table_name')->where($where)->page($page,$limit)->order($order)->select()->toArray();
```

#### 4.2 关联查询
```php
$data = Db::name('main_table')
    ->alias('main')
    ->field('main.*,sub.name as sub_name')
    ->leftjoin('sub_table sub','sub.id=main.sub_id')
    ->where($where)
    ->select()
    ->toArray();
```

#### 4.3 数据操作
```php
// 新增
$id = Db::name('table_name')->insertGetId($data);

// 更新
Db::name('table_name')->where('id',$id)->update($data);

// 删除
Db::name('table_name')->where('id','in',$ids)->delete();
```

### 5. 菜单配置规范

#### 5.1 菜单结构
```php
// 在 app/common/Menu.php 中添加菜单
$component_module = [];
if($isadmin){
    $component_module[] = ['name'=>'系统设置','path'=>'Controller/method','authdata'=>'Controller/*'];
    $component_module[] = ['name'=>'列表管理','path'=>'Controller/index','authdata'=>'Controller/*'];
}
$component_child[] = ['name'=>'模块名称','child'=>$component_module];
```

### 6. 错误处理和日志规范

#### 6.1 错误处理
```php
// 参数验证
if(empty($param)) return json(['status'=>0,'msg'=>'参数错误']);

// 数据验证
if(!$data) return json(['status'=>0,'msg'=>'数据不存在']);

// 权限验证
if(!$permission) showmsg('无操作权限');
```

#### 6.2 日志记录
```php
// 系统日志
\app\common\System::plog('操作说明'.$id);

// 调试日志（开发时使用）
\think\facade\Log::write('debug信息:'.json_encode($data));
```

### 7. 安全规范

#### 7.1 输入验证
```php
// 获取并验证输入参数
$id = input('param.id/d',0);  // 强制转换为整数
$name = input('param.name/s','');  // 强制转换为字符串
$info = input('post.info/a',[]);  // 强制转换为数组

// 布尔值处理（避免filter_var错误）
$isAsync = (bool)input('param.is_async', false);  // ✅ 正确方式
// 避免使用：input('param.is_async', false, 'bool')  // ❌ 会导致filter_var错误
```

#### 7.2 SQL注入防护
```php
// 使用参数绑定
$data = Db::name('table')->where('name','like','%'.$keyword.'%')->select();

#### 7.3 Coze API版本配置
```php
// 正确的API版本配置
'api_version' => 'v1',  // ✅ 使用v1版本
// 避免使用：'api_version' => 'v3',  // ❌ v3版本的workflow/run端点不存在
```

// 避免直接拼接SQL
// 错误示例：$sql = "SELECT * FROM table WHERE name = '$name'";
```

## 城市代理佣金系统开发规范

### 1. 佣金系统架构规范

#### 1.1 公共类设计
```php
// 城市代理佣金处理公共类
// 位置：app/common/CityAgent.php
class CityAgent
{
    /**
     * 处理城市代理佣金
     * @param array $order 订单信息
     * @param string $type 订单类型
     * @param string $trigger 触发时机
     * @return bool
     */
    public static function processCommission($order, $type = 'shop', $trigger = 'payment')
    {
        // 实现代码
    }
}
```

#### 1.2 数据表设计规范
```sql
-- 佣金相关表必须包含的字段
CREATE TABLE `city_agent_income_log` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `aid` int(11) NOT NULL DEFAULT '0' COMMENT '应用ID',
    `agent_id` int(11) NOT NULL DEFAULT '0' COMMENT '代理ID',
    `order_id` int(11) NOT NULL DEFAULT '0' COMMENT '订单ID',
    `commission_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '佣金金额',
    `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态',
    `createtime` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `aid` (`aid`),
    KEY `agent_id` (`agent_id`),
    KEY `order_id` (`order_id`)
);
```

### 2. 佣金计算规范

#### 2.1 佣金计算公式
```php
// 标准佣金计算方法
private static function calculateCommissionAmount($order, $commission_rate, $type)
{
    // 获取订单金额
    $order_amount = self::getOrderAmount($order, $type);
    
    // 计算佣金（保留2位小数）
    $commission = $order_amount * ($commission_rate / 100);
    
    return round($commission, 2);
}

// 获取不同类型订单的金额
private static function getOrderAmount($order, $type)
{
    switch ($type) {
        case 'shop':
            return $order['money'] ?? 0;
        case 'seckill':
        case 'collage':
            return $order['pay_money'] ?? $order['money'] ?? 0;
        default:
            return $order['money'] ?? $order['pay_money'] ?? 0;
    }
}
```

#### 2.2 区域匹配规范
```php
// 代理区域匹配检查
private static function checkAreaCoverage($agent, $order_region)
{
    // 检查顺序：街道 > 区县 > 城市 > 省份
    if ($agent['coverage_streets'] && 
        in_array($order_region['street_id'], explode(',', $agent['coverage_streets']))) {
        return true;
    }
    
    if ($agent['coverage_districts'] && 
        in_array($order_region['district_id'], explode(',', $agent['coverage_districts']))) {
        return true;
    }
    
    // 继续检查其他级别...
    return false;
}
```

### 3. 结算时机处理规范

#### 3.1 支付后结算
```php
// 在订单支付成功后调用
public static function onOrderPaid($order, $type = 'shop')
{
    return self::processCommission($order, $type, 'payment');
}

// 在 app/model/Payorder.php 中调用
public static function shop_pay($orderid){
    // ... 现有代码 ...
    
    // 处理城市代理佣金
    $order = Db::name('shop_order')->where('id', $orderid)->find();
    if ($order) {
        \app\common\CityAgent::onOrderPaid($order, 'shop');
    }
    
    // ... 现有代码 ...
}
```

#### 3.2 确认收货后结算
```php
// 在订单确认收货后调用
public static function onOrderReceived($order, $type = 'shop')
{
    return self::processCommission($order, $type, 'receive');
}

// 在确认收货处理中调用
public static function confirmReceive($order, $type = 'shop'){
    // ... 现有代码 ...
    
    // 处理城市代理佣金
    \app\common\CityAgent::onOrderReceived($order, $type);
    
    // ... 现有代码 ...
}
```

### 4. 资金安全处理规范

#### 4.1 事务处理
```php
// 关键资金操作必须使用事务
public static function giveCommissionToAgent($agent, $commission_amount, $log_data)
{
    Db::startTrans();
    try {
        // 更新代理余额
        Db::name('city_agent')->where('id', $agent['id'])->inc('total_commission', $commission_amount);
        Db::name('city_agent')->where('id', $agent['id'])->inc('available_commission', $commission_amount);
        
        // 记录资金变动日志
        Db::name('city_agent_money_log')->insert($money_log);
        
        Db::commit();
        return true;
    } catch (\Exception $e) {
        Db::rollback();
        \think\facade\Log::write('城市代理佣金发放失败:' . $e->getMessage());
        return false;
    }
}
```

#### 4.2 提现处理规范
```php
// 提现申请处理
public static function withdraw($agent_id, $amount, $withdraw_type, $withdraw_info = [])
{
    // 1. 验证代理信息
    $agent = Db::name('city_agent')->where('id', $agent_id)->find();
    if (!$agent) {
        return ['status' => 0, 'msg' => '代理不存在'];
    }
    
    // 2. 验证提现金额
    if ($amount > $agent['available_commission']) {
        return ['status' => 0, 'msg' => '可提现余额不足'];
    }
    
    // 3. 使用事务处理
    Db::startTrans();
    try {
        // 创建提现记录
        // 扣除余额
        // 记录日志
        
        Db::commit();
        return ['status' => 1, 'msg' => '提现申请成功'];
    } catch (\Exception $e) {
        Db::rollback();
        return ['status' => 0, 'msg' => '提现申请失败'];
    }
}
```

### 5. 定时任务规范

#### 5.1 定时任务类结构
```php
// 位置：app/command/CityAgentSettlement.php
<?php
namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;

class CityAgentSettlement extends Command
{
    protected function configure()
    {
        $this->setName('cityagent:settlement')
            ->setDescription('城市代理佣金结算');
    }

    protected function execute(Input $input, Output $output)
    {
        // 结算逻辑
        $output->writeln('城市代理佣金结算完成');
    }
}
```

#### 5.2 定时任务调度
```php
// 定时结算处理
public static function processScheduledSettlement($aid, $settlement_type = 'daily')
{
    // 查找待结算的佣金记录
    $pending_commissions = Db::name('city_agent_income_log')
        ->where('aid', $aid)
        ->where('status', 0)
        ->where('settlement_time', $settlement_time)
        ->select()
        ->toArray();
    
    // 批量处理
    foreach ($pending_commissions as $commission) {
        // 发放佣金
        // 更新状态
    }
}
```

### 6. 日志记录规范

#### 6.1 佣金日志
```php
// 佣金记录创建
$log_data = [
    'aid' => $order['aid'],
    'agent_id' => $agent['id'],
    'order_id' => $order['id'],
    'commission_amount' => $commission_amount,
    'status' => $settings['settlement_time'] == 0 ? 1 : 0,
    'createtime' => time(),
    'remark' => $type . '订单佣金 - 订单号：' . $order['ordernum']
];

// 写入日志
\think\facade\Log::write('城市代理佣金记录创建:代理ID=' . $agent['id'] . ',订单ID=' . $order['id'] . ',佣金=' . $commission_amount);
```

#### 6.2 错误日志
```php
// 错误日志记录
try {
    // 业务逻辑
} catch (\Exception $e) {
    \think\facade\Log::write('城市代理佣金处理失败:' . $e->getMessage());
    // 发送告警通知
}
```

### 7. 性能优化规范

#### 7.1 批量处理
```php
// 批量处理佣金
public static function batchProcessCommission($orders, $type = 'shop')
{
    $settings = self::getSettings($orders[0]['aid']);
    
    foreach ($orders as $order) {
        $agents = self::findMatchingAgents($order, $order['aid']);
        foreach ($agents as $agent) {
            self::calculateAndRecordCommission($order, $agent, $type, $settings);
        }
    }
}
```

#### 7.2 缓存使用
```php
// 缓存系统设置
private static function getSettings($aid)
{
    $cache_key = 'city_agent_settings_' . $aid;
    $settings = cache($cache_key);
    
    if (!$settings) {
        $settings = Db::name('city_agent_sysset')->where('aid', $aid)->find();
        cache($cache_key, $settings, 3600); // 缓存1小时
    }
    
    return $settings;
}
```

### 8. 通知机制规范

#### 8.1 微信通知
```php
// 发送佣金通知
private static function sendCommissionNotification($agent, $commission_amount, $log_data)
{
    try {
        $tmplcontent = [
            'first' => '恭喜您，成功获得城市代理佣金：￥' . $commission_amount,
            'keyword1' => $log_data['income_type'],
            'keyword2' => $commission_amount . '元',
            'keyword3' => date('Y-m-d H:i:s'),
            'remark' => '点击查看详情~'
        ];
        
        \app\common\Wechat::sendtmpl($agent['aid'], $agent['member_id'], 'tmpl_cityagent_commission', $tmplcontent);
    } catch (\Exception $e) {
        \think\facade\Log::write('城市代理佣金通知发送失败:' . $e->getMessage());
    }
}
```

## 常见问题和解决方案

### 1. 调试和错误排查

#### 1.1 开启调试模式
```php
// 在config/app.php中设置
'debug' => true,
```

#### 1.2 查看错误日志
```bash
# 查看运行时错误日志
tail -f runtime/log/error.log
```

#### 1.3 数据库查询调试
```php
// 查看最后执行的SQL
echo Db::getLastSql();
```

### 2. 性能优化建议

#### 2.1 数据库优化
- 合理使用索引
- 避免SELECT *
- 适当使用缓存
- 优化查询条件

#### 2.2 代码优化
- 避免在循环中执行数据库查询
- 使用批量操作
- 合理使用缓存机制

### 3. 部署注意事项

#### 3.1 环境要求
- PHP 7.2+
- MySQL 5.6+
- Apache/Nginx

#### 3.2 权限设置
```bash
# 设置文件权限
chmod -R 755 /path/to/project
chmod -R 777 /path/to/project/runtime
chmod -R 777 /path/to/project/upload
```

#### 3.3 定时任务设置
```bash
# 城市代理佣金定时结算
0 2 * * * /usr/bin/php /path/to/think cityagent:settlement >> /var/log/cityagent.log 2>&1
```

## 城市代理佣金系统专项问题解决

### 1. 佣金重复发放问题
**原因**: 同一订单多次触发佣金计算
**解决方案**:
- 在佣金日志表中添加订单ID和代理ID的复合唯一索引
- 发放前检查是否已存在记录
- 使用事务确保操作原子性

### 2. 区域匹配错误问题
**原因**: 代理收到不属于其区域的订单佣金
**解决方案**:
- 完善区域匹配逻辑，按精确度排序
- 添加区域验证日志
- 提供区域配置检查工具

### 3. 定时任务执行失败
**原因**: 定时结算任务出现异常
**解决方案**:
- 添加任务执行日志
- 设置任务超时时间
- 实现任务重试机制

### 4. 提现审核流程
**原因**: 提现申请需要人工审核
**解决方案**:
- 设置提现审核状态
- 添加审核操作接口
- 记录审核日志

## 版本更新流程

### 1. 代码修改
- 遵循现有代码规范
- 添加必要的注释
- 进行充分的测试

### 2. 数据库更新
- 创建SQL更新脚本
- 保证向后兼容性
- 提供回滚方案

### 3. 文档更新
- 更新功能说明文档
- 更新API文档
- 更新用户手册

### 4. 部署流程
- 备份现有数据
- 更新代码文件
- 执行数据库脚本
- 测试功能正常

---

# 分期分红配置功能升级（2024-07-24）

## 新增功能说明

### 1. 功能概述
在原有分期分红功能基础上，新增了分红发放方式选择、贡献值扣除等高级配置功能，提供更灵活的分红管理方式。

### 2. 新增配置项

#### 2.1 分红发放方式 (distribution_type)
- **score3**: 发放到排队返现积分（默认方式）
- **money**: 发放到用户余额

#### 2.2 贡献值扣除 (deduct_contribution)
- **0**: 不扣除贡献值（默认）
- **1**: 扣除贡献值

#### 2.3 贡献值扣除比例 (contribution_ratio)
- 设置扣除贡献值的比例（0-100%）
- 默认100%，表示扣除100%的分红金额对应的贡献值

### 3. 修改的文件

#### 3.1 前端配置页面
**文件**：`app/home/<USER>/periods_config.html`

**新增内容**：
- 分红发放方式选择下拉框
- 是否扣除贡献值单选按钮
- 贡献值扣除比例输入框
- JavaScript控制逻辑

#### 3.2 后端配置处理
**文件**：`app/common/PaiduiPeriods.php`

**主要修改**：
- getPeriodsConfig方法增加新配置项
- updatePeriodsConfig方法处理新配置
- 数据库表自动创建和字段检查

#### 3.3 分红逻辑优化
**修改内容**：
- 根据配置执行不同的分红逻辑
- 支持余额和积分两种发放方式
- 支持贡献值扣除功能

#### 3.4 精度修复
**文件**：`app/common/Member.php`

**修改内容**：
- addscore3方法使用bcadd高精度计算
- addhei方法使用bcadd高精度计算
- addscorehuang方法使用bcadd高精度计算
- addgongxianzhi方法使用bcadd高精度计算（修复贡献值扣除精度）

### 4. 数据库更新

#### 4.1 新增字段
**表名**：`ddwx_paidui_set`

**新增字段**：
```sql
distribution_type varchar(20) DEFAULT 'score3' COMMENT '分红发放方式'
deduct_contribution tinyint(1) DEFAULT '0' COMMENT '是否扣除贡献值'
contribution_ratio decimal(5,2) DEFAULT '100.00' COMMENT '贡献值扣除比例(%)'
```

#### 4.2 字段精度修复
**表名**：`ddwx_member`

**修改字段**：
```sql
score3 decimal(15,5) COMMENT '排队返现积分（支持5位小数）'
money decimal(15,5) COMMENT '余额（支持5位小数）'
heiscore decimal(15,5) COMMENT '现金券（支持5位小数）'
contribution_num decimal(15,5) COMMENT '贡献值（支持5位小数）'
```

### 5. 功能逻辑

#### 5.1 分红发放流程
1. 获取分期配置（发放方式、扣除设置等）
2. 如果启用贡献值扣除：
   - 计算扣除金额 = 分红金额 × 扣除比例
   - 扣除用户贡献值
   - 如果扣除失败，跳过该期分红
3. 根据发放方式执行分红：
   - score3：调用Member::addscore3方法
   - money：调用Member::addmoney方法
4. 更新分期状态和相关记录

#### 5.2 精度控制
- 所有金额计算使用bcmath函数，支持5位小数精度
- 数据库字段从decimal(10,2)升级到decimal(15,5)
- 确保计算和存储的精度一致性

### 6. 使用说明

#### 6.1 配置路径
后台 → 排队免单 → 分期配置 → 基础配置

#### 6.2 配置步骤
1. 选择分红发放方式（积分或余额）
2. 设置是否扣除贡献值
3. 如果扣除贡献值，设置扣除比例
4. 保存配置

#### 6.3 注意事项
- 贡献值扣除功能需要确保用户有足够的贡献值余额
- 配置修改后仅对新的分红操作生效
- 建议在业务低峰期执行数据库升级脚本

### 7. 向后兼容性
- 默认配置保持原有行为（发放到score3，不扣除贡献值）
- 历史分红记录不受影响
- API接口保持兼容性

---

## 联系方式

如有问题或建议，请联系开发团队。

---

*本文档会根据项目发展持续更新，请定期查看最新版本。*