#!/bin/bash

# 抽奖系统物流功能提交脚本

echo "开始提交抽奖系统物流功能..."

# 检查当前目录
echo "当前工作目录: $(pwd)"

# 添加所有修改的文件
git add .

# 提交更改
git commit -m "feat: 为抽奖系统增加发货物流功能

功能特性:
- 在抽奖记录表中增加物流相关字段(express_com, express_no, send_time, express_status等)
- 后端抽奖记录管理增加发货功能和物流查询功能
- 前端抽奖记录页面增加物流查询按钮
- 新增前端物流查询页面，支持实时物流信息查看
- 发货后自动发送微信通知给用户
- 支持修改物流信息功能

修改文件:
- shangchengquan/shangcheng/app/controller/Choujiang.php: 增加发货和物流查询方法
- shangchengquan/shangcheng/app/controller/ApiChoujiang.php: 增加前端物流查询接口
- shangchengquan/shangcheng/app/home/<USER>/record.html: 增加发货和物流查询功能
- tiantianshande/activity/xydzp/myprize.vue: 增加物流查询按钮
- tiantianshande/pagesExt/choujiang/logistics.vue: 新增物流查询页面
- tiantianshande/pages.json: 添加物流页面路由配置
- choujiang_logistics_upgrade.sql: 数据库升级脚本

技术实现:
- 参考订单系统的物流功能实现
- 使用统一的物流查询接口
- 保持与现有系统的一致性
- 支持多种快递公司查询"

echo "提交完成！"

# 显示提交状态
git status

echo "抽奖系统物流功能已成功提交到Git仓库"
