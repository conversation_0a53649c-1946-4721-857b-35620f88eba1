<template>
	<view class="dynamic-form">
		<view class="form-title">{{title}}</view>
		
		<form @submit.prevent="submitForm">
			<view v-for="(param, index) in params" :key="param.param_key" class="form-item">
				<view class="form-label">
					{{param.param_name}}
					<text v-if="param.is_required" class="required">*</text>
				</view>
				
				<!-- 文本输入 -->
				<input 
					v-if="param.param_type === 'text'" 
					type="text" 
					class="form-input"
					:placeholder="param.placeholder || '请输入' + param.param_name"
					v-model="formData[param.param_key]"
					:required="param.is_required"
				/>
				
				<!-- 数字输入 -->
				<input 
					v-if="param.param_type === 'number'" 
					type="number" 
					class="form-input"
					:placeholder="param.placeholder || '请输入' + param.param_name"
					v-model="formData[param.param_key]"
					:required="param.is_required"
				/>
				
				<!-- 多行文本 -->
				<textarea 
					v-if="param.param_type === 'textarea'" 
					class="form-textarea"
					:placeholder="param.placeholder || '请输入' + param.param_name"
					v-model="formData[param.param_key]"
					:required="param.is_required"
				></textarea>
				
				<!-- 选择框 -->
				<picker 
					v-if="param.param_type === 'select'" 
					:value="getSelectIndex(param)"
					:range="getSelectOptions(param)"
					range-key="label"
					@change="onSelectChange($event, param)"
				>
					<view class="form-select">
						{{getSelectLabel(param) || param.placeholder || '请选择' + param.param_name}}
						<text class="select-arrow">▼</text>
					</view>
				</picker>
				
				<!-- 图片上传 -->
				<view v-if="param.param_type === 'image'" class="form-image">
					<input 
						type="text" 
						class="form-input"
						:placeholder="param.placeholder || '请输入图片链接'"
						v-model="formData[param.param_key]"
						:required="param.is_required"
					/>
					<button type="button" class="upload-btn" @click="uploadImage(param.param_key)">上传图片</button>
				</view>
				
				<!-- 文件上传 -->
				<view v-if="param.param_type === 'file'" class="form-file">
					<input 
						type="text" 
						class="form-input"
						:placeholder="param.placeholder || '请选择文件'"
						v-model="formData[param.param_key]"
						:required="param.is_required"
						readonly
					/>
					<button type="button" class="upload-btn" @click="uploadFile(param.param_key)">选择文件</button>
				</view>
				
				<!-- 日期选择 -->
				<picker 
					v-if="param.param_type === 'date'" 
					mode="date"
					:value="formData[param.param_key]"
					@change="onDateChange($event, param)"
				>
					<view class="form-select">
						{{formData[param.param_key] || param.placeholder || '请选择日期'}}
						<text class="select-arrow">📅</text>
					</view>
				</picker>
				
				<!-- 开关 -->
				<switch 
					v-if="param.param_type === 'switch'" 
					:checked="!!formData[param.param_key]"
					@change="onSwitchChange($event, param)"
					class="form-switch"
				/>
				
				<!-- 参数说明 -->
				<view v-if="param.description" class="form-desc">{{param.description}}</view>
			</view>
			
			<view class="form-actions">
				<button type="submit" class="submit-btn">{{submitText}}</button>
				<button type="button" class="cancel-btn" @click="cancel" v-if="showCancel">取消</button>
			</view>
		</form>
	</view>
</template>

<script>
export default {
	name: 'DynamicForm',
	props: {
		title: {
			type: String,
			default: '参数配置'
		},
		params: {
			type: Array,
			default: () => []
		},
		submitText: {
			type: String,
			default: '提交'
		},
		showCancel: {
			type: Boolean,
			default: true
		}
	},
	data() {
		return {
			formData: {}
		}
	},
	watch: {
		params: {
			handler(newParams) {
				this.initFormData(newParams);
			},
			immediate: true,
			deep: true
		}
	},
	methods: {
		// 初始化表单数据
		initFormData(params) {
			const data = {};
			params.forEach(param => {
				data[param.param_key] = param.default_value || '';
			});
			this.formData = data;
		},
		
		// 获取选择框选项
		getSelectOptions(param) {
			if (param.param_options && param.param_options.options) {
				return param.param_options.options;
			}
			return [];
		},
		
		// 获取选择框当前索引
		getSelectIndex(param) {
			const options = this.getSelectOptions(param);
			const currentValue = this.formData[param.param_key];
			return options.findIndex(option => option.value === currentValue);
		},
		
		// 获取选择框显示标签
		getSelectLabel(param) {
			const options = this.getSelectOptions(param);
			const currentValue = this.formData[param.param_key];
			const option = options.find(option => option.value === currentValue);
			return option ? option.label : '';
		},
		
		// 选择框变化
		onSelectChange(event, param) {
			const options = this.getSelectOptions(param);
			const selectedOption = options[event.detail.value];
			if (selectedOption) {
				this.formData[param.param_key] = selectedOption.value;
			}
		},
		
		// 日期变化
		onDateChange(event, param) {
			this.formData[param.param_key] = event.detail.value;
		},
		
		// 开关变化
		onSwitchChange(event, param) {
			this.formData[param.param_key] = event.detail.value;
		},
		
		// 上传图片
		uploadImage(paramKey) {
			uni.chooseImage({
				count: 1,
				success: (res) => {
					// 这里应该调用上传接口
					// 暂时直接使用本地路径
					this.formData[paramKey] = res.tempFilePaths[0];
					uni.showToast({
						title: '图片选择成功',
						icon: 'success'
					});
				}
			});
		},
		
		// 上传文件
		uploadFile(paramKey) {
			// #ifdef H5
			const input = document.createElement('input');
			input.type = 'file';
			input.onchange = (e) => {
				const file = e.target.files[0];
				if (file) {
					this.formData[paramKey] = file.name;
					uni.showToast({
						title: '文件选择成功',
						icon: 'success'
					});
				}
			};
			input.click();
			// #endif
			
			// #ifndef H5
			uni.showToast({
				title: '当前平台不支持文件选择',
				icon: 'none'
			});
			// #endif
		},
		
		// 表单验证
		validateForm() {
			for (let param of this.params) {
				if (param.is_required && !this.formData[param.param_key]) {
					uni.showToast({
						title: param.param_name + '不能为空',
						icon: 'none'
					});
					return false;
				}
			}
			return true;
		},
		
		// 提交表单
		submitForm() {
			if (!this.validateForm()) {
				return;
			}
			
			this.$emit('submit', this.formData);
		},
		
		// 取消
		cancel() {
			this.$emit('cancel');
		}
	}
}
</script>

<style scoped>
.dynamic-form {
	padding: 20rpx;
}

.form-title {
	font-size: 36rpx;
	font-weight: bold;
	margin-bottom: 30rpx;
	text-align: center;
}

.form-item {
	margin-bottom: 30rpx;
}

.form-label {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 10rpx;
}

.required {
	color: #ff4757;
	margin-left: 5rpx;
}

.form-input, .form-textarea {
	width: 100%;
	padding: 20rpx;
	border: 2rpx solid #e1e8ed;
	border-radius: 8rpx;
	font-size: 28rpx;
	box-sizing: border-box;
}

.form-textarea {
	height: 120rpx;
	resize: none;
}

.form-select {
	width: 100%;
	padding: 20rpx;
	border: 2rpx solid #e1e8ed;
	border-radius: 8rpx;
	font-size: 28rpx;
	background-color: #fff;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.select-arrow {
	color: #999;
}

.form-image, .form-file {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.upload-btn {
	padding: 20rpx 30rpx;
	background-color: #007aff;
	color: #fff;
	border: none;
	border-radius: 8rpx;
	font-size: 24rpx;
	white-space: nowrap;
}

.form-switch {
	transform: scale(1.2);
}

.form-desc {
	font-size: 24rpx;
	color: #666;
	margin-top: 10rpx;
	line-height: 1.4;
}

.form-actions {
	margin-top: 50rpx;
	display: flex;
	gap: 20rpx;
}

.submit-btn {
	flex: 1;
	padding: 25rpx;
	background-color: #007aff;
	color: #fff;
	border: none;
	border-radius: 8rpx;
	font-size: 32rpx;
}

.cancel-btn {
	flex: 1;
	padding: 25rpx;
	background-color: #f8f9fa;
	color: #666;
	border: 2rpx solid #e1e8ed;
	border-radius: 8rpx;
	font-size: 32rpx;
}
</style>
