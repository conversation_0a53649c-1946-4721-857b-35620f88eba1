# 抽奖系统物流功能测试说明

## 功能测试清单

### 1. 数据库升级测试
- [ ] 执行 `choujiang_logistics_upgrade.sql` 脚本
- [ ] 确认 `ddwx_choujiang_record` 表新增字段：
  - `express_com` - 快递公司
  - `express_no` - 快递单号
  - `send_time` - 发货时间
  - `express_status` - 物流状态
  - `address` - 收货地址
  - `area` - 收货区域

### 2. 后端管理功能测试

#### 2.1 抽奖记录页面测试
- [ ] 访问：后台 -> 抽奖活动 -> 抽奖记录
- [ ] 确认页面显示物流信息列
- [ ] 确认中奖记录显示发货按钮
- [ ] 确认已发货记录显示"查物流"和"改物流"按钮

#### 2.2 发货功能测试
- [ ] 点击"发货"按钮
- [ ] 确认弹出发货对话框
- [ ] 选择快递公司，输入快递单号
- [ ] 点击确定，确认发货成功
- [ ] 确认记录状态更新为已发货
- [ ] 确认用户收到发货通知（如果配置了微信通知）

#### 2.3 物流查询测试
- [ ] 点击"查物流"按钮
- [ ] 确认显示物流信息弹窗
- [ ] 确认显示快递公司、快递单号、发货时间
- [ ] 确认显示物流轨迹信息（如果有）
- [ ] 点击"修改"按钮，确认可以修改物流信息

### 3. 前端用户功能测试

#### 3.1 我的奖品页面测试
- [ ] 访问抽奖活动页面
- [ ] 点击"我的奖品"
- [ ] 确认已发货的奖品显示"查物流"按钮
- [ ] 确认未发货的奖品不显示物流按钮

#### 3.2 物流查询页面测试
- [ ] 点击"查物流"按钮
- [ ] 确认跳转到物流查询页面
- [ ] 确认显示奖品信息
- [ ] 确认显示快递公司和快递单号
- [ ] 确认显示发货时间
- [ ] 确认显示物流轨迹（如果有）
- [ ] 测试下拉刷新功能

### 4. API接口测试

#### 4.1 后端接口测试
- [ ] `POST /Choujiang/sendExpress` - 发货接口
  - 参数：recordid, express_com, express_no
  - 返回：status, msg
- [ ] `POST /Choujiang/getExpress` - 物流查询接口
  - 参数：recordid
  - 返回：status, data, record

#### 4.2 前端接口测试
- [ ] `GET /ApiChoujiang/getExpress` - 前端物流查询
  - 参数：recordid
  - 返回：status, data, record

### 5. 权限和安全测试

#### 5.1 权限控制测试
- [ ] 确认只有管理员可以进行发货操作
- [ ] 确认用户只能查询自己的奖品物流
- [ ] 确认跨用户访问被正确拦截

#### 5.2 数据验证测试
- [ ] 测试空参数提交
- [ ] 测试无效记录ID
- [ ] 测试未中奖记录发货（应该被拒绝）

### 6. 兼容性测试

#### 6.1 浏览器兼容性
- [ ] Chrome浏览器测试
- [ ] Safari浏览器测试
- [ ] 微信内置浏览器测试

#### 6.2 移动端测试
- [ ] 微信小程序测试
- [ ] H5页面测试
- [ ] 响应式布局测试

### 7. 性能测试

#### 7.1 加载性能
- [ ] 物流查询页面加载速度
- [ ] 大量记录时的页面响应速度
- [ ] 物流接口响应时间

#### 7.2 并发测试
- [ ] 多用户同时查询物流
- [ ] 管理员批量发货操作

### 8. 错误处理测试

#### 8.1 网络异常测试
- [ ] 网络断开时的错误提示
- [ ] 接口超时的处理
- [ ] 物流接口异常的处理

#### 8.2 数据异常测试
- [ ] 物流信息为空的处理
- [ ] 快递公司不支持的处理
- [ ] 快递单号格式错误的处理

## 测试环境要求

### 1. 服务器环境
- PHP 7.0+
- MySQL 5.7+
- 支持物流查询接口

### 2. 前端环境
- uni-app开发环境
- 微信开发者工具（小程序测试）

### 3. 配置要求
- 快递公司配置正确
- 微信消息模板配置（可选）
- 物流查询接口配置

## 常见问题排查

### 1. 编译错误
- 检查pages.json配置是否正确
- 检查文件路径是否存在
- 检查语法错误

### 2. 接口调用失败
- 检查接口路径是否正确
- 检查参数传递是否正确
- 检查权限配置

### 3. 物流信息不显示
- 检查物流接口是否正常
- 检查快递公司配置
- 检查快递单号格式

### 4. 页面跳转失败
- 检查路由配置
- 检查页面文件是否存在
- 检查参数传递

## 测试完成标准

- [ ] 所有功能测试项目通过
- [ ] 所有API接口测试通过
- [ ] 权限和安全测试通过
- [ ] 兼容性测试通过
- [ ] 性能测试达标
- [ ] 错误处理测试通过

## 上线前检查

- [ ] 数据库备份完成
- [ ] 代码部署完成
- [ ] 配置文件更新
- [ ] 功能验证通过
- [ ] 用户培训完成
