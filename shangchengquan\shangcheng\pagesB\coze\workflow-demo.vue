<template>
	<view class="workflow-demo">
		<view class="header">
			<view class="title">工作流演示</view>
			<view class="desc">选择工作流并填写参数进行测试</view>
		</view>

		<!-- 工作流选择 -->
		<view class="workflow-select">
			<view class="label">选择工作流：</view>
			<picker :value="selectedWorkflowIndex" :range="workflowList" range-key="name" @change="onWorkflowChange">
				<view class="picker">
					{{selectedWorkflow ? selectedWorkflow.name : '请选择工作流'}}
					<text class="arrow">▼</text>
				</view>
			</picker>
		</view>

		<!-- 参数表单 -->
		<view v-if="selectedWorkflow && paramData.params.length > 0" class="param-form">
			<view class="form-title">参数配置</view>
			
			<!-- 使用动态表单组件 -->
			<dynamic-form 
				:params="paramData.params" 
				:title="''"
				:submit-text="'执行工作流'"
				:show-cancel="false"
				@submit="executeWorkflow"
			/>
		</view>

		<!-- JSON模式参数输入 -->
		<view v-if="selectedWorkflow && paramData.mode === 'json'" class="json-form">
			<view class="form-title">JSON参数</view>
			<textarea 
				class="json-input" 
				placeholder="请输入JSON格式的参数"
				v-model="jsonParams"
			></textarea>
			<button class="execute-btn" @click="executeWorkflowJson">执行工作流</button>
		</view>

		<!-- 执行结果 -->
		<view v-if="result" class="result">
			<view class="result-title">执行结果</view>
			<view class="result-content">
				<text class="result-text">{{result}}</text>
			</view>
		</view>

		<!-- 执行历史 -->
		<view class="history">
			<view class="history-title">执行历史</view>
			<view v-if="historyList.length === 0" class="no-data">暂无执行记录</view>
			<view v-for="(item, index) in historyList" :key="index" class="history-item">
				<view class="history-header">
					<text class="workflow-name">{{item.workflow_id}}</text>
					<text class="time">{{formatTime(item.create_time)}}</text>
				</view>
				<view class="history-params">参数: {{item.parameters}}</view>
				<view class="history-result">结果: {{item.result}}</view>
			</view>
		</view>
	</view>
</template>

<script>
import DynamicForm from './dynamic-form.vue'

export default {
	name: 'WorkflowDemo',
	components: {
		DynamicForm
	},
	data() {
		return {
			workflowList: [],
			selectedWorkflowIndex: -1,
			selectedWorkflow: null,
			paramData: {
				params: [],
				mode: 'custom'
			},
			jsonParams: '{}',
			result: '',
			historyList: [],
			loading: false
		}
	},
	onLoad() {
		this.loadWorkflowList();
		this.loadHistory();
	},
	methods: {
		// 加载工作流列表
		async loadWorkflowList() {
			try {
				const res = await this.$http.get('/apiCoze/getworkflowlist');
				if (res.code === 1) {
					this.workflowList = res.data;
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none'
					});
				}
			} catch (error) {
				uni.showToast({
					title: '加载工作流列表失败',
					icon: 'none'
				});
			}
		},

		// 工作流选择变化
		onWorkflowChange(event) {
			const index = event.detail.value;
			this.selectedWorkflowIndex = index;
			this.selectedWorkflow = this.workflowList[index];
			this.result = '';
			
			if (this.selectedWorkflow) {
				this.loadWorkflowParams();
			}
		},

		// 加载工作流参数配置
		async loadWorkflowParams() {
			if (!this.selectedWorkflow) return;

			try {
				const res = await this.$http.get('/apiCoze/getWorkflowParams', {
					workflow_id: this.selectedWorkflow.workflow_id
				});

				if (res.code === 1) {
					this.paramData = res.data;
					if (this.paramData.mode === 'json') {
						this.jsonParams = JSON.stringify(this.paramData.params, null, 2);
					}
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none'
					});
				}
			} catch (error) {
				uni.showToast({
					title: '加载参数配置失败',
					icon: 'none'
				});
			}
		},

		// 执行工作流（自定义参数模式）
		async executeWorkflow(formData) {
			if (!this.selectedWorkflow) {
				uni.showToast({
					title: '请先选择工作流',
					icon: 'none'
				});
				return;
			}

			this.loading = true;
			uni.showLoading({
				title: '执行中...'
			});

			try {
				const res = await this.$http.post('/apiCoze/runWorkflowWithParams', {
					workflow_id: this.selectedWorkflow.workflow_id,
					params: formData,
					is_async: false
				});

				uni.hideLoading();
				this.loading = false;

				if (res.code === 1) {
					this.result = JSON.stringify(res.data, null, 2);
					uni.showToast({
						title: '执行成功',
						icon: 'success'
					});
					this.loadHistory(); // 刷新历史记录
				} else {
					this.result = '执行失败: ' + res.msg;
					uni.showToast({
						title: res.msg,
						icon: 'none'
					});
				}
			} catch (error) {
				uni.hideLoading();
				this.loading = false;
				this.result = '执行失败: ' + error.message;
				uni.showToast({
					title: '执行失败',
					icon: 'none'
				});
			}
		},

		// 执行工作流（JSON模式）
		async executeWorkflowJson() {
			if (!this.selectedWorkflow) {
				uni.showToast({
					title: '请先选择工作流',
					icon: 'none'
				});
				return;
			}

			let params;
			try {
				params = JSON.parse(this.jsonParams);
			} catch (error) {
				uni.showToast({
					title: 'JSON格式错误',
					icon: 'none'
				});
				return;
			}

			this.loading = true;
			uni.showLoading({
				title: '执行中...'
			});

			try {
				const res = await this.$http.post('/apiCoze/runworkflow', {
					workflow_id: this.selectedWorkflow.workflow_id,
					parameters: this.jsonParams,
					is_async: false
				});

				uni.hideLoading();
				this.loading = false;

				if (res.code === 1) {
					this.result = JSON.stringify(res.data, null, 2);
					uni.showToast({
						title: '执行成功',
						icon: 'success'
					});
					this.loadHistory();
				} else {
					this.result = '执行失败: ' + res.msg;
					uni.showToast({
						title: res.msg,
						icon: 'none'
					});
				}
			} catch (error) {
				uni.hideLoading();
				this.loading = false;
				this.result = '执行失败: ' + error.message;
				uni.showToast({
					title: '执行失败',
					icon: 'none'
				});
			}
		},

		// 加载执行历史
		async loadHistory() {
			try {
				const res = await this.$http.get('/apiCoze/getWorkflowLogs', {
					page: 1,
					limit: 10
				});

				if (res.code === 1) {
					this.historyList = res.data.list;
				}
			} catch (error) {
				console.error('加载历史记录失败:', error);
			}
		},

		// 格式化时间
		formatTime(timestamp) {
			const date = new Date(timestamp * 1000);
			return date.toLocaleString();
		}
	}
}
</script>

<style scoped>
.workflow-demo {
	padding: 20rpx;
	background-color: #f8f9fa;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 30rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.desc {
	font-size: 28rpx;
	color: #666;
}

.workflow-select {
	background: #fff;
	padding: 30rpx;
	border-radius: 12rpx;
	margin-bottom: 30rpx;
}

.label {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 20rpx;
}

.picker {
	padding: 20rpx;
	border: 2rpx solid #e1e8ed;
	border-radius: 8rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	background: #fff;
}

.arrow {
	color: #999;
}

.param-form, .json-form {
	background: #fff;
	padding: 30rpx;
	border-radius: 12rpx;
	margin-bottom: 30rpx;
}

.form-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
}

.json-input {
	width: 100%;
	height: 200rpx;
	padding: 20rpx;
	border: 2rpx solid #e1e8ed;
	border-radius: 8rpx;
	font-size: 24rpx;
	margin-bottom: 30rpx;
	box-sizing: border-box;
}

.execute-btn {
	width: 100%;
	padding: 25rpx;
	background-color: #007aff;
	color: #fff;
	border: none;
	border-radius: 8rpx;
	font-size: 32rpx;
}

.result {
	background: #fff;
	padding: 30rpx;
	border-radius: 12rpx;
	margin-bottom: 30rpx;
}

.result-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.result-content {
	background: #f8f9fa;
	padding: 20rpx;
	border-radius: 8rpx;
	border-left: 4rpx solid #007aff;
}

.result-text {
	font-size: 24rpx;
	color: #333;
	line-height: 1.6;
	word-break: break-all;
}

.history {
	background: #fff;
	padding: 30rpx;
	border-radius: 12rpx;
}

.history-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.no-data {
	text-align: center;
	color: #999;
	padding: 40rpx 0;
}

.history-item {
	padding: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
	margin-bottom: 20rpx;
}

.history-header {
	display: flex;
	justify-content: space-between;
	margin-bottom: 10rpx;
}

.workflow-name {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
}

.time {
	font-size: 24rpx;
	color: #999;
}

.history-params, .history-result {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 5rpx;
	word-break: break-all;
}
</style>
