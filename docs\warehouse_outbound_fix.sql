-- 修复出库单保存失败的SQL脚本
-- 解决 "fields not exists:[amount]" 错误

-- 1. 检查并创建warehouse_voucher表（如果不存在）
CREATE TABLE IF NOT EXISTS `ddwx_warehouse_voucher` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `aid` int(11) NOT NULL DEFAULT '0' COMMENT '商家ID',
  `bid` int(11) NOT NULL DEFAULT '0' COMMENT '分店ID',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '单据类型：1-入库单，2-出库单',
  `docnum` varchar(50) NOT NULL DEFAULT '' COMMENT '单据编号',
  `docdate` date DEFAULT NULL COMMENT '单据日期',
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '单据名称',
  `original_data` text COMMENT '商品详细数据（JSON格式）',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
  `createtime` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updatetime` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `shop_order_id` int(11) NOT NULL DEFAULT '0' COMMENT '关联商城订单ID',
  `cashier_order_id` int(11) NOT NULL DEFAULT '0' COMMENT '关联收银台订单ID',
  `order_id` int(11) NOT NULL DEFAULT '0' COMMENT '关联订单ID',
  PRIMARY KEY (`id`),
  KEY `aid` (`aid`),
  KEY `bid` (`bid`),
  KEY `type` (`type`),
  KEY `docdate` (`docdate`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='仓库单据表';

-- 2. 检查并创建warehouse_product表（如果不存在）
CREATE TABLE IF NOT EXISTS `ddwx_warehouse_product` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `aid` int(11) NOT NULL DEFAULT '0' COMMENT '商家ID',
  `bid` int(11) NOT NULL DEFAULT '0' COMMENT '分店ID',
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '商品名称',
  `sku` varchar(50) NOT NULL DEFAULT '' COMMENT 'SKU编号',
  `pic` varchar(255) NOT NULL DEFAULT '' COMMENT '商品图片',
  `stock` int(11) NOT NULL DEFAULT '0' COMMENT '库存数量',
  `market_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '市场价格',
  `cost_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '成本价格',
  `weight` int(11) NOT NULL DEFAULT '0' COMMENT '重量（克）',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `type` tinyint(1) NOT NULL DEFAULT '2' COMMENT '商品类型：1-商城商品，2-仓库商品，3-组装商品',
  `createtime` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updatetime` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `aid` (`aid`),
  KEY `bid` (`bid`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='仓库商品表';

-- 3. 检查并创建warehouse_stock_log表（库存调整日志表）
CREATE TABLE IF NOT EXISTS `ddwx_warehouse_stock_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `aid` int(11) NOT NULL DEFAULT '0' COMMENT '商家ID',
  `bid` int(11) NOT NULL DEFAULT '0' COMMENT '分店ID',
  `product_id` int(11) NOT NULL DEFAULT '0' COMMENT '商品ID',
  `product_name` varchar(255) NOT NULL DEFAULT '' COMMENT '商品名称',
  `before_stock` int(11) NOT NULL DEFAULT '0' COMMENT '调整前库存',
  `after_stock` int(11) NOT NULL DEFAULT '0' COMMENT '调整后库存',
  `adjust_num` int(11) NOT NULL DEFAULT '0' COMMENT '调整数量',
  `amount` int(11) NOT NULL DEFAULT '0' COMMENT '变动数量（兼容字段）',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '调整类型：1-入库增加，2-出库减少，3-删除单据恢复，4-手动调整',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注说明',
  `voucher_id` int(11) NOT NULL DEFAULT '0' COMMENT '关联单据ID',
  `related_id` int(11) NOT NULL DEFAULT '0' COMMENT '关联ID（兼容字段）',
  `operator` varchar(50) NOT NULL DEFAULT '' COMMENT '操作人员',
  `createtime` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `aid` (`aid`),
  KEY `bid` (`bid`),
  KEY `product_id` (`product_id`),
  KEY `type` (`type`),
  KEY `voucher_id` (`voucher_id`),
  KEY `createtime` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='仓库库存调整日志表';

-- 4. 检查warehouse_voucher表是否缺少order_id字段，如果缺少则添加
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_schema = DATABASE() 
     AND table_name = 'ddwx_warehouse_voucher' 
     AND column_name = 'order_id') > 0,
    'SELECT "order_id字段已存在" as result',
    'ALTER TABLE `ddwx_warehouse_voucher` ADD COLUMN `order_id` int(11) NOT NULL DEFAULT 0 COMMENT "关联订单ID" AFTER `cashier_order_id`'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. 检查warehouse_stock_log表是否缺少amount字段，如果缺少则添加
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_schema = DATABASE() 
     AND table_name = 'ddwx_warehouse_stock_log' 
     AND column_name = 'amount') > 0,
    'SELECT "amount字段已存在" as result',
    'ALTER TABLE `ddwx_warehouse_stock_log` ADD COLUMN `amount` int(11) NOT NULL DEFAULT 0 COMMENT "变动数量（兼容字段）" AFTER `adjust_num`'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 6. 验证表结构
SELECT 'warehouse_voucher表字段检查' as table_check;
DESCRIBE `ddwx_warehouse_voucher`;

SELECT 'warehouse_stock_log表字段检查' as table_check;
DESCRIBE `ddwx_warehouse_stock_log`;

-- 7. 检查是否有测试数据
SELECT COUNT(*) as voucher_count FROM `ddwx_warehouse_voucher` WHERE `type` = 2;
SELECT COUNT(*) as product_count FROM `ddwx_warehouse_product`;
SELECT COUNT(*) as log_count FROM `ddwx_warehouse_stock_log`;

-- 8. 清理可能的错误数据（可选，谨慎执行）
-- DELETE FROM `ddwx_warehouse_voucher` WHERE `type` = 2 AND `original_data` IS NULL;

SELECT '数据库表结构修复完成' as status;
