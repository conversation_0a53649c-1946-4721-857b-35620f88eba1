<template>
<view class="dp-video" :style="{
	backgroundColor: params.bgcolor,
	backgroundImage: params.bg_image ? `url(${params.bg_image})` : 'none',
	backgroundSize: 'cover',
	backgroundPosition: 'center',
	margin: (params.margin_y*2.2)+'rpx '+(params.margin_x*2.2)+'rpx 0',
	padding: (params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx',
	borderRadius: params.bg_border_radius ? params.bg_border_radius + 'rpx' : '0'
}">
	<view class="poster-container" v-if="params.pic && !isPlaying"
		:style="{
			backgroundImage: `url(${params.pic})`,
			height: videoHeight + 'px',
			borderRadius: params.border_radius ? params.border_radius + 'rpx' : '0'
		}"
		@click="playVideo">
		<view class="play-icon"></view>
	</view>
	<video
		class="dp-video-video"
		:src="params.src"
		:poster="params.pic"
		:controls="true"
		:object-fit="videoObjectFit"
		:style="{
			height: videoHeight + 'px',
			borderRadius: params.border_radius ? params.border_radius + 'rpx' : '0',
			display: params.pic && !isPlaying ? 'none' : 'block'
		}"
		@play="onPlay"
		@pause="onPause"
		@ended="onEnded"
		@loadedmetadata="onLoadedMetadata"
		id="myVideo"
	></video>
</view>
</template>

<script>
export default {
	data() {
		return {
			videoHeight: 180,
			screenWidth: 375,
			isPlaying: false,
			videoObjectFit: 'contain', // 默认使用contain避免黑边
			videoAspectRatio: 16/9 // 默认视频比例
		}
	},
	props: {
		params: {},
		data: {}
	},
	mounted() {
		this.initVideoHeight()
		uni.onWindowResize(() => {
			this.initVideoHeight()
		})
	},
	methods: {
		initVideoHeight() {
			const systemInfo = uni.getSystemInfoSync()
			this.screenWidth = systemInfo.screenWidth

			// 根据视频比例和屏幕宽度计算合适的高度
			this.videoHeight = Math.floor(this.screenWidth / this.videoAspectRatio)

			// 设置最小和最大高度限制
			const minHeight = 150
			const maxHeight = Math.floor(this.screenWidth * 0.8) // 最大不超过屏幕宽度的80%

			if (this.videoHeight < minHeight) {
				this.videoHeight = minHeight
			} else if (this.videoHeight > maxHeight) {
				this.videoHeight = maxHeight
			}
		},
		playVideo() {
			this.isPlaying = true
			// 获取视频上下文
			const videoContext = uni.createVideoContext('myVideo', this)
			// 播放视频
			videoContext.play()
		},
		onPlay() {
			this.isPlaying = true
		},
		onPause() {
			// 暂停时不改变播放状态，以免影响用户体验
		},
		onEnded() {
			this.isPlaying = false
		},
		onLoadedMetadata(e) {
			// 当视频元数据加载完成时，获取视频的真实尺寸
			const detail = e.detail
			if (detail && detail.width && detail.height) {
				// 计算视频的真实宽高比
				this.videoAspectRatio = detail.width / detail.height

				// 根据视频比例判断使用哪种填充方式
				const screenAspectRatio = this.screenWidth / this.videoHeight

				// 如果视频比例与容器比例相近，使用fill填满
				// 如果差异较大，使用contain避免变形
				const aspectRatioDiff = Math.abs(this.videoAspectRatio - screenAspectRatio)

				if (aspectRatioDiff < 0.1) {
					this.videoObjectFit = 'fill' // 比例相近时填满
				} else if (this.videoAspectRatio > screenAspectRatio) {
					this.videoObjectFit = 'contain' // 视频更宽时使用contain
				} else {
					this.videoObjectFit = 'cover' // 视频更高时使用cover
				}

				// 重新计算高度
				this.initVideoHeight()
			}
		}
	}
}
</script>

<style>
.dp-video {
	height: auto;
	position: relative;
	font-size: 0;
	/* border-radius: 8px; */
	overflow: hidden;
	box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
	background-color: #000; /* 添加黑色背景，避免加载时的白色闪烁 */
}

.dp-video-video {
	width: 100%;
	margin: 0px;
	padding: 0px;
	transition: height 0.3s ease;
	/* 移除固定的object-fit，改为动态设置 */
	background-color: #000; /* 视频背景色设为黑色 */
	/* 删除这里的固定圆角，改为动态样式 */
}

.poster-container {
	width: 100%;
	background-size: cover;
	background-position: center;
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #000; /* 封面容器背景色 */
}

.play-icon {
	width: 60px;
	height: 60px;
	background-color: rgba(255, 255, 255, 0.8); /* 改为白色半透明背景，更醒目 */
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	transition: all 0.3s ease; /* 添加过渡效果 */
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3); /* 添加阴影 */
}

.play-icon:hover {
	background-color: rgba(255, 255, 255, 0.9); /* 悬停效果 */
	transform: scale(1.1); /* 悬停时稍微放大 */
}

.play-icon:after {
	content: '';
	width: 0;
	height: 0;
	border-style: solid;
	border-width: 12px 0 12px 20px;
	border-color: transparent transparent transparent #333; /* 改为深色三角形 */
	position: absolute;
	left: 22px;
}
</style>