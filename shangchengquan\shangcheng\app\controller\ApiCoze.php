<?php
/**
 * 鲸犀商城 - 微信公众号小程序商城系统!
 * Copyright © 2020 鲸犀网络科技有限公司 保留所有权利
 * =========================================================
 * 版本：供应链版本
 * 授权主体：无限制版本
 * 授权域名：@@@
     
 * ----------------------------------------------
 * 您只能在商业授权范围内使用，不可二次转售、分发、分享、传播
 * 任何企业和个人不得对代码以任何目的任何形式的再发布
 * =========================================================
 */

namespace app\controller;
use think\facade\Db;

class ApiCoze extends ApiCommon
{
    //获取机器人列表
    public function getbotlist(){
        $config = Db::name('coze_config')->where('aid',aid)->where('status',1)->find();
        if(!$config){
            return json(['code'=>0,'msg'=>'扣子API未配置或未启用']);
        }
        
        $result = \app\common\Coze::getBots(aid, $this->mid);
        return json($result);
    }
    
    //获取机器人详情
    public function getbotinfo(){
        $botId = input('param.bot_id');
        if(!$botId){
            return json(['code'=>0,'msg'=>'机器人ID不能为空']);
        }
        
        $result = \app\common\Coze::getBotDetail(aid, $botId, $this->mid);
        return json($result);
    }
    
    //发送聊天消息
    public function chat(){
        $botId = input('param.bot_id');
        $message = input('param.message');
        $conversationId = input('param.conversation_id', '');
        
        if(!$botId){
            return json(['code'=>0,'msg'=>'机器人ID不能为空']);
        }
        
        if(!$message){
            return json(['code'=>0,'msg'=>'消息内容不能为空']);
        }
        
        $result = \app\common\Coze::chat(aid, $botId, $this->mid, $message, $conversationId);
        return json($result);
    }
    
    //获取对话历史
    public function getconversations(){
        $pagenum = input('param.pagenum/d', 1);
        $pagesize = input('param.pagesize/d', 20);
        
        $where = [];
        $where[] = ['aid','=',aid];
        $where[] = ['user_id','=',$this->mid];
        
        $count = Db::name('coze_conversation')->where($where)->count();
        $data = Db::name('coze_conversation')->where($where)
            ->page($pagenum, $pagesize)
            ->order('update_time desc')
            ->select()->toArray();
            
        foreach($data as $k=>$v){
            $data[$k]['create_time_text'] = date('Y-m-d H:i:s', $v['create_time']);
            $data[$k]['update_time_text'] = date('Y-m-d H:i:s', $v['update_time']);
        }
        
        return json(['code'=>1,'msg'=>'获取成功','data'=>$data,'count'=>$count]);
    }
    
    //获取对话消息
    public function getmessages(){
        $conversationId = input('param.conversation_id');
        $pagenum = input('param.pagenum/d', 1);
        $pagesize = input('param.pagesize/d', 50);
        
        if(!$conversationId){
            return json(['code'=>0,'msg'=>'对话ID不能为空']);
        }
        
        $where = [];
        $where[] = ['aid','=',aid];
        $where[] = ['conversation_id','=',$conversationId];
        $where[] = ['user_id','=',$this->mid];
        
        $count = Db::name('coze_message')->where($where)->count();
        $data = Db::name('coze_message')->where($where)
            ->page($pagenum, $pagesize)
            ->order('create_time asc')
            ->select()->toArray();
            
        foreach($data as $k=>$v){
            $data[$k]['create_time_text'] = date('Y-m-d H:i:s', $v['create_time']);
        }
        
        return json(['code'=>1,'msg'=>'获取成功','data'=>$data,'count'=>$count]);
    }
    
    //删除对话
    public function deleteconversation(){
        $conversationId = input('param.conversation_id');
        
        if(!$conversationId){
            return json(['code'=>0,'msg'=>'对话ID不能为空']);
        }
        
        // 检查对话是否属于当前用户
        $conversation = Db::name('coze_conversation')
            ->where('aid',aid)
            ->where('conversation_id',$conversationId)
            ->where('user_id',$this->mid)
            ->find();
            
        if(!$conversation){
            return json(['code'=>0,'msg'=>'对话不存在或无权限']);
        }
        
        // 删除对话和相关消息
        Db::name('coze_conversation')
            ->where('aid',aid)
            ->where('conversation_id',$conversationId)
            ->where('user_id',$this->mid)
            ->delete();
            
        Db::name('coze_message')
            ->where('aid',aid)
            ->where('conversation_id',$conversationId)
            ->where('user_id',$this->mid)
            ->delete();
        
        return json(['code'=>1,'msg'=>'删除成功']);
    }
    
    //上传文件
    public function uploadfile(){
        $file = request()->file('file');
        if(!$file){
            return json(['code'=>0,'msg'=>'请选择要上传的文件']);
        }

        // 保存文件到临时目录
        $tempPath = sys_get_temp_dir() . '/' . uniqid() . '_' . $file->getOriginalName();
        $file->move(dirname($tempPath), basename($tempPath));

        $result = \app\common\Coze::uploadFile(aid, $tempPath, $this->mid);

        // 清理临时文件
        if(file_exists($tempPath)){
            unlink($tempPath);
        }

        return json($result);
    }
    
    //获取文件信息
    public function getfileinfo(){
        $fileId = input('param.file_id');

        if(!$fileId){
            return json(['code'=>0,'msg'=>'文件ID不能为空']);
        }

        // 暂时返回成功，实际可以调用Coze API获取文件信息
        return json(['code'=>1,'msg'=>'获取成功','data'=>['file_id'=>$fileId]]);
    }
    
    //获取API使用统计
    public function getstats(){
        $startDate = input('param.start_date', date('Y-m-d', strtotime('-7 days')));
        $endDate = input('param.end_date', date('Y-m-d'));
        
        $where = [];
        $where[] = ['aid','=',aid];
        $where[] = ['user_id','=',$this->mid];
        $where[] = ['request_time','>=', $startDate . ' 00:00:00'];
        $where[] = ['request_time','<=', $endDate . ' 23:59:59'];
        
        $totalRequests = Db::name('coze_api_log')->where($where)->count();
        $successRequests = Db::name('coze_api_log')->where($where)->where('status',1)->count();
        $failedRequests = $totalRequests - $successRequests;
        
        $stats = [
            'total_requests' => $totalRequests,
            'success_requests' => $successRequests,
            'failed_requests' => $failedRequests,
            'success_rate' => $totalRequests > 0 ? round($successRequests / $totalRequests * 100, 2) : 0
        ];
        
        return json(['code'=>1,'msg'=>'获取成功','data'=>$stats]);
    }

    //获取工作流列表
    public function getWorkflowList(){
        $config = Db::name('coze_config')->where('aid',aid)->where('status',1)->find();
        if(!$config){
            return json(['code'=>0,'msg'=>'扣子API未配置或未启用']);
        }

        $list = Db::name('coze_workflow')
            ->where('aid',aid)
            ->where('status',1)
            ->field('id,name,workflow_id,description')
            ->order('id desc')
            ->select();

        return json(['code'=>1,'msg'=>'获取成功','data'=>$list]);
    }

    //运行工作流
    public function runWorkflow(){
        $workflowId = input('param.workflow_id');
        $parameters = input('param.parameters', []);
        $isAsync = (bool)input('param.is_async', false);

        if(!$workflowId){
            return json(['code'=>0,'msg'=>'工作流ID不能为空']);
        }

        // 检查工作流是否存在且启用
        $workflow = Db::name('coze_workflow')
            ->where('aid',aid)
            ->where('workflow_id',$workflowId)
            ->where('status',1)
            ->find();

        if(!$workflow){
            return json(['code'=>0,'msg'=>'工作流不存在或未启用']);
        }

        // 调用工作流（支持异步）
        $result = \app\common\Coze::runWorkflow(aid, $workflowId, $parameters, $isAsync, $this->mid);

        // 记录工作流执行记录
        $this->logWorkflowExecution($workflowId, $parameters, $result);

        return json($result);
    }

    //查询工作流异步执行结果
    public function queryWorkflowResult(){
        $workflowId = input('param.workflow_id');
        $executeId = input('param.execute_id');

        if(!$workflowId || !$executeId){
            return json(['code'=>0,'msg'=>'工作流ID和执行ID不能为空']);
        }

        // 检查工作流是否存在且启用
        $workflow = Db::name('coze_workflow')
            ->where('aid',aid)
            ->where('workflow_id',$workflowId)
            ->where('status',1)
            ->find();

        if(!$workflow){
            return json(['code'=>0,'msg'=>'工作流不存在或未启用']);
        }

        // 查询执行结果
        $result = \app\common\Coze::queryWorkflowResult(aid, $workflowId, $executeId, $this->mid);

        return json($result);
    }

    //运行工作流（带文件上传）
    public function runWorkflowWithFiles(){
        $workflowId = input('param.workflow_id');
        $parameters = input('param.parameters', []);
        $isAsync = (bool)input('param.is_async', false);
        $files = $_FILES;

        if(!$workflowId){
            return json(['code'=>0,'msg'=>'工作流ID不能为空']);
        }

        // 检查工作流是否存在且启用
        $workflow = Db::name('coze_workflow')
            ->where('aid',aid)
            ->where('workflow_id',$workflowId)
            ->where('status',1)
            ->find();

        if(!$workflow){
            return json(['code'=>0,'msg'=>'工作流不存在或未启用']);
        }

        // 处理上传的文件
        $fileParams = [];
        foreach($files as $paramName => $file){
            if($file['error'] == 0){
                // 移动文件到临时目录
                $tempPath = sys_get_temp_dir() . '/' . uniqid() . '_' . $file['name'];
                if(move_uploaded_file($file['tmp_name'], $tempPath)){
                    $fileParams[$paramName] = $tempPath;
                }
            }
        }

        // 调用工作流（带文件，支持异步）
        $result = \app\common\Coze::runWorkflowWithFiles(aid, $workflowId, $parameters, $fileParams, $isAsync, $this->mid);

        // 清理临时文件
        foreach($fileParams as $tempPath){
            if(file_exists($tempPath)){
                unlink($tempPath);
            }
        }

        // 记录工作流执行记录
        $this->logWorkflowExecution($workflowId, array_merge($parameters, ['files' => array_keys($files)]), $result);

        return json($result);
    }

    //记录工作流执行记录
    private function logWorkflowExecution($workflowId, $parameters, $result){
        try {
            $logData = [
                'aid' => aid,
                'mid' => $this->mid,
                'workflow_id' => $workflowId,
                'parameters' => json_encode($parameters),
                'result' => json_encode($result),
                'status' => $result['code'] == 1 ? 1 : 0,
                'create_time' => time()
            ];

            Db::name('coze_workflow_log')->insert($logData);
        } catch (\Exception $e) {
            // 记录日志失败不影响主流程
            error_log('工作流日志记录失败: ' . $e->getMessage());
        }
    }

    //获取工作流执行记录
    public function getWorkflowLogs(){
        $page = input('param.page', 1, 'intval');
        $limit = input('param.limit', 20, 'intval');
        $workflowId = input('param.workflow_id', '');

        $where = [];
        $where[] = ['aid','=',aid];
        $where[] = ['mid','=',$this->mid];

        if(!empty($workflowId)){
            $where[] = ['workflow_id','=',$workflowId];
        }

        $list = Db::name('coze_workflow_log')
            ->where($where)
            ->field('id,workflow_id,parameters,result,status,create_time')
            ->order('id desc')
            ->page($page, $limit)
            ->select();

        $total = Db::name('coze_workflow_log')->where($where)->count();

        return json([
            'code'=>1,
            'msg'=>'获取成功',
            'data'=>[
                'list'=>$list,
                'total'=>$total,
                'page'=>$page,
                'limit'=>$limit
            ]
        ]);
    }

    /**
     * 获取工作流参数配置（支持动态参数）
     */
    public function getWorkflowParams()
    {
        $workflowId = input('param.workflow_id', '');
        if (empty($workflowId)) {
            return json(['code' => 0, 'msg' => '工作流ID不能为空']);
        }

        try {
            // 获取工作流信息
            $workflow = Db::name('coze_workflow')
                ->where('aid', aid)
                ->where('workflow_id', $workflowId)
                ->where('status', 1)
                ->find();

            if (!$workflow) {
                return json(['code' => 0, 'msg' => '工作流不存在或已禁用']);
            }

            // 如果是自定义字段模式，获取参数配置
            if ($workflow['param_config_mode'] == 1) {
                $params = Db::name('coze_workflow_params')
                    ->where('aid', aid)
                    ->where('workflow_id', $workflowId)
                    ->where('status', 1)
                    ->order('sort_order asc, id asc')
                    ->select()
                    ->toArray();

                // 处理参数选项
                foreach ($params as &$param) {
                    if ($param['param_options']) {
                        $param['param_options'] = json_decode($param['param_options'], true);
                    }
                }

                return json([
                    'code' => 1,
                    'msg' => '获取成功',
                    'data' => [
                        'workflow' => $workflow,
                        'params' => $params,
                        'mode' => 'custom'
                    ]
                ]);
            } else {
                // JSON模式，返回默认参数
                $defaultParams = $workflow['default_params'] ? json_decode($workflow['default_params'], true) : [];
                return json([
                    'code' => 1,
                    'msg' => '获取成功',
                    'data' => [
                        'workflow' => $workflow,
                        'params' => $defaultParams,
                        'mode' => 'json'
                    ]
                ]);
            }
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 执行工作流（支持动态参数验证）
     */
    public function runWorkflowWithParams()
    {
        $workflowId = input('param.workflow_id', '');
        $params = input('param.params', []);
        $isAsync = input('param.is_async', false);

        if (empty($workflowId)) {
            return json(['code' => 0, 'msg' => '工作流ID不能为空']);
        }

        try {
            // 获取工作流信息
            $workflow = Db::name('coze_workflow')
                ->where('aid', aid)
                ->where('workflow_id', $workflowId)
                ->where('status', 1)
                ->find();

            if (!$workflow) {
                return json(['code' => 0, 'msg' => '工作流不存在或已禁用']);
            }

            // 如果是自定义字段模式，验证参数
            if ($workflow['param_config_mode'] == 1) {
                $paramConfigs = Db::name('coze_workflow_params')
                    ->where('aid', aid)
                    ->where('workflow_id', $workflowId)
                    ->where('status', 1)
                    ->select()
                    ->toArray();

                // 验证必填参数
                foreach ($paramConfigs as $config) {
                    if ($config['is_required'] && empty($params[$config['param_key']])) {
                        return json(['code' => 0, 'msg' => $config['param_name'] . '不能为空']);
                    }
                }

                // 设置默认值
                foreach ($paramConfigs as $config) {
                    if (!isset($params[$config['param_key']]) && !empty($config['default_value'])) {
                        $params[$config['param_key']] = $config['default_value'];
                    }
                }
            }

            // 调用工作流
            $result = \app\common\Coze::runWorkflow(aid, $workflowId, $params, $isAsync);

            return json($result);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '执行失败: ' . $e->getMessage()]);
        }
    }
}
