<view class="container"><block wx:if="{{isload}}"><block><block wx:if="{{$root.m0}}"><view data-event-opts="{{[['tap',[['getweixinaddress',['$event']]]]]}}" class="addfromwx" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image>获取微信收货地址<view class="flex1"></view><image style="width:30rpx;height:30rpx;" src="/static/img/arrowright.png"></image></view></block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]]]}}" bindsubmit="__e"><view class="form"><view class="form-item"><text class="label">姓 名</text><input class="input" type="text" placeholder="请输入姓名" placeholder-style="font-size:28rpx;color:#BBBBBB" name="name" value="{{name}}"/></view><block wx:if="{{showCompany}}"><view class="form-item"><text class="label">公 司</text><input class="input" type="text" placeholder="请输入公司或单位名称" placeholder-style="font-size:28rpx;color:#BBBBBB" name="company" value="{{company}}"/></view></block><view class="form-item"><text class="label">手机号</text><input class="input" type="number" placeholder="请输入手机号" placeholder-style="font-size:28rpx;color:#BBBBBB" name="tel" value="{{tel}}"/></view><block wx:if="{{type==1}}"><view class="form-item"><text class="label flex0">选择位置</text><text data-event-opts="{{[['tap',[['selectzuobiao',['$event']]]]]}}" class="flex1" style="{{'text-align:right;'+(area?'':'color:#BBBBBB')}}" bindtap="__e">{{area?area:'请选择您的位置'}}</text></view></block><block wx:else><view class="form-item"><text class="label flex1">所在地区</text><uni-data-picker vue-id="13129bc5-1" localdata="{{items}}" border="{{false}}" placeholder="{{regiondata||'请选择省市区'}}" data-event-opts="{{[['^change',[['regionchange']]]]}}" bind:change="__e" bind:__l="__l"></uni-data-picker></view></block><view class="form-item"><text class="label">{{$root.m1}}</text><input class="input" type="text" placeholder="{{'请输入'+$root.m2}}" placeholder-style="font-size:28rpx;color:#BBBBBB" name="address" value="{{address}}"/></view><block wx:if="{{type!=1}}"><view class="item flex-y-center"><view class="f2 flex-y-center flex1"><input style="width:85%;font-size:24rpx;margin:20rpx 0;height:100rpx;padding:4rpx 10rpx;" id="addressxx" placeholder="粘贴地址信息，可自动识别并填写，如：张三，188********，广东省 东莞市 xx区 xx街道 xxxx" placeholder-style="font-size:24rpx;color:#BBBBBB" data-event-opts="{{[['input',[['setaddressxx',['$event']]]]]}}" bindinput="__e"/><view data-event-opts="{{[['tap',[['shibie',['$event']]]]]}}" style="width:15%;text-align:center;color:#999;" bindtap="__e">识别</view></view></view></block></view><button class="savebtn" style="{{('background:linear-gradient(90deg,'+$root.m3+' 0%,rgba('+$root.m4+',0.8) 100%)')}}" form-type="submit">保 存</button></form></block></block><block wx:if="{{loading}}"><loading vue-id="13129bc5-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="13129bc5-3" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="13129bc5-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>