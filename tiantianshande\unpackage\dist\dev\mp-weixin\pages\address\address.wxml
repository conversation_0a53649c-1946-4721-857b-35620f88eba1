<view class="container"><block wx:if="{{isload}}"><block><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="/static/img/search_ico.png"></image><input placeholder="输入姓名/手机号搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]],['input',[['searchChange',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e" bindinput="__e"/></view></view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="content" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['setdefault',['$event']]]]]}}" catchtap="__e"><view class="f1"><text class="t1">{{item.$orig.name}}</text><text class="t2">{{item.$orig.tel}}</text><block wx:if="{{item.$orig.company}}"><text class="t2">{{item.$orig.company}}</text></block><text class="flex1"></text><image class="t3" src="/static/img/edit.png" data-url="{{'/pages/address/addressadd?id='+item.$orig.id+'&type='+(item.$orig.latitude>0?'1':'0')}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"></image></view><view class="f2">{{item.$orig.area+" "+item.$orig.address}}</view><view class="f3"><view class="flex-y-center"><view class="radio" style="{{(item.$orig.isdefault?'border:0;background:'+item.m0:'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view><view class="mrtxt">{{item.$orig.isdefault?'默认地址':'设为默认'}}</view></view><view class="flex1"></view><view class="del" style="{{'color:'+(item.m1)+';'}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['del',['$event']]]]]}}" catchtap="__e">删除</view></view></view></block><block wx:if="{{nodata}}"><nodata vue-id="91826bd4-1" bind:__l="__l"></nodata></block><view style="height:140rpx;"></view><view class="{{['btn-add',menuindex>-1?'tabbarbot':'notabbarbot3']}}" style="{{('background:linear-gradient(90deg,'+$root.m2+' 0%,rgba('+$root.m3+',0.8) 100%)')}}" data-url="{{'/pages/address/addressadd?type='+type}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image style="width:28rpx;height:28rpx;margin-right:6rpx;" src="/static/img/add.png"></image>添加地址</view></block></block><block wx:if="{{loading}}"><loading vue-id="91826bd4-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="91826bd4-3" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="91826bd4-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>