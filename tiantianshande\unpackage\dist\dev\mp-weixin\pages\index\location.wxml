<view class="location-page"><view class="search-section"><view class="search-box"><view class="search-input-box"><image class="search-icon" src="{{pre_url+'/static/img/search.png'}}" mode="aspectFit"></image><input class="search-input" type="text" placeholder="搜索地点、小区、写字楼" data-event-opts="{{[['input',[['__set_model',['','keyword','$event',[]]],['handleSearch',['$event']]]]]}}" value="{{keyword}}" bindinput="__e"/><block wx:if="{{keyword}}"><view data-event-opts="{{[['tap',[['clearSearch',['$event']]]]]}}" class="clear-btn" bindtap="__e"><image class="clear-icon" src="{{pre_url+'/static/img/close.png'}}" mode="aspectFit"></image></view></block></view></view><view data-event-opts="{{[['tap',[['getCurrentLocation',['$event']]]]]}}" class="{{['get-location-btn',(isLocating)?'loading':'']}}" style="{{'background:'+('linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')+';'}}" bindtap="__e"><image class="{{['btn-icon',(isLocating)?'rotating':'']}}" src="{{pre_url+'/static/img/location.png'}}" mode="aspectFit"></image><text>{{isLocating?'正在获取位置...':'重新定位'}}</text></view></view><view data-event-opts="{{[['tap',[['useCurrentLocation',['$event']]]]]}}" class="current-location" bindtap="__e"><view class="location-left"><view class="location-icon-wrap"><image class="location-icon" src="{{pre_url+'/static/img/location.png'}}" mode="aspectFit"></image></view><view class="location-info"><text class="title">当前位置</text><text class="address">{{currentAddress||'定位中...'}}</text></view></view><view class="{{['use-btn',(!locationInfo)?'disabled':'']}}" style="{{'background:'+('linear-gradient(90deg,'+$root.m2+' 0%,rgba('+$root.m3+',0.8) 100%)')+';'}}"><text>{{locationInfo?'使用':'定位中'}}</text><image class="arrow-icon" src="{{pre_url+'/static/img/arrow-right.png'}}" mode="aspectFit"></image></view></view><block wx:if="{{$root.g0}}"><view class="history-section"><view class="section-header"><text class="title">搜索历史</text><view data-event-opts="{{[['tap',[['clearHistory',['$event']]]]]}}" class="clear-history" bindtap="__e"><image class="delete-icon" src="{{pre_url+'/static/img/delete.png'}}" mode="aspectFit"></image><text>清除</text></view></view><view class="history-list"><block wx:for="{{searchHistory}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['useHistoryItem',['$0'],[[['searchHistory','',index]]]]]]]}}" class="history-item" bindtap="__e"><image class="time-icon" src="{{pre_url+'/static/img/time.png'}}" mode="aspectFit"></image><text class="history-text">{{item.name}}</text></view></block></view></view></block><block wx:if="{{$root.g1>0}}"><scroll-view class="location-list" scroll-y="{{true}}" data-event-opts="{{[['scrolltolower',[['loadMore',['$event']]]]]}}" bindscrolltolower="__e"><block wx:for="{{searchResults}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="location-item" hover-class="item-hover" data-event-opts="{{[['tap',[['selectLocation',['$0'],[[['searchResults','',index]]]]]]]}}" bindtap="__e"><view class="location-detail"><view class="name-wrap"><text class="name">{{item.name}}</text><block wx:if="{{item.type}}"><text class="tag">{{item.type}}</text></block></view><text class="address">{{item.address}}</text></view><view class="right-area"><text class="distance">{{item.distance}}</text><image class="arrow-icon" src="{{pre_url+'/static/img/arrow-right.png'}}" mode="aspectFit"></image></view></view></block><block wx:if="{{hasMore}}"><view class="load-more"><block wx:if="{{loading}}"><view class="loading-dot"><view class="dot"></view><view class="dot"></view><view class="dot"></view></view></block><text>{{loading?'加载中...':'上拉加载更多'}}</text></view></block></scroll-view></block><block wx:else><block wx:if="{{keyword&&!loading}}"><view class="no-result"><image class="no-result-icon" src="{{pre_url+'/static/img/no-result.png'}}" mode="aspectFit"></image><text class="main-text">未找到相关地址</text><text class="sub-text">换个关键词试试</text></view></block></block><block wx:if="{{loading}}"><view class="loading-overlay"><view class="loading-content"><view class="loading-spinner"><block wx:for="{{12}}" wx:for-item="i" wx:for-index="__i0__" wx:key="*this"><view class="spinner-item"></view></block></view><text>加载中...</text></view></view></block></view>