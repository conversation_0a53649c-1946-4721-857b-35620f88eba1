-- 修复warehouse_stock_log表的SQL脚本
-- 解决出库单保存时的字段错误

-- 1. 删除可能存在的错误表结构
DROP TABLE IF EXISTS `ddwx_warehouse_stock_log`;

-- 2. 重新创建warehouse_stock_log表
CREATE TABLE `ddwx_warehouse_stock_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `aid` int(11) NOT NULL DEFAULT '0',
  `bid` int(11) NOT NULL DEFAULT '0',
  `product_id` int(11) NOT NULL DEFAULT '0',
  `product_name` varchar(255) NOT NULL DEFAULT '',
  `before_stock` int(11) NOT NULL DEFAULT '0',
  `after_stock` int(11) NOT NULL DEFAULT '0',
  `adjust_num` int(11) NOT NULL DEFAULT '0',
  `type` tinyint(1) NOT NULL DEFAULT '1',
  `remark` varchar(255) NOT NULL DEFAULT '',
  `voucher_id` int(11) NOT NULL DEFAULT '0',
  `operator` varchar(50) NOT NULL DEFAULT '',
  `createtime` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`),
  KEY `voucher_id` (`voucher_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='仓库库存调整日志表';

-- 3. 检查表结构
DESCRIBE `ddwx_warehouse_stock_log`;

SELECT '库存日志表修复完成' as message;
