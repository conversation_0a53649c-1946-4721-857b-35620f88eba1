<?php
// 查询工作流执行结果
// 直接使用CURL查询，不依赖ThinkPHP框架

// 配置信息
$token = 'pat_DPKdPgz27jXNZooKwHy2OQ6jMp6tY9Ga0nHdbjx9YRbbA2AKYbWH0gxKZ5Ta5GzR';
$base_url = 'https://api.coze.cn';

// 查询参数
$workflowId = '7519352650686890038';
$executeId = '7532932732713074726';

echo "=== 查询工作流执行结果 ===\n";
echo "工作流ID: {$workflowId}\n";
echo "执行ID: {$executeId}\n";
echo "查询时间: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // 构建查询URL
    $url = $base_url . '/v1/workflows/' . $workflowId . '/run_histories/' . $executeId;

    $headers = [
        'Authorization: Bearer ' . $token,
        'Content-Type: application/json'
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    echo "=== 查询结果 ===\n";
    echo "HTTP状态码: {$http_code}\n";
    echo "请求URL: {$url}\n";

    if (!empty($error)) {
        echo "CURL错误: {$error}\n";
    } else {
        echo "\n=== 响应数据 ===\n";
        $responseData = json_decode($response, true);
        if ($responseData) {
            echo json_encode($responseData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";

            // 提取关键信息
            if (isset($responseData['data'])) {
                echo "\n=== 关键信息 ===\n";
                $data = $responseData['data'];
                if (isset($data['status'])) {
                    echo "执行状态: " . $data['status'] . "\n";
                }
                if (isset($data['output'])) {
                    echo "输出结果: " . json_encode($data['output'], JSON_UNESCAPED_UNICODE) . "\n";
                }
                if (isset($data['error'])) {
                    echo "错误信息: " . json_encode($data['error'], JSON_UNESCAPED_UNICODE) . "\n";
                }
            }
        } else {
            echo "原始响应: " . $response . "\n";
        }
    }

} catch (\Exception $e) {
    echo "查询出错: " . $e->getMessage() . "\n";
}

echo "\n=== 数据库记录查询 ===\n";
echo "注意：需要通过Web界面查询数据库记录\n";
echo "可以访问: http://localhost/?s=/Coze/getWorkflowHistory 查看执行历史\n";
?>
