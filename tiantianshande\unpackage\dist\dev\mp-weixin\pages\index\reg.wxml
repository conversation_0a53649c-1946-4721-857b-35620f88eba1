<view class="container"><block wx:if="{{isload}}"><block><block wx:if="{{logintype!=4&&logintype!=5}}"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="title">注册账号</view><view class="regform"><block wx:if="{{!show_custom_field}}"><block><view class="form-item"><image class="img" src="/static/img/reg-tel.png"></image><input class="input" type="text" placeholder="请输入手机号" placeholder-style="font-size:30rpx;color:#B2B5BE" name="tel" value="" data-event-opts="{{[['input',[['telinput',['$event']]]]]}}" bindinput="__e"/></view><block wx:if="{{needsms}}"><view class="form-item"><image class="img" src="/static/img/reg-code.png"></image><input class="input" type="text" placeholder="请输入验证码" placeholder-style="font-size:30rpx;color:#B2B5BE" name="smscode" value=""/><view data-event-opts="{{[['tap',[['smscode',['$event']]]]]}}" class="code" style="{{'color:'+($root.m0)+';'}}" bindtap="__e">{{smsdjs||'获取验证码'}}</view></view></block><view class="form-item"><image class="img" src="/static/img/reg-pwd.png"></image><input class="input" type="text" placeholder="6-16位字母数字组合密码" placeholder-style="font-size:30rpx;color:#B2B5BE" name="pwd" value="" password="{{true}}"/></view><view class="form-item"><image class="img" src="/static/img/reg-pwd.png"></image><input class="input" type="text" placeholder="再次输入登录密码" placeholder-style="font-size:30rpx;color:#B2B5BE" name="repwd" value="" password="{{true}}"/></view><block wx:if="{{reg_invite_code&&!parent}}"><view class="form-item"><image class="img" src="/static/img/reg-yqcode.png"></image><input class="input" type="text" placeholder="{{'请输入'+(reg_invite_code_type==0?'邀请人手机号':'邀请码')+(reg_invite_code==2?'(必填)':'(选填)')}}" placeholder-style="font-size:30rpx;color:#B2B5BE" name="yqcode" value=""/></view></block><block wx:if="{{reg_invite_code&&parent}}"><view class="form-item" style="color:#666;"><block wx:if="{{reg_invite_code_type==0}}"><block>邀请人：<image style="width:80rpx;height:80rpx;border-radius:50%;" src="{{parent.headimg}}"></image>{{''+parent.nickname+''}}</block></block><block wx:else><block>{{'邀请码：'+parent.yqcode+''}}</block></block></view></block><block wx:if="{{xlevelarr}}"><view class="form-item" style="color:#666;"><block>{{'注册等级：'+xlevelarr.name+''}}</block></view></block></block></block><block wx:if="{{show_custom_field}}"><block><view class="dp-form-item"><view class="label">手机号<text style="color:red;">*</text></view><input class="input" type="text" placeholder="请输入手机号" placeholder-style="font-size:30rpx;color:#B2B5BE" name="tel" value="" data-event-opts="{{[['input',[['telinput',['$event']]]]]}}" bindinput="__e"/></view><block wx:if="{{needsms}}"><view class="dp-form-item"><text class="label">验证码</text><input class="input" type="text" placeholder="请输入验证码" placeholder-style="font-size:30rpx;color:#B2B5BE" name="smscode" value=""/><view data-event-opts="{{[['tap',[['smscode',['$event']]]]]}}" class="code" style="{{'color:'+($root.m1)+';'}}" bindtap="__e">{{smsdjs||'获取验证码'}}</view></view></block><view class="dp-form-item"><view class="label">密码<text style="color:red;">*</text></view><input class="input" type="text" placeholder="6-16位字母数字组合密码" placeholder-style="font-size:30rpx;color:#B2B5BE" name="pwd" value="" password="{{true}}"/></view><view class="dp-form-item"><view class="label">确认密码</view><input class="input" type="text" placeholder="再次输入登录密码" placeholder-style="font-size:30rpx;color:#B2B5BE" name="repwd" value="" password="{{true}}"/></view><block wx:if="{{reg_invite_code&&!parent}}"><view class="dp-form-item"><text class="label">邀请码</text><input class="input" type="text" placeholder="{{'请输入'+(reg_invite_code_type==0?'邀请人手机号':'邀请码')+(reg_invite_code==2?'(必填)':'(选填)')}}" placeholder-style="font-size:30rpx;color:#B2B5BE" name="yqcode" value=""/></view></block><block wx:if="{{reg_invite_code&&parent}}"><view class="dp-form-item" style="color:#666;"><block wx:if="{{reg_invite_code_type==0}}"><block>邀请人：<image style="width:80rpx;height:80rpx;border-radius:50%;" src="{{parent.headimg}}"></image>{{''+parent.nickname+''}}</block></block><block wx:else><block>{{'邀请码：'+parent.yqcode+''}}</block></block></view></block><block wx:if="{{show_custom_field}}"><view class="custom_field"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><view class="{{['dp-form-item']}}"><view class="label">{{item.$orig.val1}}<block wx:if="{{item.$orig.val3==1}}"><text style="color:red;">*</text></block></view><block wx:if="{{item.$orig.key=='input'}}"><block><block wx:if="{{item.$orig.val5}}"><text style="margin-right:10rpx;">{{item.$orig.val5}}</text></block><input class="input" type="{{item.$orig.val4==1||item.$orig.val4==2?'digit':'text'}}" name="{{'form'+idx}}" placeholder="{{item.$orig.val2}}" placeholder-style="font-size:28rpx" data-formidx="{{'form'+idx}}" data-event-opts="{{[['input',[['setfield',['$event']]]]]}}" value="{{custom_formdata['form'+idx]}}" bindinput="__e"/></block></block><block wx:if="{{item.$orig.key=='textarea'}}"><block><textarea class="textarea" name="{{'form'+idx}}" placeholder="{{item.$orig.val2}}" placeholder-style="font-size:28rpx" data-formidx="{{'form'+idx}}" data-event-opts="{{[['input',[['setfield',['$event']]]]]}}" value="{{custom_formdata['form'+idx]}}" bindinput="__e"></textarea></block></block><block wx:if="{{item.$orig.key=='radio'}}"><block><radio-group class="flex" style="flex-wrap:wrap;" name="{{'form'+idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['change',[['setfield',['$event']]]]]}}" bindchange="__e"><block wx:for="{{item.$orig.val2}}" wx:for-item="item1" wx:for-index="idx1" wx:key="id"><label class="flex-y-center"><radio class="radio" style="transform:scale(0.8);" value="{{item1}}" checked="{{custom_formdata['form'+idx]&&custom_formdata['form'+idx]==item1?true:false}}"></radio>{{item1+''}}</label></block></radio-group></block></block><block wx:if="{{item.$orig.key=='checkbox'}}"><block><checkbox-group class="flex" style="flex-wrap:wrap;" name="{{'form'+idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['change',[['setfield',['$event']]]]]}}" bindchange="__e"><block wx:for="{{item.l0}}" wx:for-item="item1" wx:for-index="idx1" wx:key="id"><label class="flex-y-center"><checkbox class="checkbox" style="transform:scale(0.8);" value="{{item1.$orig}}" checked="{{item1.m2?true:false}}"></checkbox>{{item1.$orig+''}}</label></block></checkbox-group></block></block><block wx:if="{{item.$orig.key=='selector'}}"><block><picker class="picker" mode="selector" name="{{'form'+idx}}" value="{{editorFormdata[idx]}}" range="{{item.$orig.val2}}" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{editorFormdata[idx]||editorFormdata[idx]===0}}"><view>{{''+item.$orig.val2[editorFormdata[idx]]}}</view></block><block wx:else><view style="color:#b2b5be;">请选择</view></block></picker></block></block><block wx:if="{{item.$orig.key=='time'}}"><block><picker class="picker" mode="time" name="{{'form'+idx}}" value="{{custom_formdata['form'+idx]}}" start="{{item.$orig.val2[0]}}" end="{{item.$orig.val2[1]}}" range="{{item.$orig.val2}}" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{editorFormdata[idx]}}"><view>{{editorFormdata[idx]}}</view></block><block wx:else><view style="color:#b2b5be;">请选择</view></block></picker></block></block><block wx:if="{{item.$orig.key=='date'}}"><block><picker class="picker" mode="date" name="{{'form'+idx}}" value="{{custom_formdata['form'+idx]}}" start="{{item.$orig.val2[0]}}" end="{{item.$orig.val2[1]}}" range="{{item.$orig.val2}}" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{editorFormdata[idx]}}"><view>{{editorFormdata[idx]}}</view></block><block wx:else><view style="color:#b2b5be;">请选择</view></block></picker></block></block><block wx:if="{{item.$orig.key=='region'}}"><block><uni-data-picker vue-id="{{'05513098-1-'+idx}}" localdata="{{items}}" popup-title="请选择省市区" placeholder="{{custom_formdata['form'+idx]||'请选择省市区'}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['^change',[['onchange']]]]}}" bind:change="__e" bind:__l="__l"></uni-data-picker><input style="display:none;" type="text" name="{{'form'+idx}}" value="{{regiondata?regiondata:custom_formdata['form'+idx]}}"/></block></block><block wx:if="{{item.$orig.key=='upload'}}"><block><input style="display:none;" type="text" name="{{'form'+idx}}" value="{{editorFormdata[idx]}}"/><view class="flex" style="flex-wrap:wrap;padding-top:20rpx;"><block wx:if="{{editorFormdata[idx]}}"><view class="dp-form-imgbox"><view class="dp-form-imgbox-close" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image class="image" src="/static/img/ico-del.png"></image></view><view class="dp-form-imgbox-img"><image class="image" src="{{editorFormdata[idx]}}" data-url="{{editorFormdata[idx]}}" mode="widthFix" data-idx="{{idx}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:else><view class="dp-form-uploadbtn" style="{{'background:'+('url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx')+';'+('background-size:'+('80rpx 80rpx')+';')+('background-color:'+('#F3F3F3')+';')}}" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['tap',[['editorChooseImage',['$event']]]]]}}" bindtap="__e"></view></block></view></block></block></view></block></view></block><view style="display:none;">{{test}}</view></block></block><block wx:if="{{xystatus==1}}"><view class="xieyi-item"><checkbox-group data-event-opts="{{[['change',[['isagreeChange',['$event']]]]]}}" bindchange="__e"><label class="flex-y-center"><checkbox class="checkbox" value="1" checked="{{isagree}}"></checkbox>阅读并同意</label></checkbox-group><text data-event-opts="{{[['tap',[['showxieyiFun',['$event']]]]]}}" style="{{'color:'+($root.m3)+';'}}" bindtap="__e">{{xyname}}</text><block wx:if="{{xyname2}}"><text data-event-opts="{{[['tap',[['showxieyiFun',['$event']]]]]}}" bindtap="__e">和</text></block><block wx:if="{{xyname2}}"><text data-event-opts="{{[['tap',[['showxieyiFun2',['$event']]]]]}}" style="{{'color:'+($root.m4)+';'}}" bindtap="__e">{{xyname2}}</text></block></view></block><button class="form-btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m5+' 0%,rgba('+$root.m6+',0.8) 100%)')+';'}}" form-type="submit">注册</button></view></form><view class="tologin" data-url="login" data-opentype="redirect" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">已有账号? 前去登录</view><block wx:if="{{logintype_2||logintype_3}}"><block><view class="othertip"><view class="othertip-line"></view><view class="othertip-text"><text class="txt">其他方式登录</text></view><view class="othertip-line"></view></view><view class="othertype"><block wx:if="{{logintype_3}}"><view data-event-opts="{{[['tap',[['weixinlogin',['$event']]]]]}}" class="othertype-item" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/login-'+platformimg+'.png'}}"></image><text class="txt">{{platformname+"登录"}}</text></view></block><block wx:if="{{logintype_2}}"><view class="othertype-item" data-url="login?logintype=2" data-opentype="redirect" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/reg-tellogin.png'}}"></image><text class="txt">手机号登录</text></view></block></view></block></block></block></block><block wx:if="{{logintype==4}}"><block><view class="authlogin"><view class="authlogin-logo"><image style="width:100%;height:100%;" src="{{logo}}"></image></view><view class="authlogin-name">{{"授权登录"+name}}</view><block wx:if="{{reg_invite_code&&parent}}"><view class="authlogin-parent" style="margin-top:30rpx;text-align:center;color:#666;"><block wx:if="{{reg_invite_code_type==0}}"><block>邀请人：<image style="width:80rpx;height:80rpx;border-radius:50%;vertical-align:middle;" src="{{parent.headimg}}"></image>{{''+parent.nickname+''}}</block></block><block wx:else><block>{{'邀请码：'+parent.yqcode+''}}</block></block></view></block><button class="authlogin-btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m7+' 0%,rgba('+$root.m8+',0.8) 100%)')+';'}}" open-type="getPhoneNumber" data-event-opts="{{[['getphonenumber',[['getPhoneNumber',['$event']]]]]}}" bindgetphonenumber="__e">{{platformname+"授权绑定手机号"}}</button><block wx:if="{{login_bind==1}}"><button data-event-opts="{{[['tap',[['nobindregister',['$event']]]]]}}" class="authlogin-btn2" bindtap="__e">暂不绑定</button></block></view></block></block><block wx:if="{{logintype==5}}"><block><form data-event-opts="{{[['submit',[['setnicknameregister',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="title">请设置头像昵称</view><view class="regform"><block wx:if="{{reg_invite_code&&parent}}"><view class="form-item" style="color:#666;margin-bottom:20rpx;"><block wx:if="{{reg_invite_code_type==0}}"><block>邀请人：<image style="width:80rpx;height:80rpx;border-radius:50%;" src="{{parent.headimg}}"></image>{{''+parent.nickname+''}}</block></block><block wx:else><block>{{'邀请码：'+parent.yqcode+''}}</block></block></view></block><view class="form-item" style="height:120rpx;line-height:120rpx;"><view class="flex1">头像</view><button style="width:100rpx;height:100rpx;" open-type="chooseAvatar" data-event-opts="{{[['chooseavatar',[['onChooseAvatar',['$event']]]]]}}" bindchooseavatar="__e"><image style="width:100%;height:100%;border-radius:50%;" src="{{headimg||default_headimg}}"></image></button></view><view class="form-item" style="height:120rpx;line-height:120rpx;"><view class="flex1">昵称</view><input class="input" style="text-align:right;" type="nickname" placeholder="请输入昵称" name="nickname" placeholder-style="font-size:30rpx;color:#B2B5BE"/></view><button class="form-btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m9+' 0%,rgba('+$root.m10+',0.8) 100%)')+';'}}" form-type="submit">确定</button><block wx:if="{{login_setnickname==1}}"><button data-event-opts="{{[['tap',[['nosetnicknameregister',['$event']]]]]}}" class="form-btn2" bindtap="__e">暂不设置</button></block></view></form></block></block><block wx:if="{{showxieyi}}"><view class="xieyibox"><view class="xieyibox-content"><view style="overflow:scroll;height:100%;"><parse vue-id="05513098-2" content="{{xycontent}}" data-event-opts="{{[['^navigate',[['navigate']]]]}}" bind:navigate="__e" bind:__l="__l"></parse></view><view data-event-opts="{{[['tap',[['hidexieyi',['$event']]]]]}}" style="{{'position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center;width:50%;height:60rpx;line-height:60rpx;color:#fff;border-radius:8rpx;'+('background:'+('linear-gradient(90deg,'+$root.m11+' 0%,rgba('+$root.m12+',0.8) 100%)')+';')}}" bindtap="__e">已阅读并同意</view></view></view></block><block wx:if="{{showxieyi2}}"><view class="xieyibox"><view class="xieyibox-content"><view style="overflow:scroll;height:100%;"><parse vue-id="05513098-3" content="{{xycontent2}}" data-event-opts="{{[['^navigate',[['navigate']]]]}}" bind:navigate="__e" bind:__l="__l"></parse></view><view data-event-opts="{{[['tap',[['hidexieyi2',['$event']]]]]}}" style="{{'position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center;width:50%;height:60rpx;line-height:60rpx;color:#fff;border-radius:8rpx;'+('background:'+('linear-gradient(90deg,'+$root.m13+' 0%,rgba('+$root.m14+',0.8) 100%)')+';')}}" bindtap="__e">已阅读并同意</view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="05513098-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="05513098-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="05513098-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>