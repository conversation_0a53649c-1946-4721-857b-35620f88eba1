<?php
// 调试工作流日志数据
require_once 'vendor/autoload.php';

use think\facade\Db;
use think\facade\Config;

// 初始化数据库配置
Config::set([
    'database' => [
        'default' => 'mysql',
        'connections' => [
            'mysql' => [
                'type' => 'mysql',
                'hostname' => 'localhost',
                'database' => 'qixian_zhongheng',
                'username' => 'root',
                'password' => 'root123',
                'hostport' => '3306',
                'prefix' => 'ddwx_',
                'charset' => 'utf8mb4',
            ]
        ]
    ]
], 'database');

try {
    echo "<h2>工作流执行日志数据调试</h2>";
    
    // 查询原始数据
    $list = Db::name('coze_workflow_log')
        ->where('aid', 1)
        ->field('id,workflow_id,execute_id,parameters,is_async,debug_url,status,result,mid,create_time,update_time')
        ->order('id desc')
        ->limit(5)
        ->select();
    
    echo "<h3>原始数据：</h3>";
    echo "<pre>";
    print_r($list->toArray());
    echo "</pre>";
    
    // 测试数据格式化
    $formattedList = [];
    foreach($list as $item){
        $formattedItem = [
            'id' => $item['id'],
            'workflow_id' => $item['workflow_id'],
            'execute_id' => $item['execute_id'] ?? '',
            'parameters' => $item['parameters'] ?? '',
            'is_async' => $item['is_async'] ?? 0,
            'debug_url' => $item['debug_url'] ?? '',
            'status' => $item['status'] ?? '',
            'result' => $item['result'] ?? '',
            'mid' => $item['mid'] ?? 0,
            'create_time' => $item['create_time'] ?? 0,
            'update_time' => $item['update_time'] ?? 0
        ];
        
        // 格式化时间
        $formattedItem['create_time_format'] = $formattedItem['create_time'] ? date('Y-m-d H:i:s', $formattedItem['create_time']) : '';
        $formattedItem['update_time_format'] = $formattedItem['update_time'] ? date('Y-m-d H:i:s', $formattedItem['update_time']) : '';
        $formattedItem['is_async_text'] = $formattedItem['is_async'] ? '异步' : '同步';
        
        // 状态显示
        switch($formattedItem['status']){
            case 'completed':
                $formattedItem['status_text'] = '已完成';
                $formattedItem['status_class'] = 'layui-bg-green';
                break;
            case 'running':
                $formattedItem['status_text'] = '运行中';
                $formattedItem['status_class'] = 'layui-bg-blue';
                break;
            case 'failed':
                $formattedItem['status_text'] = '失败';
                $formattedItem['status_class'] = 'layui-bg-red';
                break;
            default:
                $formattedItem['status_text'] = '未知';
                $formattedItem['status_class'] = 'layui-bg-gray';
        }

        // 解析参数
        if(!empty($formattedItem['parameters'])){
            $params = json_decode($formattedItem['parameters'], true);
            $formattedItem['parameters_text'] = $params ? json_encode($params, JSON_UNESCAPED_UNICODE) : $formattedItem['parameters'];
        } else {
            $formattedItem['parameters_text'] = '无参数';
        }

        // 解析结果
        if(!empty($formattedItem['result'])){
            $result = json_decode($formattedItem['result'], true);
            if($result && isset($result['output'])){
                $output = json_decode($result['output'], true);
                $formattedItem['output_text'] = $output ? json_encode($output, JSON_UNESCAPED_UNICODE) : $result['output'];
            } else {
                $formattedItem['output_text'] = '无输出';
            }
        } else {
            $formattedItem['output_text'] = '无结果';
        }
        
        $formattedList[] = $formattedItem;
    }
    
    echo "<h3>格式化后数据：</h3>";
    echo "<pre>";
    print_r($formattedList);
    echo "</pre>";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage();
}
?>
