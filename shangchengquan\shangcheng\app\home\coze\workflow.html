<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>工作流管理</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  {include file="public/css"/}
</head>
<body>
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-card layui-col-md12">
          <div class="layui-card-header"><i class="fa fa-list"></i> 工作流管理</div>
          <div class="layui-card-body" pad15>
						<div class="layui-col-md4" style="padding-bottom:10px">
							<a class="layui-btn layuiadmin-btn-list" href="javascript:void(0)" onclick="openmax('{:url('edit')}/type/workflow')">添加工作流</a>
							<button class="layui-btn layui-btn-primary layuiadmin-btn-list" onclick="datadel(0)">删除</button>
							<button class="layui-btn layui-btn-primary layuiadmin-btn-list" onclick="setst(0,1)">启用</button>
							<button class="layui-btn layui-btn-primary layuiadmin-btn-list" onclick="setst(0,0)">禁用</button>
						</div>
						<div class="layui-form layui-col-md8" style="text-align:right;padding-bottom:10px">
							<div class="layui-inline layuiadmin-input-useradmin">
								<label class="layui-form-label" style="width:60px">名称</label>
								<div class="layui-input-block" style="width:120px;margin-left:90px">
									<input type="text" name="name" autocomplete="off" class="layui-input" value="">
								</div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label" style="width:30px">状态</label>
								<div class="layui-input-block" style="width:80px;margin-left:60px;text-align:left">
									<select name="status">
										<option value="">全部</option>
										<option value="1">启用</option>
										<option value="0">禁用</option>
									</select>
								</div>
							</div>
							<div class="layui-inline">
								<button class="layui-btn layuiadmin-btn-useradmin" lay-submit lay-filter="LAY-user-front-search">
									<i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
								</button>
							</div>
						</div>
						<table class="layui-hide" id="LAY-user-manage" lay-filter="LAY-user-manage"></table>
						<script type="text/html" id="table-useradmin-admin">
							<div class="layui-btn-container">
								{{# if(d.status == 1){ }}
								<a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="testSync">同步测试</a>
								<a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="testAsync">异步测试</a>
								{{# } }}
								<a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="params">参数配置</a>
								<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
								<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
							</div>
						</script>
					</div>
        </div>
    </div>
  </div>
  {include file="public/js"/}
  <script>
  layui.use(['table', 'form'], function(){
      var $ = layui.$
      ,table = layui.table
      ,form = layui.form;

      window.tableIns = table.render({
          elem: '#LAY-user-manage'
          ,url: '{:url("workflow")}'
          ,cols: [[
              {type: 'checkbox', fixed: 'left'}
              ,{field: 'id', width: 80, title: 'ID', sort: true}
              ,{field: 'name', title: '工作流名称', minWidth: 200}
              ,{field: 'workflow_id', title: '工作流ID', minWidth: 200}
              ,{field: 'description', title: '描述', minWidth: 200}
              ,{field: 'status_text', title: '状态', width: 80, align: 'center'}
              ,{field: 'create_time_text', title: '创建时间', width: 160}
              ,{title: '操作', width: 150, align: 'center', fixed: 'right', toolbar: '#table-useradmin-admin'}
          ]]
          ,page: true
          ,limit: 20
          ,height: 'full-220'
          ,text: '对不起，加载出现异常！'
      });

      //监听搜索
      form.on('submit(LAY-user-front-search)', function(data){
          var field = data.field;
          tableIns.reload({
              where: field
          });
      });

      //监听工具条
      table.on('tool(LAY-user-manage)', function(obj){
          var data = obj.data;
          if(obj.event === 'del'){
              layer.confirm('确定删除此工作流？', function(index){
                  datadel(data.id);
                  layer.close(index);
              });
          } else if(obj.event === 'edit'){
              openmax('{:url("edit")}/type/workflow/id/' + data.id);
          } else if(obj.event === 'params'){
              openmax('{:url("workflowParams")}/workflow_id/' + data.workflow_id);
          } else if(obj.event === 'testSync'){
              testWorkflow(data, false);
          } else if(obj.event === 'testAsync'){
              testWorkflow(data, true);
          }
      });
  });

  function datadel(id){
      var checkStatus = window.tableIns.checkStatus('LAY-user-manage');
      var data = checkStatus.data;
      var ids = [];
      if(id > 0){
          ids.push(id);
      } else {
          if(data.length === 0){
              layer.msg('请选择要删除的数据');
              return;
          }
          for(var i = 0; i < data.length; i++){
              ids.push(data[i].id);
          }
      }

      $.post('{:url("del")}', {ids: ids, type: 'workflow'}, function(res){
          if(res.status == 1){
              layer.msg(res.msg, {icon: 1});
              window.tableIns.reload();
          } else {
              layer.msg(res.msg, {icon: 2});
          }
      });
  }

  function setst(id, status){
      var checkStatus = window.tableIns.checkStatus('LAY-user-manage');
      var data = checkStatus.data;
      var ids = [];
      if(id > 0){
          ids.push(id);
      } else {
          if(data.length === 0){
              layer.msg('请选择要操作的数据');
              return;
          }
          for(var i = 0; i < data.length; i++){
              ids.push(data[i].id);
          }
      }

      $.post('{:url("setst")}', {ids: ids, status: status, type: 'workflow'}, function(res){
          if(res.status == 1){
              layer.msg(res.msg, {icon: 1});
              window.tableIns.reload();
          } else {
              layer.msg(res.msg, {icon: 2});
          }
      });
  }

  // 测试工作流
  function testWorkflow(data, isAsync) {
      var workflowId = data.workflow_id;
      var workflowName = data.name;

      // 先获取工作流参数配置，根据配置模式显示不同界面
      $.get('{:url("getWorkflowParams")}/workflow_id/' + workflowId, function(res) {
          if (res.code === 1) {
              var workflow = res.data.workflow;
              var params = res.data.params;
              var mode = res.data.mode;

              if (mode === 'custom' && params && params.length > 0) {
                  // 自定义字段模式：显示动态表单
                  showDynamicTestForm(workflowId, workflowName, params, isAsync);
              } else {
                  // JSON模式：显示传统JSON输入框
                  showJsonTestForm(workflowId, workflowName, workflow, isAsync);
              }
          } else {
              // 获取参数配置失败，使用JSON模式
              showJsonTestForm(workflowId, workflowName, data, isAsync);
          }
      }).fail(function() {
          // 请求失败，使用JSON模式
          console.log('获取工作流参数配置失败，使用JSON模式');
          showJsonTestForm(workflowId, workflowName, data, isAsync);
      });
  }

  // 显示JSON参数测试表单
  function showJsonTestForm(workflowId, workflowName, data, isAsync) {
      // 获取工作流的默认参数
      var defaultParams = {};

      // 处理不同的数据结构
      var defaultParamsStr = '';
      if (data && data.default_params) {
          defaultParamsStr = data.default_params;
      } else if (data && data.default_parameters) {
          defaultParamsStr = data.default_parameters;
      }

      if (defaultParamsStr) {
          try {
              defaultParams = JSON.parse(defaultParamsStr);
          } catch (e) {
              console.log('解析默认参数失败:', e);
              defaultParams = {};
          }
      }

      // 如果没有配置默认参数，使用硬编码的默认值
      if (Object.keys(defaultParams).length === 0 && workflowId === '7519352650686890038') {
          defaultParams = {
              'BOT_USER_INPUT': '',
              'gender': '男',
              'image_url': 'https://p9-bot-workflow-sign.byteimg.com/tos-cn-i-mdko3gqilj/3a2b90b00c3847b789552acd74616d11.jpg~tplv-mdko3gqilj-image.image?rk3s=81d4c505&x-expires=1781840170&x-signature=MpZX%2FHwnljK5BWi025xiPjzXEqM%3D&x-wf-file_name=%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250526170910.jpg',
              'zhiye': '医生'
          };
      }

      // 弹出参数输入框
      layer.open({
          type: 1,
          title: '测试工作流: ' + workflowName + ' (' + (isAsync ? '异步' : '同步') + ') - JSON模式',
          content: '<div style="padding: 20px;">' +
                   '<div class="layui-form-item">' +
                   '<label class="layui-form-label">工作流ID:</label>' +
                   '<div class="layui-input-block">' + workflowId + '</div>' +
                   '</div>' +
                   '<div class="layui-form-item">' +
                   '<label class="layui-form-label">执行参数:</label>' +
                   '<div class="layui-input-block">' +
                   '<textarea id="testParams" placeholder="请输入JSON格式的参数" class="layui-textarea" style="height: 200px;">' +
                   JSON.stringify(defaultParams, null, 2) + '</textarea>' +
                   '</div>' +
                   '</div>' +
                   '<div class="layui-form-item">' +
                   '<div class="layui-input-block">' +
                   '<button type="button" class="layui-btn" id="executeBtn">执行工作流</button>' +
                   '<button type="button" class="layui-btn layui-btn-primary" id="cancelBtn">取消</button>' +
                   '</div>' +
                   '</div>' +
                   '</div>',
          area: ['600px', '450px'],
          closeBtn: 1, // 显示关闭按钮
          shadeClose: true, // 点击遮罩关闭
          maxmin: true, // 显示最大化最小化按钮
          success: function(layero, index) {
              // 执行按钮事件
              layero.find('#executeBtn').on('click', function() {
                  var params = layero.find('#testParams').val();
                  try {
                      var parsedParams = params ? JSON.parse(params) : {};
                      executeWorkflowRequest(workflowId, workflowName, parsedParams, isAsync, index);
                  } catch (e) {
                      layer.msg('参数格式错误，请输入有效的JSON格式', {icon: 2});
                  }
              });

              // 取消按钮事件
              layero.find('#cancelBtn').on('click', function() {
                  layer.close(index);
              });
          }
      });
  }

  // 显示动态参数测试表单
  function showDynamicTestForm(workflowId, workflowName, params, isAsync) {
      var formHtml = '<div style="padding: 20px;">' +
                     '<div class="layui-form-item">' +
                     '<label class="layui-form-label">工作流ID:</label>' +
                     '<div class="layui-input-block">' + workflowId + '</div>' +
                     '</div>';

      // 生成动态表单字段
      params.forEach(function(param) {
          var required = param.is_required ? ' <span style="color: red;">*</span>' : '';
          var placeholder = param.placeholder || '请输入' + param.param_name;

          formHtml += '<div class="layui-form-item">';
          formHtml += '<label class="layui-form-label">' + param.param_name + required + '</label>';
          formHtml += '<div class="layui-input-block">';

          switch(param.param_type) {
              case 'text':
                  formHtml += '<input type="text" name="' + param.param_key + '" placeholder="' + placeholder + '" class="layui-input" value="' + (param.default_value || '') + '">';
                  break;
              case 'number':
                  formHtml += '<input type="number" name="' + param.param_key + '" placeholder="' + placeholder + '" class="layui-input" value="' + (param.default_value || '') + '">';
                  break;
              case 'textarea':
                  formHtml += '<textarea name="' + param.param_key + '" placeholder="' + placeholder + '" class="layui-textarea">' + (param.default_value || '') + '</textarea>';
                  break;
              case 'select':
                  formHtml += '<select name="' + param.param_key + '" lay-filter="' + param.param_key + '">';
                  if (param.param_options && param.param_options.options) {
                      param.param_options.options.forEach(function(option) {
                          var selected = option.value === param.default_value ? ' selected' : '';
                          formHtml += '<option value="' + option.value + '"' + selected + '>' + option.label + '</option>';
                      });
                  }
                  formHtml += '</select>';
                  break;
              case 'switch':
                  var checked = param.default_value ? ' checked' : '';
                  formHtml += '<input type="checkbox" name="' + param.param_key + '" lay-skin="switch" lay-text="是|否"' + checked + '>';
                  break;
              case 'date':
                  formHtml += '<input type="text" name="' + param.param_key + '" placeholder="' + placeholder + '" class="layui-input" id="date_' + param.param_key + '" value="' + (param.default_value || '') + '">';
                  break;
              default:
                  formHtml += '<input type="text" name="' + param.param_key + '" placeholder="' + placeholder + '" class="layui-input" value="' + (param.default_value || '') + '">';
          }

          if (param.description) {
              formHtml += '<div class="layui-form-mid layui-word-aux">' + param.description + '</div>';
          }

          formHtml += '</div>';
          formHtml += '</div>';
      });

      formHtml += '<div class="layui-form-item">' +
                  '<div class="layui-input-block">' +
                  '<button type="button" class="layui-btn" id="executeDynamicBtn">执行工作流</button>' +
                  '<button type="button" class="layui-btn layui-btn-primary" id="cancelDynamicBtn">取消</button>' +
                  '</div>' +
                  '</div>' +
                  '</div>';

      layer.open({
          type: 1,
          title: '测试工作流: ' + workflowName + ' (' + (isAsync ? '异步' : '同步') + ') - 自定义字段模式',
          content: formHtml,
          area: ['700px', '600px'],
          closeBtn: 1, // 显示关闭按钮
          shadeClose: true, // 点击遮罩关闭
          maxmin: true, // 显示最大化最小化按钮
          success: function(layero, index) {
              // 重新渲染layui表单
              layui.use('form', function(){
                  var form = layui.form;
                  form.render();
              });

              // 初始化日期选择器
              params.forEach(function(param) {
                  if (param.param_type === 'date') {
                      layui.use('laydate', function(){
                          var laydate = layui.laydate;
                          laydate.render({
                              elem: '#date_' + param.param_key
                          });
                      });
                  }
              });

              // 执行按钮事件
              layero.find('#executeDynamicBtn').on('click', function() {
                  var formData = {};

                  // 收集表单数据
                  layero.find('input, textarea, select').each(function() {
                      var name = $(this).attr('name');
                      var value = $(this).val();

                      if ($(this).attr('type') === 'checkbox') {
                          value = $(this).prop('checked');
                      }

                      if (name) {
                          formData[name] = value;
                      }
                  });

                  // 验证必填字段
                  var isValid = true;
                  params.forEach(function(param) {
                      if (param.is_required && !formData[param.param_key]) {
                          layer.msg(param.param_name + '不能为空', {icon: 2});
                          isValid = false;
                          return false;
                      }
                  });

                  if (isValid) {
                      executeWorkflowRequest(workflowId, workflowName, formData, isAsync, index);
                  }
              });

              // 取消按钮事件
              layero.find('#cancelDynamicBtn').on('click', function() {
                  layer.close(index);
              });
          }
      });
  }

  // 执行工作流请求
  function executeWorkflowRequest(workflowId, workflowName, parameters, isAsync, layerIndex) {
      var loadIndex = layer.load(2, {content: '执行中...'});

      $.ajax({
          url: '{:url("runWorkflowDemo")}',
          type: 'POST',
          dataType: 'json',
          data: {
              workflow_id: workflowId,
              parameters: JSON.stringify(parameters),
              custom_params: parameters,
              is_async: isAsync
          },
          success: function(res) {
              layer.close(loadIndex);

              if(res.code === 1) {
                  layer.close(layerIndex);

                  var resultContent = '<div style="padding: 20px;">' +
                      '<div class="layui-form-item">' +
                      '<label class="layui-form-label">执行状态:</label>' +
                      '<div class="layui-input-block"><span class="layui-badge layui-bg-green">成功</span></div>' +
                      '</div>';

                  if(res.data) {
                      if(res.data.execute_id) {
                          resultContent += '<div class="layui-form-item">' +
                              '<label class="layui-form-label">执行ID:</label>' +
                              '<div class="layui-input-block">' + res.data.execute_id + '</div>' +
                              '</div>';
                      }

                      if(res.data.debug_url) {
                          resultContent += '<div class="layui-form-item">' +
                              '<label class="layui-form-label">调试链接:</label>' +
                              '<div class="layui-input-block"><a href="' + res.data.debug_url + '" target="_blank">打开调试页面</a></div>' +
                              '</div>';
                      }

                      if(!isAsync && res.data.output) {
                          try {
                              var output = JSON.parse(res.data.output);
                              resultContent += '<div class="layui-form-item">' +
                                  '<label class="layui-form-label">输出结果:</label>' +
                                  '<div class="layui-input-block"><pre>' + JSON.stringify(output, null, 2) + '</pre></div>' +
                                  '</div>';
                          } catch(e) {
                              resultContent += '<div class="layui-form-item">' +
                                  '<label class="layui-form-label">输出结果:</label>' +
                                  '<div class="layui-input-block"><pre>' + res.data.output + '</pre></div>' +
                                  '</div>';
                          }
                      }

                      resultContent += '<div class="layui-form-item">' +
                          '<label class="layui-form-label">完整响应:</label>' +
                          '<div class="layui-input-block"><pre>' + JSON.stringify(res.data, null, 2) + '</pre></div>' +
                          '</div>';
                  }

                  resultContent += '</div>';

                  layer.open({
                      type: 1,
                      title: '执行结果',
                      content: resultContent,
                      area: ['700px', '500px'],
                      btn: ['关闭'],
                      yes: function(index) {
                          layer.close(index);
                      }
                  });

                  if(isAsync) {
                      layer.msg('异步工作流已启动，请到执行日志查看结果', {icon: 1, time: 3000});
                  }
              } else {
                  layer.msg('执行失败: ' + res.msg, {icon: 2});
              }
          },
          error: function() {
              layer.close(loadIndex);
              layer.msg('请求失败', {icon: 2});
          }
      });
  }
  </script>
</body>
</html>
