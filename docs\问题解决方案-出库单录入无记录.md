# 出库单录入后没有记录问题解决方案

## 问题描述

用户反馈：出库单录入后没有记录，数据没有保存到数据库中。

## 问题分析

### 1. 问题定位

通过代码分析发现，`shangchengquan/shangcheng/app/controller/WarehouseOutbound.php` 文件中的 `save()` 方法存在严重缺陷：

1. **只处理基本信息**：方法只保存了 `info` 数组中的基本单据信息
2. **忽略商品数据**：完全没有处理前端提交的 `product` 数组数据
3. **缺少关键字段**：没有设置 `type=2`（出库单类型）和 `original_data`（商品详情）
4. **无库存处理**：没有扣减相应商品的库存数量
5. **缺少验证**：没有验证商品数据和库存充足性

### 2. 根本原因

**代码逻辑不完整**：save方法只实现了基础单据保存，缺少出库单特有的业务逻辑处理。

## 解决方案

### 1. 重写save()方法

完整重构save方法，添加以下功能：

#### A. 数据获取和验证
```php
$info = input('post.info/a');
$productArr = input('post.product/a');

// 验证商品数据
if(!is_array($productArr)){
    return json(['status'=>0,'msg'=>'商品数据有误']);
}
```

#### B. 商品信息验证
```php
foreach ($productArr as $key => $value) {
    $amount = (int)$value['amount'];
    if(empty($value['name']) || empty($amount)){
        return json(['status'=>0,'msg'=>'商品名称或数量不能为空']);
    }

    if($amount <= 0){
        return json(['status'=>0,'msg'=>'出库数量必须大于0']);
    }

    // 检查库存是否足够
    if(isset($value['id']) && $value['id'] > 0){
        $currentStock = Db::name('warehouse_product')->where('id', $value['id'])->value('stock');
        if($currentStock < $amount){
            return json(['status'=>0,'msg'=>'商品【'.$value['name'].'】库存不足']);
        }
    }
}
```

#### C. 事务处理和数据保存
```php
// 开启事务
Db::startTrans();
try {
    $info['aid'] = aid;
    $info['bid'] = bid;
    $info['type'] = 2; // 出库单类型
    $info['original_data'] = json_encode($productArr);
    
    // 保存单据
    $voucherId = Db::name('warehouse_voucher')->insertGetId($info);
    
    // 扣减库存
    foreach ($productArr as $product) {
        if(isset($product['id']) && $product['id'] > 0){
            $currentStock = Db::name('warehouse_product')->where('id', $product['id'])->value('stock');
            $newStock = $currentStock - $product['amount'];
            
            Db::name('warehouse_product')->where('id', $product['id'])->update(['stock' => $newStock]);
            
            // 记录库存调整日志
            $this->addStockLog($product['id'], $product['amount'], 2, '出库单扣减库存', $voucherId);
        }
    }
    
    Db::commit();
    return json(['status'=>1,'msg'=>'操作成功','url'=>(string)url('index')]);
    
} catch (\Exception $e) {
    Db::rollback();
    return json(['status'=>0,'msg'=>'保存失败：' . $e->getMessage()]);
}
```

### 2. 添加库存调整日志方法

```php
/**
 * 添加库存调整日志
 * @param int $productId 商品ID
 * @param int $amount 调整数量
 * @param int $type 调整类型 1=入库增加 2=出库减少 3=删除单据恢复
 * @param string $remark 备注
 * @param int $voucherId 单据ID
 */
private function addStockLog($productId, $amount, $type, $remark, $voucherId = 0) {
    $logData = [
        'aid' => aid,
        'bid' => bid,
        'product_id' => $productId,
        'amount' => $amount,
        'type' => $type,
        'remark' => $remark,
        'voucher_id' => $voucherId,
        'createtime' => time()
    ];
    
    Db::name('warehouse_stock_log')->insert($logData);
}
```

## 修复后的功能特性

### 1. 完整的业务逻辑
- ✅ 保存出库单基本信息
- ✅ 保存商品详情到 `original_data` 字段
- ✅ 设置正确的单据类型 `type=2`
- ✅ 扣减相应商品库存
- ✅ 记录库存调整日志

### 2. 数据验证
- ✅ 商品名称和数量验证
- ✅ 库存充足性检查
- ✅ 数据格式验证

### 3. 安全特性
- ✅ 事务处理确保数据一致性
- ✅ 异常处理和回滚机制
- ✅ 详细的错误提示

### 4. 日志记录
- ✅ 操作日志记录
- ✅ 库存调整日志
- ✅ 完整的审计追踪

## 测试验证

修复后需要验证以下功能：

1. **基本功能**：
   - 出库单能正常保存
   - 商品信息正确记录
   - 库存正确扣减

2. **验证功能**：
   - 空商品名称或数量的验证
   - 库存不足时的提示
   - 数据格式错误的处理

3. **异常处理**：
   - 数据库异常时的回滚
   - 网络异常时的处理

## 后续修复：字段不存在错误

### 问题描述
修复后出现新错误：`保存失败：fields not exists:[amount]`

### 原因分析
在保存数据时，`$info`数组包含了前端表单的所有字段，包括不属于`warehouse_voucher`表的字段（如`amount`），导致数据库操作失败。

### 解决方案
重构数据保存逻辑，明确指定要保存到`warehouse_voucher`表的字段：

```php
// 准备保存到warehouse_voucher表的数据
$voucherData = [
    'aid' => aid,
    'bid' => bid,
    'type' => 2, // 出库单类型
    'docnum' => $info['docnum'],
    'docdate' => $info['docdate'],
    'original_data' => json_encode($productArr),
    'remark' => isset($info['remark']) ? $info['remark'] : '',
    'order_id' => isset($info['order_id']) ? $info['order_id'] : 0
];
```

## 提交记录

```
git commit -m "fix: 修复出库单录入后没有记录的问题

- 重写save()方法，正确处理商品数据和库存扣减
- 添加商品数据验证，确保名称和数量不为空
- 添加库存充足性检查，防止超出库存出库
- 设置正确的type=2（出库单类型）
- 保存original_data字段存储商品详情
- 添加事务处理确保数据一致性
- 新增库存调整日志记录出库操作
- 移除未使用的saveOutbound方法
- 添加详细的操作日志记录"

git commit -m "fix: 修复出库单保存时字段不存在的错误

- 修复warehouse_voucher表不存在amount字段导致的保存失败
- 重构数据保存逻辑，只保存表中实际存在的字段
- 使用voucherData数组明确指定要保存的字段
- 确保docnum、docdate、remark、order_id等字段正确保存
- 避免将前端表单中的无关字段传入数据库操作"
```

## 预防措施

1. **代码审查**：确保所有CRUD操作都有完整的业务逻辑
2. **单元测试**：为关键业务方法编写测试用例
3. **集成测试**：验证前后端数据交互的完整性
4. **日志监控**：监控业务操作的成功率和异常情况
