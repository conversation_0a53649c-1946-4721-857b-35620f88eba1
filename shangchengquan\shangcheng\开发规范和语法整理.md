# 开发规范和语法整理

## 📋 Coze系统页面状态说明

### ✅ 正常可用的页面
- **API配置**: `http://localhost/?s=/coze/config` - 配置Coze API密钥和基础设置
- **工作流管理**: `http://localhost/?s=/coze/workflow` - 管理工作流，支持直接测试执行
- **执行日志**: `http://localhost/?s=/coze/workflowLog` - 查看工作流执行记录，支持状态查询
- **工作流演示**: `http://localhost/?s=/coze/workflowDemo` - 工作流演示页面

### ❌ 已移除的页面（解决报错问题）
- ~~API日志~~: `http://localhost/?s=/coze/logs` - 已移除（功能重复）
- ~~响应日志~~: `http://localhost/?s=/coze/responseLogs` - 已移除（未完全实现）
- ~~对话列表~~: `http://localhost/?s=/coze/conversations` - 已移除（当前系统不使用对话功能）

### 🎯 核心功能
1. **工作流管理** - 创建、编辑、删除工作流配置
2. **直接测试执行** - 在工作流列表页面直接测试同步/异步执行
3. **参数配置** - 支持JSON格式的默认参数配置
4. **状态查询** - 支持手动查询异步执行状态
5. **执行日志** - 完整的执行记录和结果查看

---

## 🏗️ 项目结构规范

### 后端控制器结构
```
app/controller/
├── Xxx.php          # 后台管理控制器 (extends Common)
└── ApiXxx.php       # 前端接口控制器 (extends ApiCommon)
```

### 视图文件结构
```
app/home/<USER>/
├── index.html       # 列表页面
├── edit.html        # 编辑页面
└── detail.html      # 详情页面
```

### 数据库表前缀
- 统一使用前缀：`ddwx_`

## 📝 代码开发标准

### 1. 后端控制器规范

#### 基础结构
```php
<?php
namespace app\controller;
use think\facade\Db;
use think\facade\View;

class ModuleName extends Common
{
    public function index()
    {
        define('aid', $this->aid);
        
        if(request()->isAjax()){
            // AJAX数据处理
            return json(['code'=>0,'msg'=>'','count'=>$count,'data'=>$data]);
        }
        
        return View::fetch();
    }
}
```

#### API控制器规范
```php
<?php
namespace app\controller;
use think\facade\Db;

class ApiModuleName extends ApiCommon
{
    public function index()
    {
        $mid = $this->mid; // 用户ID
        
        // 业务逻辑
        
        return json(['code'=>1,'msg'=>'操作成功','data'=>$data]);
    }
}
```

### 2. 数据库操作规范

#### 查询操作
```php
// 列表查询
$list = Db::name('table_name')
    ->where($where)
    ->field('id,name,status,create_time')
    ->page($page, $limit)
    ->order('id desc')
    ->select()
    ->toArray();

// 单条查询
$info = Db::name('table_name')
    ->where('id', $id)
    ->find();
```

#### 数据操作
```php
// 新增
$data = [
    'aid' => aid,
    'name' => $name,
    'create_time' => time(),
    'update_time' => time()
];
$result = Db::name('table_name')->insert($data);

// 更新
$data['update_time'] = time();
$result = Db::name('table_name')
    ->where('aid', aid)
    ->where('id', $id)
    ->update($data);

// 删除
$result = Db::name('table_name')
    ->where('aid', aid)
    ->where('id', $id)
    ->delete();
```

### 3. 返回数据规范

#### 后台AJAX返回
```php
// 成功
return json(['status'=>1, 'msg'=>'操作成功', 'url'=>(string)url('index')]);

// 失败
return json(['status'=>0, 'msg'=>'操作失败']);

// 列表数据
return json(['code'=>0, 'msg'=>'', 'count'=>$count, 'data'=>$data]);
```

#### 前端API返回
```php
// 成功
return json(['code'=>1, 'msg'=>'操作成功', 'data'=>$data]);

// 失败
return json(['code'=>0, 'msg'=>'操作失败']);
```

### 4. 视图模板规范

#### HTML结构
```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>页面标题</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    {include file="public/css"/}
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-row layui-col-space15">
            <div class="layui-card layui-col-md12">
                <div class="layui-card-header">
                    <i class="fa fa-list"></i> 页面标题
                </div>
                <div class="layui-card-body" pad15>
                    <!-- 页面内容 -->
                </div>
            </div>
        </div>
    </div>
    {include file="public/js"/}
    <script>
    // JavaScript代码
    </script>
</body>
</html>
```

#### Layui表格配置
```javascript
layui.use(['table', 'form'], function(){
    var table = layui.table;
    var form = layui.form;
    
    window.tableIns = table.render({
        elem: '#LAY-user-manage',
        url: '{:url("index")}',
        cols: [[
            {type: 'checkbox', fixed: 'left'},
            {field: 'id', title: 'ID', width: 80, sort: true},
            {field: 'name', title: '名称'},
            {field: 'status_text', title: '状态', width: 100},
            {field: 'create_time_text', title: '创建时间', width: 180},
            {title: '操作', width: 200, align: 'center', fixed: 'right', toolbar: '#table-useradmin-admin'}
        ]],
        page: true,
        limit: 20,
        height: 'full-220',
        text: {none: '暂无相关数据'}
    });
});
```

### 5. 表单提交规范

#### 表单HTML
```html
<form class="layui-form" lay-filter="component-form-group">
    <div class="layui-form-item">
        <label class="layui-form-label">字段名：</label>
        <div class="layui-input-inline" style="width:400px">
            <input type="text" name="info[field]" lay-verify="required" lay-verType="tips" class="layui-input" value="{$info.field}" placeholder="请输入内容">
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-input-block">
            <button class="layui-btn" lay-submit lay-filter="formSubmit">保存</button>
            <button type="button" class="layui-btn layui-btn-primary" onclick="closeself()">取消</button>
        </div>
    </div>
</form>
```

#### 表单提交处理
```javascript
form.on('submit(formSubmit)', function(data){
    var field = data.field;
    var index = layer.load();
    $.post("{:url('save')}", field, function(data){
        layer.close(index);
        if(data.status==1){
            layer.msg(data.msg, {icon: 1});
            setTimeout(function(){
                parent.layer.close(parent.layer.getFrameIndex(window.name));
                parent.tableIns.reload();
            }, 1000);
        }else{
            layer.msg(data.msg, {icon: 2});
        }
    });
    return false;
});
```

### 6. 操作日志规范

```php
// 在关键操作位置添加日志
\app\common\System::plog('操作描述');

// 示例
\app\common\System::plog('新增工作流');
\app\common\System::plog('编辑工作流');
\app\common\System::plog('删除工作流');
```

### 7. 菜单配置规范

在 `app/common/Menu.php` 中添加菜单：

```php
$component_module[] = [
    'name' => '模块名称',
    'path' => 'controller/method',
    'authdata' => 'Controller/*,controller/method'
];
```

## 🎨 前端开发规范

### 1. 颜色配置
```html
<!-- 动态颜色 -->
<view :style="{color:t('color1')}">文本</view>

<!-- 渐变背景 -->
<view :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">内容</view>
```

### 2. 图片资源引用
```html
<!-- 使用域名前缀 -->
<image :src="pre_url+'/static/img/icon.png'"/>
```

### 3. 接口调用规范
```javascript
// 统一接口调用格式
this.$http.post('api/method', data).then(res => {
    if(res.code == 1){
        // 成功处理
    }else{
        // 失败处理
    }
});
```

## 🔧 开发工具和命令

### Git提交规范
```bash
git add .
git commit -m "功能描述

- 具体修改点1
- 具体修改点2
- 具体修改点3"
```

### 数据库操作
```sql
-- 表结构修改
ALTER TABLE `ddwx_table_name` 
ADD COLUMN `field_name` VARCHAR(255) COMMENT '字段说明' AFTER `existing_field`;

-- 数据更新
UPDATE `ddwx_table_name` SET `field` = 'value' WHERE `condition` = 'value';
```

## 📋 注意事项

1. **代码一致性**：严格按照现有代码结构和风格编写
2. **字段验证**：所有用户输入必须进行验证和过滤
3. **错误处理**：完善的异常捕获和错误提示
4. **安全考虑**：防止SQL注入、XSS等安全问题
5. **性能优化**：合理使用索引、避免N+1查询
6. **文档更新**：及时更新相关文档和注释

---

**版本**：v1.0  
**更新时间**：2025-07-31
