-- 修复warehouse_stock_log表缺少related_id字段的问题
-- 解决 "数据表字段不存在:[related_id]" 错误

-- 1. 检查当前warehouse_stock_log表结构
DESCRIBE `ddwx_warehouse_stock_log`;

-- 2. 添加related_id字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_schema = DATABASE() 
     AND table_name = 'ddwx_warehouse_stock_log' 
     AND column_name = 'related_id') > 0,
    'SELECT "related_id字段已存在" as result',
    'ALTER TABLE `ddwx_warehouse_stock_log` ADD COLUMN `related_id` int(11) NOT NULL DEFAULT 0 COMMENT "关联记录ID"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 检查修复结果
SELECT 'warehouse_stock_log表修复后结构：' as info;
DESCRIBE `ddwx_warehouse_stock_log`;

-- 4. 显示所有字段
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'ddwx_warehouse_stock_log'
ORDER BY ORDINAL_POSITION;

SELECT 'related_id字段修复完成，请重新测试入库单保存功能' as message;
