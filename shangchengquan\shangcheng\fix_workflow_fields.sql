-- 修复工作流表字段缺失问题
-- 请在数据库中执行以下SQL语句

-- 1. 添加默认参数字段
ALTER TABLE `ddwx_coze_workflow` 
ADD COLUMN `default_params` TEXT COMMENT '默认参数JSON格式' AFTER `description`;

-- 2. 添加参数说明字段  
ALTER TABLE `ddwx_coze_workflow` 
ADD COLUMN `params_description` TEXT COMMENT '参数说明' AFTER `default_params`;

-- 3. 为现有测试工作流添加默认参数
UPDATE `ddwx_coze_workflow` 
SET `default_params` = '{"BOT_USER_INPUT": "", "gender": "男", "image_url": "https://p9-bot-workflow-sign.byteimg.com/tos-cn-i-mdko3gqilj/3a2b90b00c3847b789552acd74616d11.jpg~tplv-mdko3gqilj-image.image?rk3s=81d4c505&x-expires=1781840170&x-signature=MpZX%2FHwnljK5BWi025xiPjzXEqM%3D&x-wf-file_name=%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250526170910.jpg", "zhiye": "医生"}',
    `params_description` = 'BOT_USER_INPUT: 用户输入内容\ngender: 性别选择（男/女）\nimage_url: 图片链接\nzhiye: 职业信息'
WHERE `workflow_id` = '7519352650686890038';

-- 4. 查看表结构确认
SHOW COLUMNS FROM `ddwx_coze_workflow`;
