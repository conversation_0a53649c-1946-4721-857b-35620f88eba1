# 抽奖系统物流功能实现说明

## 功能概述

为抽奖系统增加了完整的发货物流功能，实物奖品中奖后可以进行发货操作，用户可以查询物流信息。

## 主要功能

### 1. 后端管理功能
- **发货管理**: 在抽奖记录页面可以对实物奖品进行发货操作
- **物流查询**: 支持查看物流信息和修改物流信息
- **状态管理**: 自动更新发货状态和物流状态

### 2. 前端用户功能
- **物流查询**: 用户可以在"我的奖品"页面查看物流信息
- **实时更新**: 物流信息实时同步显示
- **状态提醒**: 发货后自动发送微信通知

## 技术实现

### 数据库修改
```sql
-- 在抽奖记录表中增加物流相关字段
ALTER TABLE `ddwx_choujiang_record` 
ADD COLUMN `express_com` varchar(100) DEFAULT NULL COMMENT '快递公司',
ADD COLUMN `express_no` varchar(100) DEFAULT NULL COMMENT '快递单号', 
ADD COLUMN `send_time` int(11) DEFAULT NULL COMMENT '发货时间',
ADD COLUMN `express_status` tinyint(1) DEFAULT '0' COMMENT '物流状态 0未发货 1已发货 2已收货',
ADD COLUMN `address` varchar(500) DEFAULT NULL COMMENT '收货地址',
ADD COLUMN `area` varchar(200) DEFAULT NULL COMMENT '收货区域';
```

### 后端接口
1. **发货接口**: `Choujiang/sendExpress`
   - 参数: recordid, express_com, express_no
   - 功能: 更新发货信息，发送通知

2. **物流查询接口**: `Choujiang/getExpress`
   - 参数: recordid
   - 功能: 查询物流信息

3. **前端物流查询**: `ApiChoujiang/getExpress`
   - 参数: recordid
   - 功能: 前端用户查询物流

### 前端页面
1. **抽奖记录管理页面**: `shangchengquan/shangcheng/app/home/<USER>/record.html`
   - 增加物流信息列
   - 增加发货和查物流按钮
   - 发货弹窗和物流查询弹窗

2. **我的奖品页面**: `tiantianshande/activity/xydzp/myprize.vue`
   - 增加查物流按钮
   - 跳转到物流查询页面

3. **物流查询页面**: `tiantianshande/pagesExt/choujiang/logistics.vue`
   - 显示奖品信息
   - 显示物流轨迹
   - 实时更新物流状态

## 使用说明

### 管理员操作
1. 进入后台 -> 抽奖活动 -> 抽奖记录
2. 找到中奖记录，点击"发货"按钮
3. 选择快递公司，输入快递单号
4. 确认发货，系统自动发送通知给用户
5. 可以点击"查物流"查看物流信息
6. 可以点击"改物流"修改物流信息

### 用户操作
1. 进入抽奖活动页面，点击"我的奖品"
2. 在奖品列表中，已发货的奖品会显示"查物流"按钮
3. 点击"查物流"查看详细的物流信息
4. 物流信息实时更新，显示配送进度

## 系统集成

### 物流查询接口
- 使用系统统一的物流查询接口 `\app\common\Common::getwuliu()`
- 支持多家快递公司查询
- 自动解析物流轨迹信息

### 消息通知
- 发货后自动发送微信模板消息
- 支持公众号和小程序通知
- 包含奖品名称、快递公司、快递单号等信息

### 权限控制
- 后端发货功能需要管理员权限
- 前端物流查询仅限奖品所有者
- 数据安全验证和权限检查

## 配置要求

### 快递公司配置
- 系统需要配置支持的快递公司列表
- 使用 `express_data()` 函数获取快递公司数据

### 消息模板配置
- 需要配置微信消息模板 `tmpl_orderfahuo`
- 模板字段包括: 奖品名称、快递公司、快递单号、收货人信息

## 注意事项

1. **数据库升级**: 部署前需要执行 `choujiang_logistics_upgrade.sql` 升级脚本
2. **权限配置**: 确保管理员有抽奖记录管理权限
3. **物流接口**: 确保物流查询接口正常可用
4. **消息模板**: 确保微信消息模板已正确配置
5. **页面路由**: 确保前端页面路由配置正确

## 扩展功能

### 可扩展的功能点
1. **批量发货**: 支持批量选择记录进行发货
2. **物流状态推送**: 物流状态变化时主动推送通知
3. **收货确认**: 用户确认收货功能
4. **物流评价**: 用户对物流服务进行评价
5. **退换货**: 支持奖品退换货流程

### 性能优化
1. **缓存机制**: 物流信息缓存，减少接口调用
2. **异步查询**: 物流信息异步更新
3. **分页加载**: 大量记录时分页显示

## 维护说明

### 日志记录
- 发货操作会记录到系统日志
- 使用 `\app\common\System::plog()` 记录操作日志

### 错误处理
- 完善的错误提示和异常处理
- 网络异常时的重试机制
- 用户友好的错误提示

### 数据备份
- 重要操作前建议备份数据
- 定期备份物流相关数据表
