<?php /*a:3:{s:72:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\coze\edit.html";i:1753935737;s:73:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\css.html";i:1745486434;s:72:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\js.html";i:1745486434;}*/ ?>
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title><?php if($type=='workflow'): if(!$info['id']): ?>添加工作流<?php else: ?>编辑工作流<?php endif; else: if(!$info['id']): ?>添加配置<?php else: ?>编辑配置<?php endif; ?><?php endif; ?></title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" type="text/css" href="/static/admin/layui/css/layui.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/layui/css/modules/formSelects-v4.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/css/admin.css?v=20210826" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/css/font-awesome.min.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/webuploader/webuploader.css?v=<?php echo time(); ?>" media="all">
<link rel="stylesheet" type="text/css" href="/static/imgsrc/designer.css?v=20220803" media="all">
<link rel="stylesheet" type="text/css" href="/static/fonts/iconfont.css?v=20201218" media="all">
</head>
<body>
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-card layui-col-md12">
				<div class="layui-card-header">
					<?php if($type=='workflow'): if(!$info['id']): ?><i class="fa fa-plus"></i> 添加工作流<?php else: ?><i class="fa fa-pencil"></i> 编辑工作流<?php endif; else: if(!$info['id']): ?><i class="fa fa-plus"></i> 添加配置<?php else: ?><i class="fa fa-pencil"></i> 编辑配置<?php endif; ?>
					<?php endif; ?>
					<i class="layui-icon layui-icon-close" style="font-size:18px;font-weight:bold;cursor:pointer" onclick="closeself()"></i>
				</div>
				<div class="layui-card-body" pad15>
					<div class="layui-form">
						<?php if($type=='workflow'): ?>
						<!-- 工作流表单 -->
						<input type="hidden" name="info[id]" value="<?php echo $info['id']; ?>"/>
						<input type="hidden" name="type" value="workflow"/>
						<div class="layui-form-item">
							<label class="layui-form-label">工作流名称：</label>
							<div class="layui-input-inline" style="width:400px">
								<input type="text" name="info[name]" lay-verify="required" lay-verType="tips" class="layui-input" value="<?php echo $info['name']; ?>" placeholder="请输入工作流名称">
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">工作流ID：</label>
							<div class="layui-input-inline" style="width:400px">
								<input type="text" name="info[workflow_id]" lay-verify="required" lay-verType="tips" class="layui-input" value="<?php echo $info['workflow_id']; ?>" placeholder="请输入Coze工作流ID">
							</div>
							<div class="layui-form-mid layui-word-aux">从Coze平台获取的工作流ID</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">描述：</label>
							<div class="layui-input-inline" style="width:400px">
								<textarea name="info[description]" class="layui-textarea" placeholder="请输入工作流描述"><?php echo $info['description']; ?></textarea>
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">默认参数：</label>
							<div class="layui-input-inline" style="width:600px">
								<textarea name="info[default_params]" class="layui-textarea" placeholder="请输入JSON格式的默认参数，例如：{&quot;param1&quot;: &quot;value1&quot;, &quot;param2&quot;: &quot;value2&quot;}" style="height: 120px;"><?php echo $info['default_params']; ?></textarea>
							</div>
							<div class="layui-form-mid layui-word-aux">
								<div>工作流执行时的默认参数，JSON格式</div>
								<div style="margin-top: 5px;">
									<button type="button" class="layui-btn layui-btn-xs layui-btn-primary" onclick="formatParams()">格式化JSON</button>
									<button type="button" class="layui-btn layui-btn-xs layui-btn-normal" onclick="validateParams()">验证格式</button>
								</div>
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">参数说明：</label>
							<div class="layui-input-inline" style="width:600px">
								<textarea name="info[params_description]" class="layui-textarea" placeholder="请输入参数说明，例如：&#10;param1: 用户输入内容&#10;param2: 性别选择（男/女）" style="height: 80px;"><?php echo $info['params_description']; ?></textarea>
							</div>
							<div class="layui-form-mid layui-word-aux">参数的详细说明，便于使用时参考</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">参数配置模式：</label>
							<div class="layui-input-block">
								<input type="radio" name="info[param_config_mode]" value="0" title="JSON模式" <?php if(!$info['param_config_mode']): ?>checked<?php endif; ?>>
								<input type="radio" name="info[param_config_mode]" value="1" title="自定义字段模式" <?php if($info['param_config_mode']==1): ?>checked<?php endif; ?>>
							</div>
							<div class="layui-form-mid layui-word-aux">选择参数配置方式：JSON模式使用上面的参数配置，自定义字段模式可配置灵活的表单字段</div>
						</div>
						<div class="layui-form-item" id="custom-param-config" style="<?php if(!$info['param_config_mode']): ?>display:none;<?php endif; ?>">
							<label class="layui-form-label">参数字段配置：</label>
							<div class="layui-input-block">
								<?php if($info['workflow_id']): ?>
								<button type="button" class="layui-btn layui-btn-normal" onclick="openParamConfig('<?php echo $info['workflow_id']; ?>')">
									<i class="layui-icon layui-icon-set"></i>配置参数字段
								</button>
								<button type="button" class="layui-btn layui-btn-primary" onclick="previewParamForm('<?php echo $info['workflow_id']; ?>')">
									<i class="layui-icon layui-icon-survey"></i>预览表单
								</button>
								<?php else: ?>
								<div class="layui-word-aux" style="color: #ff5722;">请先保存工作流后再配置参数字段</div>
								<?php endif; ?>
							</div>
							<div class="layui-form-mid layui-word-aux">自定义参数字段，支持多种类型和验证规则</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">状态：</label>
							<div class="layui-input-block">
								<input type="radio" name="info[status]" value="1" title="启用" <?php if($info['status']==1 || !$info['id']): ?>checked<?php endif; ?>>
								<input type="radio" name="info[status]" value="0" title="禁用" <?php if($info['status']==0 && $info['id']): ?>checked<?php endif; ?>>
							</div>
						</div>
						<?php else: ?>
						<!-- 配置表单 -->
						<input type="hidden" name="info[id]" value="<?php echo $info['id']; ?>"/>
						<input type="hidden" name="type" value="config"/>
						<div class="layui-form-item">
							<label class="layui-form-label">API密钥：</label>
							<div class="layui-input-inline" style="width:400px">
								<input type="text" name="info[api_key]" lay-verify="required" lay-verType="tips" class="layui-input" value="<?php echo $info['api_key']; ?>" placeholder="请输入Coze API密钥">
							</div>
							<div class="layui-form-mid layui-word-aux">从Coze平台获取的API密钥</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">API地址：</label>
							<div class="layui-input-inline" style="width:400px">
								<input type="text" name="info[base_url]" lay-verify="required" lay-verType="tips" class="layui-input" value="<?php echo (isset($info['base_url']) && ($info['base_url'] !== '')?$info['base_url']:'https://api.coze.cn'); ?>" placeholder="请输入API地址">
							</div>
							<div class="layui-form-mid layui-word-aux">默认：https://api.coze.cn</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">状态：</label>
							<div class="layui-input-block">
								<input type="radio" name="info[status]" value="1" title="启用" <?php if($info['status']==1 || !$info['id']): ?>checked<?php endif; ?>>
								<input type="radio" name="info[status]" value="0" title="禁用" <?php if($info['status']==0 && $info['id']): ?>checked<?php endif; ?>>
							</div>
						</div>
						<?php endif; ?>
						<div class="layui-form-item">
							<div class="layui-input-block">
								<button class="layui-btn" lay-submit lay-filter="formSubmit">立即提交</button>
								<button type="reset" class="layui-btn layui-btn-primary">重置</button>
							</div>
						</div>
					</div>
				</div>
      </div>
    </div>
  </div>
  <script type="text/javascript" src="/static/admin/layui/layui.all.js?v=20210222"></script>
<script type="text/javascript" src="/static/admin/layui/lay/modules/formSelects-v4.js"></script>
<script type="text/javascript" src="/static/admin/js/jquery-ui.min.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/ueditor/ueditor.js?v=20220707"></script>
<script type="text/javascript" src="/static/admin/ueditor/135editor.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/webuploader/webuploader.js?v=20200620"></script>
<script type="text/javascript" src="/static/admin/js/qrcode.min.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/js/dianda.js?v=2022"></script>
  <script>
  layui.use(['form'], function(){
      var $ = layui.$
      ,form = layui.form;

      //监听提交
      form.on('submit(formSubmit)', function(data){
          var field = data.field;
          var index = layer.load();
          $.post("<?php echo url('save'); ?>", field, function(data){
              layer.close(index);
              if(data.status==1){
                  layer.msg(data.msg, {icon: 1});
                  setTimeout(function(){
                      parent.layer.close(parent.layer.getFrameIndex(window.name));
                      parent.tableIns.reload();
                  }, 1000);
              }else{
                  layer.msg(data.msg, {icon: 2});
              }
          });
          return false;
      });

      // 监听参数配置模式切换
      form.on('radio(param_config_mode)', function(data){
          if(data.value == '1'){
              $('#custom-param-config').show();
          } else {
              $('#custom-param-config').hide();
          }
      });
  });

  // 打开参数配置页面
  function openParamConfig(workflowId) {
      layer.open({
          type: 2,
          title: '参数字段配置',
          content: '<?php echo url("workflowParams"); ?>/workflow_id/' + workflowId,
          area: ['90%', '80%'],
          maxmin: true
      });
  }

  // 预览参数表单
  function previewParamForm(workflowId) {
      $.get('<?php echo url("getWorkflowParams"); ?>/workflow_id/' + workflowId, function(res){
          if(res.code == 1){
              showPreviewForm(res.data);
          } else {
              layer.msg(res.msg, {icon: 2});
          }
      });
  }

  // 显示预览表单
  function showPreviewForm(params) {
      if(!params || params.length == 0) {
          layer.msg('暂无参数配置', {icon: 2});
          return;
      }

      var html = '<div style="padding: 20px;">';
      html += '<h3>参数表单预览</h3>';
      html += '<form class="layui-form">';

      params.forEach(function(param){
          html += '<div class="layui-form-item">';
          html += '<label class="layui-form-label">' + param.param_name;
          if(param.is_required) html += '<span style="color:red;">*</span>';
          html += '</label>';
          html += '<div class="layui-input-block">';

          switch(param.param_type) {
              case 'text':
              case 'number':
                  html += '<input type="' + param.param_type + '" placeholder="' + (param.placeholder || '') + '" class="layui-input" value="' + (param.default_value || '') + '">';
                  break;
              case 'textarea':
                  html += '<textarea placeholder="' + (param.placeholder || '') + '" class="layui-textarea">' + (param.default_value || '') + '</textarea>';
                  break;
              case 'select':
                  html += '<select>';
                  html += '<option value="">请选择</option>';
                  if(param.param_options && param.param_options.options) {
                      param.param_options.options.forEach(function(option){
                          html += '<option value="' + option.value + '"' + (option.value == param.default_value ? ' selected' : '') + '>' + option.label + '</option>';
                      });
                  }
                  html += '</select>';
                  break;
              case 'image':
                  html += '<input type="text" placeholder="' + (param.placeholder || '图片链接') + '" class="layui-input" value="' + (param.default_value || '') + '">';
                  html += '<button type="button" class="layui-btn layui-btn-primary">上传图片</button>';
                  break;
              case 'switch':
                  html += '<input type="checkbox" lay-skin="switch"' + (param.default_value ? ' checked' : '') + '>';
                  break;
              default:
                  html += '<input type="text" placeholder="' + (param.placeholder || '') + '" class="layui-input" value="' + (param.default_value || '') + '">';
          }

          html += '</div>';
          if(param.description) {
              html += '<div class="layui-form-mid layui-word-aux">' + param.description + '</div>';
          }
          html += '</div>';
      });

      html += '</form>';
      html += '</div>';

      layer.open({
          type: 1,
          title: '参数表单预览',
          content: html,
          area: ['600px', '500px'],
          btn: ['关闭']
      });
  }

  // 格式化JSON参数
  function formatParams() {
      var textarea = $('textarea[name="info[default_params]"]');
      var value = textarea.val().trim();

      if (!value) {
          layer.msg('请先输入参数内容', {icon: 2});
          return;
      }

      try {
          var parsed = JSON.parse(value);
          var formatted = JSON.stringify(parsed, null, 2);
          textarea.val(formatted);
          layer.msg('格式化成功', {icon: 1});
      } catch (e) {
          layer.msg('JSON格式错误: ' + e.message, {icon: 2});
      }
  }

  // 验证参数格式
  function validateParams() {
      var textarea = $('textarea[name="info[default_params]"]');
      var value = textarea.val().trim();

      if (!value) {
          layer.msg('参数为空，验证通过', {icon: 1});
          return;
      }

      try {
          var parsed = JSON.parse(value);
          var paramCount = Object.keys(parsed).length;
          layer.msg('JSON格式正确，包含 ' + paramCount + ' 个参数', {icon: 1});
      } catch (e) {
          layer.msg('JSON格式错误: ' + e.message, {icon: 2});
      }
  }
  </script>
</body>
</html>