-- 为工作流表添加默认参数和参数说明字段
-- 执行前请先检查字段是否已存在，避免重复添加

-- 检查并添加默认参数字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'ddwx_coze_workflow'
     AND column_name = 'default_params'
     AND table_schema = DATABASE()) > 0,
    'SELECT "default_params字段已存在" as message',
    'ALTER TABLE `ddwx_coze_workflow` ADD COLUMN `default_params` TEXT COMMENT "默认参数JSON格式" AFTER `description`'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加参数说明字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'ddwx_coze_workflow'
     AND column_name = 'params_description'
     AND table_schema = DATABASE()) > 0,
    'SELECT "params_description字段已存在" as message',
    'ALTER TABLE `ddwx_coze_workflow` ADD COLUMN `params_description` TEXT COMMENT "参数说明" AFTER `default_params`'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为现有的测试工作流添加默认参数（如果记录存在且字段为空）
UPDATE `ddwx_coze_workflow`
SET `default_params` = '{"BOT_USER_INPUT": "", "gender": "男", "image_url": "https://p9-bot-workflow-sign.byteimg.com/tos-cn-i-mdko3gqilj/3a2b90b00c3847b789552acd74616d11.jpg~tplv-mdko3gqilj-image.image?rk3s=81d4c505&x-expires=1781840170&x-signature=MpZX%2FHwnljK5BWi025xiPjzXEqM%3D&x-wf-file_name=%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250526170910.jpg", "zhiye": "医生"}',
    `params_description` = 'BOT_USER_INPUT: 用户输入内容\ngender: 性别选择（男/女）\nimage_url: 图片链接\nzhiye: 职业信息'
WHERE `workflow_id` = '7519352650686890038'
AND (`default_params` IS NULL OR `default_params` = '');

-- 显示表结构确认
SHOW COLUMNS FROM `ddwx_coze_workflow`;
