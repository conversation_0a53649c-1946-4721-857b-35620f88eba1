-- 修复Coze API版本从v3到v1
-- 解决404错误：The requested API endpoint POST /v3/workflow/run does not exist

-- 查看当前配置
SELECT id, aid, name, api_version, status, create_time, update_time 
FROM ddwx_coze_config 
WHERE api_version = 'v3';

-- 更新所有v3配置为v1
UPDATE ddwx_coze_config 
SET api_version = 'v1', 
    update_time = UNIX_TIMESTAMP() 
WHERE api_version = 'v3';

-- 如果没有配置记录，插入默认配置
INSERT IGNORE INTO ddwx_coze_config 
(aid, name, api_key, bot_id, base_url, api_version, status, create_time, update_time) 
VALUES 
(1, '默认配置', '', '', 'https://api.coze.cn', 'v1', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 查看更新后的配置
SELECT id, aid, name, api_version, status, create_time, update_time 
FROM ddwx_coze_config 
ORDER BY id;

-- 显示更新结果
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '没有找到配置记录'
        WHEN SUM(CASE WHEN api_version = 'v1' THEN 1 ELSE 0 END) = COUNT(*) THEN '✅ 所有配置已更新为v1版本'
        ELSE '⚠️ 仍有配置使用其他版本'
    END as update_status,
    COUNT(*) as total_configs,
    SUM(CASE WHEN api_version = 'v1' THEN 1 ELSE 0 END) as v1_configs,
    SUM(CASE WHEN api_version = 'v3' THEN 1 ELSE 0 END) as v3_configs
FROM ddwx_coze_config;
