<?php
/**
 * 鲸犀商城 - 微信公众号小程序商城系统!
 * Copyright © 2020 鲸犀网络科技有限公司 保留所有权利
 * =========================================================
 * 版本：供应链版本
 * 授权主体：无限制版本
 * 授权域名：@@@
     
 * ----------------------------------------------
 * 您只能在商业授权范围内使用，不可二次转售、分发、分享、传播
 * 任何企业和个人不得对代码以任何目的任何形式的再发布
 * =========================================================
 */

// +----------------------------------------------------------------------
// | 扣子管理 扣子配置
// +----------------------------------------------------------------------
namespace app\controller;
use think\facade\Db;
use think\facade\View;

class Coze extends Common
{
    //列表
    public function index(){
        if(request()->isAjax()){
            $page = input('param.page');
            $limit = input('param.limit');
            if(input('param.field') && input('param.order')){
                $order = input('param.field').' '.input('param.order');
            }else{
                $order = 'id desc';
            }
            $where = array();
            $where[] = ['aid','=',aid];
            
            if(input('param.name')) $where[] = ['name','like','%'.input('param.name').'%'];
            if(input('?param.status') && input('param.status')!=='') $where[] = ['status','=',input('param.status')];
            
            $count = 0 + Db::name('coze_config')->where($where)->count();
            $data = Db::name('coze_config')->where($where)->page($page,$limit)->order($order)->select()->toArray();
            
            foreach($data as $k=>$v){
                $data[$k]['status_text'] = $v['status'] ? '启用' : '禁用';
                $data[$k]['create_time_text'] = date('Y-m-d H:i:s', $v['create_time']);
                $data[$k]['update_time_text'] = $v['update_time'] ? date('Y-m-d H:i:s', $v['update_time']) : '';
            }
            return json(['code'=>0,'msg'=>'查询成功','count'=>$count,'data'=>$data]);
        }
        
        return View::fetch();
    }
    
    //编辑配置
    public function edit(){
        $type = input('type', '');
        if($type == 'workflow'){
            // 工作流编辑
            if(input('param.id')){
                $info = Db::name('coze_workflow')->where('aid',aid)->where('id',input('param.id/d'))->find();
                if(!$info){
                    echo '<script>alert("工作流不存在");history.back();</script>';
                    exit;
                }
            }else{
                $info = ['id'=>'','name'=>'','workflow_id'=>'','description'=>'','status'=>1,'create_time'=>time()];
            }
        }else{
            // 配置编辑
            if(input('param.id')){
                $info = Db::name('coze_config')->where('aid',aid)->where('id',input('param.id/d'))->find();
                if(!$info) return json(['status'=>0,'msg'=>'配置不存在']);
            }else{
                $info = array();
            }
        }
        View::assign('info',$info);
        View::assign('type',$type);
        return View::fetch();
    }
    
    //保存配置
    public function save(){
        $info = input('post.info');
        $type = input('post.type', '');

        if(!$info){
            return json(['status'=>0,'msg'=>'参数错误']);
        }

        if($type == 'workflow'){
            // 工作流保存
            if(empty($info['name'])){
                return json(['status'=>0,'msg'=>'工作流名称不能为空']);
            }
            if(empty($info['workflow_id'])){
                return json(['status'=>0,'msg'=>'工作流ID不能为空']);
            }

            $info['aid'] = aid;
            $info['update_time'] = time();

            if($info['id']){
                // 更新工作流
                $result = Db::name('coze_workflow')->where('aid',aid)->where('id',$info['id'])->update($info);
                if($result !== false){
                    \app\common\System::plog('编辑工作流');
                    return json(['status'=>1,'msg'=>'更新成功','url'=>(string)url('workflow')]);
                }else{
                    return json(['status'=>0,'msg'=>'更新失败']);
                }
            }else{
                // 新增工作流
                $info['create_time'] = time();
                $result = Db::name('coze_workflow')->insert($info);
                if($result){
                    \app\common\System::plog('添加工作流');
                    return json(['status'=>1,'msg'=>'添加成功','url'=>(string)url('workflow')]);
                }else{
                    return json(['status'=>0,'msg'=>'添加失败']);
                }
            }
        }else{
            // 配置保存
            if(empty($info['api_key'])){
                return json(['status'=>0,'msg'=>'API密钥不能为空']);
            }

            if(empty($info['base_url'])){
                $info['base_url'] = 'https://api.coze.cn';
            }

            $info['aid'] = aid;
            $info['update_time'] = time();

            if($info['id']){
                // 更新配置
                $result = Db::name('coze_config')->where('aid',aid)->where('id',$info['id'])->update($info);
                if($result !== false){
                    \app\common\System::plog('编辑Coze配置');
                    return json(['status'=>1,'msg'=>'更新成功','url'=>(string)url('index')]);
                }else{
                    return json(['status'=>0,'msg'=>'更新失败']);
                }
            }else{
                // 新增配置
                $info['create_time'] = time();
                $result = Db::name('coze_config')->insert($info);
                if($result){
                    \app\common\System::plog('添加Coze配置');
                    return json(['status'=>1,'msg'=>'添加成功','url'=>(string)url('index')]);
                }else{
                    return json(['status'=>0,'msg'=>'添加失败']);
                }
            }
        }
    }
    
    //删除配置
    public function del(){
        $ids = input('post.ids');
        $type = input('post.type', '');

        // 如果没有传递ids，尝试获取id参数
        if(!$ids){
            $id = input('post.id');
            if(strpos($id, ',') !== false){
                $ids = explode(',', $id);
            } else {
                $ids = [$id];
            }
        }

        if($type == 'workflow'){
            // 删除工作流
            Db::name('coze_workflow')->where('aid',aid)->where('id','in',$ids)->delete();
            \app\common\System::plog('删除工作流');
        }else{
            // 删除配置
            Db::name('coze_config')->where('aid',aid)->where('id','in',$ids)->delete();
            \app\common\System::plog('删除扣子API配置');
        }

        return json(['status'=>1,'msg'=>'删除成功']);
    }

    //设置状态
    public function setst(){
        $ids = input('post.ids');
        $status = input('post.status/d');
        $type = input('post.type', '');

        // 如果没有传递ids，尝试获取id参数
        if(!$ids){
            $id = input('post.id');
            if(strpos($id, ',') !== false){
                $ids = explode(',', $id);
            } else {
                $ids = [$id];
            }
        }

        if($type == 'workflow'){
            // 设置工作流状态
            Db::name('coze_workflow')->where('aid',aid)->where('id','in',$ids)->update(['status'=>$status]);
            \app\common\System::plog('设置工作流状态');
        }else{
            // 设置配置状态
            Db::name('coze_config')->where('aid',aid)->where('id','in',$ids)->update(['status'=>$status]);
            \app\common\System::plog('设置扣子API配置状态');
        }

        return json(['status'=>1,'msg'=>'操作成功']);
    }
    
    //测试API连接
    public function test(){
        $id = input('post.id/d');
        $config = Db::name('coze_config')->where('aid',aid)->where('id',$id)->find();
        
        if(!$config){
            return json(['status'=>0,'msg'=>'配置不存在']);
        }
        
        if(empty($config['api_key'])){
            return json(['status'=>0,'msg'=>'API密钥不能为空']);
        }
        
        // 构建测试请求URL
        $baseUrl = $config['base_url'] ?: 'https://api.coze.cn';
        $apiVersion = $config['api_version'] ?: 'v1';
        $url = rtrim($baseUrl, '/') . '/' . $apiVersion . '/bots';
        
        // 发送测试请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $config['api_key'],
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            return json(['status' => 0, 'msg' => '连接失败: ' . $error]);
        }
        
        if ($httpCode === 200) {
            \app\common\System::plog('测试扣子API连接成功');
            return json(['status' => 1, 'msg' => 'API连接测试成功']);
        } else {
            $responseData = json_decode($response, true);
            $errorMsg = $responseData['msg'] ?? '连接失败，HTTP状态码: ' . $httpCode;
            return json(['status' => 0, 'msg' => $errorMsg]);
        }
    }
    
    //日志列表
    public function logs(){
        if(request()->isAjax()){
            $page = input('param.page');
            $limit = input('param.limit');
            $order = 'id desc';
            $where = array();
            $where[] = ['aid','=',aid];
            
            if(input('param.endpoint')) $where[] = ['endpoint','like','%'.input('param.endpoint').'%'];
            if(input('param.status')) $where[] = ['status','=',input('param.status')];
            
            $count = 0 + Db::name('coze_api_log')->where($where)->count();
            $data = Db::name('coze_api_log')->where($where)->page($page,$limit)->order($order)->select()->toArray();
            
            foreach($data as $k=>$v){
                $data[$k]['status_text'] = $v['status'] ? '成功' : '失败';
                $data[$k]['request_time_text'] = date('Y-m-d H:i:s', strtotime($v['request_time']));
                $data[$k]['response_time_text'] = $v['response_time'] ? date('Y-m-d H:i:s', strtotime($v['response_time'])) : '';
            }
            return json(['code'=>0,'msg'=>'查询成功','count'=>$count,'data'=>$data]);
        }
        
        return View::fetch();
    }
    
    //查看日志详情
    public function logdetail(){
        $id = input('param.id/d');
        $log = Db::name('coze_api_log')->where('aid',aid)->where('id',$id)->find();
        if(!$log) return json(['status'=>0,'msg'=>'日志不存在']);
        
        View::assign('log',$log);
        return View::fetch();
    }
    
    //清理日志
    public function clearlogs(){
        $days = input('post.days/d', 30);
        $time = time() - ($days * 24 * 3600);
        $count = Db::name('coze_api_log')->where('aid',aid)->where('request_time','<',date('Y-m-d H:i:s', $time))->delete();
        \app\common\System::plog('清理扣子API日志，清理了'.$count.'条记录');
        return json(['status'=>1,'msg'=>'清理成功，共清理'.$count.'条记录']);
    }

    //配置页面
    public function config(){
        if(request()->isPost()){
            $data = input('post.');

            // 验证必填字段
            if (empty($data['api_key'])) {
                return json(['status' => 0, 'msg' => 'API密钥不能为空']);
            }

            // 查找是否已有配置
            $config = Db::name('coze_config')->where('aid',aid)->find();

            if($config){
                // 更新配置
                $data['update_time'] = time();
                Db::name('coze_config')->where('aid',aid)->where('id',$config['id'])->update($data);
                \app\common\System::plog('更新扣子API配置');
            }else{
                // 新增配置
                $data['aid'] = aid;
                $data['name'] = '默认配置';
                $data['create_time'] = time();
                $data['update_time'] = time();
                Db::name('coze_config')->insert($data);
                \app\common\System::plog('新增扣子API配置');
            }

            return json(['status'=>1,'msg'=>'配置保存成功']);
        }

        // 获取配置信息
        $config = Db::name('coze_config')->where('aid',aid)->find();
        if(!$config){
            $config = [
                'api_key' => '',
                'bot_id' => '',
                'base_url' => 'https://api.coze.cn',
                'api_version' => 'v1',
                'status' => 1
            ];
        }

        View::assign('config',$config);
        return View::fetch();
    }

    //保存配置（专门用于config页面的AJAX提交）
    public function saveConfig(){
        $postData = input('post.');

        // 调试信息
        var_dump('=== saveConfig调试信息 ===');
        var_dump('POST数据:', $postData);

        // 验证必填字段
        if (empty($postData['api_key'])) {
            var_dump('错误: API密钥为空');
            return json(['code' => 0, 'msg' => 'API密钥不能为空']);
        }

        // 过滤允许的字段
        $allowFields = ['api_key', 'bot_id', 'base_url', 'api_version', 'status'];
        $data = [];
        foreach($allowFields as $field){
            if(isset($postData[$field])){
                $data[$field] = $postData[$field];
                var_dump('添加字段:', $field, '值:', $postData[$field]);
            }
        }

        var_dump('过滤后的数据:', $data);

        // 查找是否已有配置
        $config = Db::name('coze_config')->where('aid',aid)->find();
        var_dump('现有配置:', $config);

        try {
            if($config){
                // 更新配置
                var_dump('执行更新操作');
                $data['update_time'] = time();
                $result = Db::name('coze_config')->where('aid',aid)->where('id',$config['id'])->update($data);
                var_dump('更新结果:', $result);
                \app\common\System::plog('更新扣子API配置');
            }else{
                // 新增配置
                var_dump('执行新增操作');
                $data['aid'] = aid;
                $data['name'] = '默认配置';
                $data['create_time'] = time();
                $data['update_time'] = time();
                var_dump('新增数据:', $data);
                $result = Db::name('coze_config')->insert($data);
                var_dump('新增结果:', $result);
                \app\common\System::plog('新增扣子API配置');
            }
        } catch (\Exception $e) {
            var_dump('数据库操作失败:', $e->getMessage());
            return json(['code' => 0, 'msg' => '保存失败: ' . $e->getMessage()]);
        }

        var_dump('saveConfig操作成功');
        return json(['code'=>1,'msg'=>'配置保存成功']);
    }

    //测试连接（专门用于config页面的AJAX调用）
    public function testConnection(){
        $api_key = input('post.api_key');
        $base_url = input('post.base_url', 'https://api.coze.cn');
        $api_version = input('post.api_version', 'v1');

        if(empty($api_key)){
            return json(['code'=>0,'msg'=>'API密钥不能为空']);
        }

        // 构建测试请求URL
        $url = rtrim($base_url, '/') . '/' . $api_version . '/bots';

        // 发送测试请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $api_key,
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            return json(['code' => 0, 'msg' => '连接失败: ' . $error]);
        }

        if ($httpCode === 200) {
            \app\common\System::plog('测试扣子API连接成功');
            return json(['code' => 1, 'msg' => 'API连接测试成功']);
        } else {
            $responseData = json_decode($response, true);
            $errorMsg = $responseData['msg'] ?? '连接失败，HTTP状态码: ' . $httpCode;
            return json(['code' => 0, 'msg' => $errorMsg]);
        }
    }

    //工作流管理页面
    public function workflow(){
        if(request()->isAjax()){
            $page = input('param.page');
            $limit = input('param.limit');
            if(input('param.field') && input('param.order')){
                $order = input('param.field').' '.input('param.order');
            }else{
                $order = 'id desc';
            }
            $where = array();
            $where[] = ['aid','=',aid];

            if(input('param.name')) $where[] = ['name','like','%'.input('param.name').'%'];
            if(input('?param.status') && input('param.status')!=='') $where[] = ['status','=',input('param.status')];

            $count = 0 + Db::name('coze_workflow')->where($where)->count();

            // 检查字段是否存在，避免SQL错误
            try {
                $data = Db::name('coze_workflow')
                    ->where($where)
                    ->field('id,name,workflow_id,description,default_params,params_description,status,create_time,update_time')
                    ->page($page,$limit)
                    ->order($order)
                    ->select()
                    ->toArray();
            } catch (\Exception $e) {
                // 如果字段不存在，使用基础字段查询
                $data = Db::name('coze_workflow')
                    ->where($where)
                    ->field('id,name,workflow_id,description,status,create_time,update_time')
                    ->page($page,$limit)
                    ->order($order)
                    ->select()
                    ->toArray();
            }

            foreach($data as $k=>$v){
                $data[$k]['status_text'] = $v['status'] ? '启用' : '禁用';
                $data[$k]['create_time_text'] = date('Y-m-d H:i:s', $v['create_time']);
                $data[$k]['update_time_text'] = $v['update_time'] ? date('Y-m-d H:i:s', $v['update_time']) : '';

                // 确保默认参数字段存在
                $data[$k]['default_params'] = $v['default_params'] ?? '';
                $data[$k]['params_description'] = $v['params_description'] ?? '';
            }
            return json(['code'=>0,'msg'=>'查询成功','count'=>$count,'data'=>$data]);
        }

        return View::fetch();
    }



    //保存工作流
    public function workflowSave(){
        $info = input('post.info');

        // 调试信息
        var_dump('=== 保存工作流调试信息 ===');
        var_dump('POST数据:', input('post.'));
        var_dump('info数据:', $info);

        // 验证必填字段
        if (empty($info['name'])) {
            var_dump('错误: 工作流名称为空');
            return json(['status' => 0, 'msg' => '工作流名称不能为空']);
        }

        if (empty($info['workflow_id'])) {
            var_dump('错误: 工作流ID为空');
            return json(['status' => 0, 'msg' => '工作流ID不能为空']);
        }

        // 验证默认参数格式
        if (!empty($info['default_params'])) {
            $params = json_decode($info['default_params'], true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return json(['status' => 0, 'msg' => '默认参数格式错误，请输入有效的JSON格式']);
            }
        }

        // 过滤允许的字段
        $data = [];
        if(isset($info['name'])) $data['name'] = $info['name'];
        if(isset($info['workflow_id'])) $data['workflow_id'] = $info['workflow_id'];
        if(isset($info['description'])) $data['description'] = $info['description'];
        if(isset($info['default_params'])) $data['default_params'] = $info['default_params'];
        if(isset($info['params_description'])) $data['params_description'] = $info['params_description'];
        if(isset($info['status'])) $data['status'] = $info['status'];

        var_dump('最终要保存的数据:', $data);

        try {
            if($info['id']){
                // 更新工作流
                var_dump('执行更新操作，ID:', $info['id']);
                $data['update_time'] = time();
                $result = Db::name('coze_workflow')->where('aid',aid)->where('id',$info['id'])->update($data);
                var_dump('更新结果:', $result);
                \app\common\System::plog('更新扣子工作流');
            }else{
                // 新增工作流
                var_dump('执行新增操作');
                $data['aid'] = aid;
                $data['create_time'] = time();
                $data['update_time'] = time();
                $result = Db::name('coze_workflow')->insert($data);
                var_dump('新增结果:', $result);
                \app\common\System::plog('新增扣子工作流');
            }
        } catch (\Exception $e) {
            var_dump('数据库操作失败:', $e->getMessage());
            return json(['status' => 0, 'msg' => '保存失败: ' . $e->getMessage()]);
        }

        var_dump('工作流保存成功');
        return json(['status'=>1,'msg'=>'操作成功','url'=>(string)url('workflow')]);
    }

    //删除工作流
    public function workflowDel(){
        $id = input('id',0,'intval');
        if(!$id){
            return json(['status'=>0,'msg'=>'参数错误']);
        }

        try {
            $result = Db::name('coze_workflow')->where('aid',aid)->where('id',$id)->delete();
            if($result){
                \app\common\System::plog('删除扣子工作流');
                return json(['status'=>1,'msg'=>'删除成功']);
            }else{
                return json(['status'=>0,'msg'=>'删除失败']);
            }
        } catch (\Exception $e) {
            return json(['status' => 0, 'msg' => '删除失败: ' . $e->getMessage()]);
        }
    }

    //测试工作流
    public function testWorkflow(){
        $workflowId = input('workflow_id','');
        $parameters = input('parameters','{}');
        $useCustomParams = input('use_custom_params', 0); // 是否使用自定义参数配置
        $customParams = input('custom_params', []); // 自定义参数值

        if(empty($workflowId)){
            return json(['code'=>0,'msg'=>'工作流ID不能为空']);
        }

        // 获取工作流信息
        $workflow = Db::name('coze_workflow')->where('aid',aid)->where('workflow_id',$workflowId)->find();
        if(!$workflow){
            return json(['code'=>0,'msg'=>'工作流不存在']);
        }

        try {
            // 处理参数
            if($useCustomParams && $workflow['param_config_mode'] == 1){
                // 使用自定义参数配置模式
                $params = $customParams;
            } else {
                // 使用JSON模式
                $params = json_decode($parameters, true);
                if(json_last_error() !== JSON_ERROR_NONE){
                    return json(['code'=>0,'msg'=>'参数格式错误，请使用JSON格式']);
                }
            }

            // 调用工作流
            $result = \app\common\Coze::runWorkflow(aid, $workflowId, $params);

            \app\common\System::plog('测试执行工作流');

            if($result['code'] == 1){
                return json(['code'=>1,'msg'=>'工作流执行成功','data'=>$result['data']]);
            }else{
                return json(['code'=>0,'msg'=>$result['msg'],'data'=>$result['data']]);
            }

        } catch (\Exception $e) {
            return json(['code'=>0,'msg'=>'测试失败: ' . $e->getMessage()]);
        }
    }

    //工作流演示页面
    public function workflowDemo(){
        $workflows = Db::name('coze_workflow')->where('aid',aid)->where('status',1)->select();
        View::assign('workflows',$workflows);
        return View::fetch();
    }

    //运行工作流演示
    public function runWorkflowDemo(){
        $workflowId = input('workflow_id','');
        $parameters = input('parameters','{}');
        $customParams = input('custom_params', []);
        $isAsync = (bool)input('is_async', false);

        if(empty($workflowId)){
            return json(['code'=>0,'msg'=>'工作流ID不能为空']);
        }

        try {
            // 获取工作流信息，检查参数配置模式
            $workflow = Db::name('coze_workflow')
                ->where('aid', aid)
                ->where('workflow_id', $workflowId)
                ->where('status', 1)
                ->find();

            if (!$workflow) {
                return json(['code'=>0,'msg'=>'工作流不存在或已禁用']);
            }

            $params = [];

            // 根据参数配置模式处理参数
            if ($workflow['param_config_mode'] == 1) {
                // 自定义字段模式：使用动态参数验证
                if (!empty($customParams)) {
                    $params = $customParams;
                } else {
                    // 如果没有传递自定义参数，尝试解析JSON参数作为兼容处理
                    $params = json_decode($parameters, true);
                    if(json_last_error() !== JSON_ERROR_NONE){
                        return json(['code'=>0,'msg'=>'参数格式错误，请使用JSON格式或传递custom_params']);
                    }
                }

                // 获取参数配置进行验证
                $paramConfigs = Db::name('coze_workflow_params')
                    ->where('aid', aid)
                    ->where('workflow_id', $workflowId)
                    ->where('status', 1)
                    ->select()
                    ->toArray();

                // 验证必填参数
                foreach ($paramConfigs as $config) {
                    if ($config['is_required'] && empty($params[$config['param_key']])) {
                        return json(['code'=>0,'msg'=>$config['param_name'] . '不能为空']);
                    }
                }

                // 设置默认值
                foreach ($paramConfigs as $config) {
                    if (!isset($params[$config['param_key']]) && !empty($config['default_value'])) {
                        $params[$config['param_key']] = $config['default_value'];
                    }
                }
            } else {
                // JSON模式：使用传统的JSON参数处理
                $params = json_decode($parameters, true);
                if(json_last_error() !== JSON_ERROR_NONE){
                    return json(['code'=>0,'msg'=>'参数格式错误，请使用JSON格式']);
                }
            }

            // 调用工作流（支持异步）
            $result = \app\common\Coze::runWorkflow(aid, $workflowId, $params, $isAsync);

            \app\common\System::plog('运行扣子工作流演示' . ($isAsync ? '(异步)' : '(同步)') . ' - 模式:' . ($workflow['param_config_mode'] == 1 ? '自定义字段' : 'JSON'));

            return json($result);

        } catch (\Exception $e) {
            return json(['code'=>0,'msg'=>'运行失败: ' . $e->getMessage()]);
        }
    }

    //查询工作流异步执行结果
    public function queryWorkflowResult(){
        $workflowId = input('workflow_id','');
        $executeId = input('execute_id','');

        if(empty($workflowId) || empty($executeId)){
            return json(['code'=>0,'msg'=>'工作流ID和执行ID不能为空']);
        }

        try {
            $result = \app\common\Coze::queryWorkflowResult(aid, $workflowId, $executeId);

            \app\common\System::plog('查询扣子工作流执行结果');

            return json($result);

        } catch (\Exception $e) {
            return json(['code'=>0,'msg'=>'查询失败: ' . $e->getMessage()]);
        }
    }

    //获取工作流执行历史
    public function getWorkflowHistory(){
        $page = input('page',1,'intval');
        $limit = input('limit',10,'intval');
        $workflow_id = input('workflow_id','');
        $status = input('status','');

        $where = ['aid' => aid];
        if(!empty($workflow_id)){
            $where['workflow_id'] = $workflow_id;
        }
        if(!empty($status)){
            $where['status'] = $status;
        }

        $list = Db::name('coze_workflow_log')
            ->where($where)
            ->field('id,workflow_id,execute_id,parameters,is_async,debug_url,status,result,mid,create_time,update_time')
            ->order('id desc')
            ->page($page, $limit)
            ->select();

        // 转换为数组并格式化数据
        $list = $list->toArray();
        foreach($list as &$item){
            // 确保字段存在并处理空值
            $item['execute_id'] = $item['execute_id'] ?? '';
            $item['parameters'] = $item['parameters'] ?? '';
            $item['is_async'] = $item['is_async'] ?? 0;
            $item['debug_url'] = $item['debug_url'] ?? '';
            $item['status'] = $item['status'] ?? '';
            $item['result'] = $item['result'] ?? '';
            $item['mid'] = $item['mid'] ?? 0;
            $item['create_time'] = $item['create_time'] ?? 0;
            $item['update_time'] = $item['update_time'] ?? 0;

            // 格式化时间
            $item['create_time_format'] = $item['create_time'] ? date('Y-m-d H:i:s', $item['create_time']) : '';
            $item['update_time_format'] = $item['update_time'] ? date('Y-m-d H:i:s', $item['update_time']) : '';
            $item['is_async_text'] = $item['is_async'] ? '异步' : '同步';

            // 状态显示
            switch($item['status']){
                case 'completed':
                    $item['status_text'] = '已完成';
                    $item['status_class'] = 'layui-bg-green';
                    break;
                case 'running':
                    $item['status_text'] = '运行中';
                    $item['status_class'] = 'layui-bg-blue';
                    break;
                case 'failed':
                    $item['status_text'] = '失败';
                    $item['status_class'] = 'layui-bg-red';
                    break;
                default:
                    $item['status_text'] = $item['status'] ? $item['status'] : '未知';
                    $item['status_class'] = 'layui-bg-gray';
            }

            // 解析参数
            if(!empty($item['parameters']) && $item['parameters'] !== '{}' && $item['parameters'] !== 'null'){
                $params = json_decode($item['parameters'], true);
                if($params && is_array($params) && !empty($params)){
                    $item['parameters_text'] = json_encode($params, JSON_UNESCAPED_UNICODE);
                } else {
                    $item['parameters_text'] = '无参数';
                }
            } else {
                $item['parameters_text'] = '无参数';
            }

            // 解析结果
            if(!empty($item['result']) && $item['result'] !== 'null'){
                $result = json_decode($item['result'], true);
                if($result && is_array($result)){
                    if(isset($result['output']) && !empty($result['output'])){
                        $output = json_decode($result['output'], true);
                        if($output && is_array($output)){
                            $item['output_text'] = json_encode($output, JSON_UNESCAPED_UNICODE);
                        } else {
                            $item['output_text'] = $result['output'];
                        }
                    } else {
                        $item['output_text'] = '无输出';
                    }
                } else {
                    $item['output_text'] = '无输出';
                }
            } else {
                $item['output_text'] = '无结果';
            }
        }

        $count = Db::name('coze_workflow_log')->where($where)->count();

        return json(['code'=>0,'msg'=>'','count'=>$count,'data'=>$list]);
    }

    //工作流执行日志列表页面
    public function workflowLog(){
        // 获取工作流列表用于筛选
        $workflows = Db::name('coze_workflow')
            ->where('aid', aid)
            ->where('status', 1)
            ->field('id,name,workflow_id')
            ->order('id desc')
            ->select();

        View::assign('workflows', $workflows);
        return View::fetch();
    }

    //删除工作流执行日志
    public function delWorkflowLog(){
        $id = input('param.id/d');
        if(!$id){
            return json(['code'=>0,'msg'=>'参数错误']);
        }

        $info = Db::name('coze_workflow_log')->where('aid',aid)->where('id',$id)->find();
        if(!$info){
            return json(['code'=>0,'msg'=>'记录不存在']);
        }

        $result = Db::name('coze_workflow_log')->where('aid',aid)->where('id',$id)->delete();
        if($result){
            \app\common\System::plog('删除扣子工作流执行日志');
            return json(['code'=>1,'msg'=>'删除成功']);
        } else {
            return json(['code'=>0,'msg'=>'删除失败']);
        }
    }

    //批量删除工作流执行日志
    public function batchDelWorkflowLog(){
        $ids = input('param.ids','');
        if(empty($ids)){
            return json(['code'=>0,'msg'=>'请选择要删除的记录']);
        }

        $idArray = explode(',', $ids);
        $result = Db::name('coze_workflow_log')
            ->where('aid', aid)
            ->where('id', 'in', $idArray)
            ->delete();

        if($result){
            \app\common\System::plog('批量删除扣子工作流执行日志');
            return json(['code'=>1,'msg'=>'删除成功，共删除' . $result . '条记录']);
        } else {
            return json(['code'=>0,'msg'=>'删除失败']);
        }
    }

    //查询单个工作流状态
    public function queryWorkflowStatus(){
        $id = input('param.id/d');
        $workflowId = input('param.workflow_id','');
        $executeId = input('param.execute_id','');

        if(!$id || !$workflowId || !$executeId){
            return json(['code'=>0,'msg'=>'参数错误']);
        }

        // 查询记录是否存在
        $logInfo = Db::name('coze_workflow_log')
            ->where('aid', aid)
            ->where('id', $id)
            ->find();

        if(!$logInfo){
            return json(['code'=>0,'msg'=>'记录不存在']);
        }

        try {
            // 调用Coze API查询状态
            $result = \app\common\Coze::queryWorkflowResult(aid, $workflowId, $executeId);

            if($result['code'] == 1 && isset($result['data'])){
                $apiData = $result['data'];

                // 更新数据库记录
                $updateData = [
                    'update_time' => time()
                ];

                if(isset($apiData['execute_status'])){
                    $status = $apiData['execute_status'];
                    $updateData['status'] = strtolower($status);
                }

                if(isset($apiData['output']) && $apiData['output']){
                    $updateData['result'] = json_encode($apiData['output']);
                }

                // 更新记录
                Db::name('coze_workflow_log')
                    ->where('aid', aid)
                    ->where('id', $id)
                    ->update($updateData);

                \app\common\System::plog('查询扣子工作流状态');

                return json([
                    'code' => 1,
                    'msg' => '状态查询成功',
                    'data' => [
                        'status' => $apiData['execute_status'] ?? 'Unknown',
                        'output' => $apiData['output'] ?? null
                    ]
                ]);
            } else {
                return json(['code'=>0,'msg'=>'查询失败: ' . ($result['msg'] ?? '未知错误')]);
            }

        } catch (\Exception $e) {
            return json(['code'=>0,'msg'=>'查询异常: ' . $e->getMessage()]);
        }
    }

    //批量查询运行中的工作流状态
    public function batchQueryStatus(){
        try {
            // 查询所有运行中的记录
            $runningLogs = Db::name('coze_workflow_log')
                ->where('aid', aid)
                ->where('status', 'running')
                ->where('execute_id', '<>', '')
                ->field('id,workflow_id,execute_id')
                ->select();

            $updatedCount = 0;

            foreach($runningLogs as $log){
                try {
                    // 查询每个工作流的状态
                    $result = \app\common\Coze::queryWorkflowResult(aid, $log['workflow_id'], $log['execute_id']);

                    if($result['code'] == 1 && isset($result['data']['execute_status'])){
                        $apiData = $result['data'];
                        $status = strtolower($apiData['execute_status']);

                        // 只有状态发生变化时才更新
                        if($status !== 'running'){
                            $updateData = [
                                'status' => $status,
                                'update_time' => time()
                            ];

                            if(isset($apiData['output']) && $apiData['output']){
                                $updateData['result'] = json_encode($apiData['output']);
                            }

                            Db::name('coze_workflow_log')
                                ->where('aid', aid)
                                ->where('id', $log['id'])
                                ->update($updateData);

                            $updatedCount++;
                        }
                    }
                } catch (\Exception $e) {
                    // 单个查询失败不影响其他查询
                    continue;
                }
            }

            \app\common\System::plog('批量查询扣子工作流状态');

            return json([
                'code' => 1,
                'msg' => '批量查询完成',
                'data' => [
                    'total_count' => count($runningLogs),
                    'updated_count' => $updatedCount
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code'=>0,'msg'=>'批量查询异常: ' . $e->getMessage()]);
        }
    }

    //文件上传
    public function uploadFile(){
        $file = request()->file('file');
        if(!$file){
            return json(['code'=>0,'msg'=>'请选择要上传的文件']);
        }

        try {
            // 保存文件到临时目录
            $tempPath = sys_get_temp_dir() . '/' . uniqid() . '_' . $file->getOriginalName();
            $file->move(dirname($tempPath), basename($tempPath));

            // 上传到Coze
            $result = \app\common\Coze::uploadFile(aid, $tempPath);

            // 清理临时文件
            if(file_exists($tempPath)){
                unlink($tempPath);
            }

            if($result['code'] == 1){
                \app\common\System::plog('上传文件到扣子');
                return json(['code'=>1,'msg'=>'上传成功','data'=>$result['data']]);
            }else{
                return json(['code'=>0,'msg'=>$result['msg']]);
            }

        } catch (\Exception $e) {
            return json(['code'=>0,'msg'=>'上传失败: ' . $e->getMessage()]);
        }
    }

    //演示页面
    public function demo(){
        // 获取启用的工作流列表
        $workflows = Db::name('coze_workflow')
            ->where('aid', aid)
            ->where('status', 1)
            ->order('id desc')
            ->select()
            ->toArray();

        // 获取启用的API配置
        $configs = Db::name('coze_config')
            ->where('aid', aid)
            ->where('status', 1)
            ->order('id desc')
            ->select()
            ->toArray();

        View::assign('workflows', $workflows);
        View::assign('configs', $configs);

        \app\common\System::plog('访问扣子演示页面');

        return View::fetch();
    }

    //获取Bot信息
    public function getBotInfo(){
        try {
            $config = Db::name('coze_config')
                ->where('aid', aid)
                ->where('status', 1)
                ->find();

            if (!$config) {
                return json(['code'=>0,'msg'=>'未找到可用的API配置']);
            }

            \app\common\System::plog('获取Bot信息');

            return json([
                'code'=>1,
                'msg'=>'获取成功',
                'data'=>[
                    'bot_id' => $config['bot_id'],
                    'api_key' => $config['api_key'],
                    'base_url' => $config['base_url']
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code'=>0,'msg'=>'获取Bot信息失败: ' . $e->getMessage()]);
        }
    }

    //创建对话
    public function createConversation(){
        try {
            $botId = input('bot_id','');

            if(empty($botId)){
                return json(['code'=>0,'msg'=>'Bot ID不能为空']);
            }

            $result = \app\common\Coze::createConversation(aid, $botId);

            \app\common\System::plog('创建扣子对话');

            return json($result);

        } catch (\Exception $e) {
            return json(['code'=>0,'msg'=>'创建对话失败: ' . $e->getMessage()]);
        }
    }

    //发送消息
    public function sendMessage(){
        try {
            $conversationId = input('conversation_id','');
            $botId = input('bot_id','');
            $message = input('message','');

            if(empty($conversationId) || empty($botId) || empty($message)){
                return json(['code'=>0,'msg'=>'参数不完整']);
            }

            $result = \app\common\Coze::sendMessage(aid, $conversationId, $botId, $message);

            \app\common\System::plog('发送扣子消息');

            return json($result);

        } catch (\Exception $e) {
            return json(['code'=>0,'msg'=>'发送消息失败: ' . $e->getMessage()]);
        }
    }

    //获取工作流运行状态
    public function getWorkflowRunStatus(){
        try {
            $runId = input('run_id','');

            if(empty($runId)){
                return json(['code'=>0,'msg'=>'运行ID不能为空']);
            }

            $result = \app\common\Coze::getWorkflowRunStatus(aid, $runId);

            \app\common\System::plog('获取工作流运行状态');

            return json($result);

        } catch (\Exception $e) {
            return json(['code'=>0,'msg'=>'获取运行状态失败: ' . $e->getMessage()]);
        }
    }

    //代理请求
    public function proxyRequest(){
        try {
            $apiToken = input('api_token','');
            $endpoint = input('endpoint','');
            $method = input('method','POST');
            $data = input('data',[]);

            if(empty($apiToken) || empty($endpoint)){
                return json(['code'=>0,'msg'=>'参数不完整']);
            }

            $result = \app\common\Coze::proxyRequest(aid, $apiToken, $endpoint, $method, $data);

            \app\common\System::plog('代理扣子API请求');

            return json($result);

        } catch (\Exception $e) {
            return json(['code'=>0,'msg'=>'代理请求失败: ' . $e->getMessage()]);
        }
    }

    //工作流参数配置页面
    public function workflowParams(){
        $workflowId = input('param.workflow_id','');
        if(empty($workflowId)){
            if(request()->isAjax()){
                return json(['code'=>0,'msg'=>'工作流ID不能为空']);
            } else {
                echo '<script>alert("工作流ID不能为空");history.back();</script>';
                exit;
            }
        }

        if(request()->isAjax()){
            $page = input('param.page');
            $limit = input('param.limit');
            $order = 'sort_order asc, id asc';
            $where = array();
            $where[] = ['aid','=',aid];
            $where[] = ['workflow_id','=',$workflowId];

            $count = 0 + Db::name('coze_workflow_params')->where($where)->count();
            $data = Db::name('coze_workflow_params')->where($where)->page($page,$limit)->order($order)->select()->toArray();

            foreach($data as $k=>$v){
                $data[$k]['param_type_text'] = $this->getParamTypeText($v['param_type']);
                $data[$k]['is_required_text'] = $v['is_required'] ? '是' : '否';
                $data[$k]['status_text'] = $v['status'] ? '启用' : '禁用';
                $data[$k]['create_time_text'] = date('Y-m-d H:i:s', $v['create_time']);
            }
            return json(['code'=>0,'msg'=>'查询成功','count'=>$count,'data'=>$data]);
        }

        // 获取工作流信息
        $workflow = Db::name('coze_workflow')->where('aid',aid)->where('workflow_id',$workflowId)->find();
        if(!$workflow){
            return json(['code'=>0,'msg'=>'工作流不存在']);
        }

        View::assign('workflow',$workflow);
        View::assign('workflowId',$workflowId);
        return View::fetch();
    }

    //保存工作流参数配置
    public function saveWorkflowParam(){
        $info = input('post.info');
        $workflowId = input('post.workflow_id','');

        if(empty($workflowId)){
            return json(['code'=>0,'msg'=>'工作流ID不能为空']);
        }

        // 验证必填字段
        if(empty($info['param_key']) || empty($info['param_name']) || empty($info['param_type'])){
            return json(['code'=>0,'msg'=>'参数键名、显示名称和类型不能为空']);
        }

        // 检查参数键名是否重复
        $exists = Db::name('coze_workflow_params')
            ->where('aid',aid)
            ->where('workflow_id',$workflowId)
            ->where('param_key',$info['param_key']);

        if(isset($info['id']) && $info['id']){
            $exists = $exists->where('id','<>',$info['id']);
        }

        if($exists->count() > 0){
            return json(['code'=>0,'msg'=>'参数键名已存在']);
        }

        $data = [
            'aid' => aid,
            'workflow_id' => $workflowId,
            'param_key' => $info['param_key'],
            'param_name' => $info['param_name'],
            'param_type' => $info['param_type'],
            'param_options' => $info['param_options'] ?? '',
            'default_value' => $info['default_value'] ?? '',
            'placeholder' => $info['placeholder'] ?? '',
            'description' => $info['description'] ?? '',
            'is_required' => isset($info['is_required']) ? 1 : 0,
            'validation_rule' => $info['validation_rule'] ?? '',
            'sort_order' => intval($info['sort_order'] ?? 0),
            'status' => isset($info['status']) ? 1 : 0,
            'update_time' => time()
        ];

        if(isset($info['id']) && $info['id']){
            // 更新
            $result = Db::name('coze_workflow_params')->where('aid',aid)->where('id',$info['id'])->update($data);
            \app\common\System::plog('编辑工作流参数配置');
        } else {
            // 新增
            $data['create_time'] = time();
            $result = Db::name('coze_workflow_params')->insert($data);
            \app\common\System::plog('新增工作流参数配置');
        }

        if($result){
            return json(['code'=>1,'msg'=>'保存成功']);
        } else {
            return json(['code'=>0,'msg'=>'保存失败']);
        }
    }

    //删除工作流参数配置
    public function delWorkflowParam(){
        $id = input('param.id/d');
        if(!$id){
            return json(['code'=>0,'msg'=>'参数错误']);
        }

        $param = Db::name('coze_workflow_params')->where('aid',aid)->where('id',$id)->find();
        if(!$param){
            return json(['code'=>0,'msg'=>'参数配置不存在']);
        }

        $result = Db::name('coze_workflow_params')->where('aid',aid)->where('id',$id)->delete();

        if($result){
            \app\common\System::plog('删除工作流参数配置');
            return json(['code'=>1,'msg'=>'删除成功']);
        } else {
            return json(['code'=>0,'msg'=>'删除失败']);
        }
    }

    //获取工作流参数配置
    public function getWorkflowParams(){
        $workflowId = input('param.workflow_id','');
        if(empty($workflowId)){
            return json(['code'=>0,'msg'=>'工作流ID不能为空']);
        }

        // 获取工作流信息
        $workflow = Db::name('coze_workflow')
            ->where('aid',aid)
            ->where('workflow_id',$workflowId)
            ->where('status',1)
            ->find();

        if(!$workflow){
            return json(['code'=>0,'msg'=>'工作流不存在']);
        }

        // 获取参数配置
        $params = Db::name('coze_workflow_params')
            ->where('aid',aid)
            ->where('workflow_id',$workflowId)
            ->where('status',1)
            ->order('sort_order asc, id asc')
            ->select()
            ->toArray();

        foreach($params as $k=>$v){
            if($v['param_options']){
                $params[$k]['param_options'] = json_decode($v['param_options'], true);
            }
        }

        // 判断参数配置模式
        $mode = 'json'; // 默认JSON模式
        if($workflow['param_config_mode'] == 1 && count($params) > 0){
            $mode = 'custom'; // 自定义字段模式
        }

        return json([
            'code'=>1,
            'msg'=>'获取成功',
            'data'=>[
                'workflow' => $workflow,
                'params' => $params,
                'mode' => $mode
            ]
        ]);
    }

    //获取参数类型文本
    private function getParamTypeText($type){
        $types = [
            'text' => '文本',
            'number' => '数字',
            'select' => '选择',
            'textarea' => '多行文本',
            'file' => '文件',
            'image' => '图片',
            'date' => '日期',
            'switch' => '开关'
        ];
        return $types[$type] ?? $type;
    }
}
