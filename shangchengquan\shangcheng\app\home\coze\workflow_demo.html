<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>扣子工作流演示</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  {include file="public/css"/}
  <style>
    .workflow-card {
      margin-bottom: 20px;
      border: 1px solid #e6e6e6;
      border-radius: 4px;
      padding: 15px;
      background-color: #fff;
    }
    .workflow-card:hover {
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .workflow-title {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      margin-bottom: 8px;
    }
    .workflow-id {
      font-size: 12px;
      color: #999;
      margin-bottom: 8px;
    }
    .workflow-desc {
      font-size: 14px;
      color: #666;
      margin-bottom: 15px;
      line-height: 1.5;
    }
    .workflow-actions {
      text-align: right;
    }
    .result-container {
      height: 400px;
      overflow-y: auto;
      border: 1px solid #e6e6e6;
      padding: 15px;
      background-color: #f9f9f9;
      border-radius: 4px;
      margin-top: 15px;
    }
    .async-controls {
      margin-top: 10px;
      padding: 10px;
      background-color: #f0f9ff;
      border-radius: 4px;
      border-left: 4px solid #1890ff;
    }
  </style>
</head>
<body>
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-header">
            <i class="fa fa-cogs"></i> 扣子工作流演示
            <span class="layui-badge layui-bg-blue" style="margin-left: 10px;">支持异步执行</span>
          </div>
          <div class="layui-card-body">
            {if condition="$workflows"}
            <div class="layui-row layui-col-space15">
              {volist name="workflows" id="workflow"}
              <div class="layui-col-md6">
                <div class="workflow-card">
                  <div class="workflow-title">{$workflow.name}</div>
                  <div class="workflow-id">工作流ID: {$workflow.workflow_id}</div>
                  <div class="workflow-desc">{$workflow.description|default='暂无描述'}</div>
                  <div class="workflow-actions">
                    <button type="button" class="layui-btn layui-btn-sm" onclick="runWorkflow('{$workflow.workflow_id}', '{$workflow.name}', false)">
                      <i class="fa fa-play"></i> 同步执行
                    </button>
                    <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" onclick="runWorkflow('{$workflow.workflow_id}', '{$workflow.name}', true)">
                      <i class="fa fa-clock-o"></i> 异步执行
                    </button>
                    <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" onclick="showWorkflowParams('{$workflow.workflow_id}', '{$workflow.name}')">
                      <i class="fa fa-cog"></i> 自定义参数
                    </button>
                  </div>
                </div>
              </div>
              {/volist}
            </div>
            {else /}
            <div class="layui-empty">
              <div class="layui-empty-icon">
                <i class="fa fa-exclamation-triangle" style="font-size: 48px; color: #ccc;"></i>
              </div>
              <p class="layui-empty-text">暂无可用的工作流</p>
              <div class="layui-empty-btn">
                <a href="{:url('workflow')}" class="layui-btn layui-btn-primary">
                  <i class="fa fa-plus"></i> 添加工作流
                </a>
              </div>
            </div>
            {/if}
          </div>
        </div>
      </div>
      
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-header">
            <i class="fa fa-terminal"></i> 执行结果
            <div style="float: right;">
              <button type="button" class="layui-btn layui-btn-xs layui-btn-primary" onclick="clearResults()">
                <i class="fa fa-trash"></i> 清空结果
              </button>
            </div>
          </div>
          <div class="layui-card-body">
            <div class="result-container" id="result-container">
              <div style="text-align: center; color: #999; padding: 50px 0;">
                <i class="fa fa-info-circle" style="font-size: 24px;"></i>
                <p style="margin-top: 10px;">点击上方工作流按钮开始执行</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 自定义参数弹窗 -->
  <div id="params-dialog" style="display: none; padding: 20px;">
    <form class="layui-form" lay-filter="params-form">
      <div class="layui-form-item">
        <label class="layui-form-label">工作流名称</label>
        <div class="layui-input-block">
          <input type="text" id="dialog-workflow-name" readonly class="layui-input">
        </div>
      </div>
      <div class="layui-form-item">
        <label class="layui-form-label">工作流ID</label>
        <div class="layui-input-block">
          <input type="text" id="dialog-workflow-id" readonly class="layui-input">
        </div>
      </div>
      <div class="layui-form-item">
        <label class="layui-form-label">执行方式</label>
        <div class="layui-input-block">
          <input type="radio" name="exec_type" value="sync" title="同步执行" checked>
          <input type="radio" name="exec_type" value="async" title="异步执行">
        </div>
      </div>
      <!-- JSON参数输入容器 -->
      <div id="json-params-container">
        <div class="layui-form-item layui-form-text">
          <label class="layui-form-label">参数JSON</label>
          <div class="layui-input-block">
            <textarea id="dialog-parameters" placeholder="请输入JSON格式的参数，例如：{&quot;input&quot;: &quot;测试内容&quot;}" class="layui-textarea" rows="8">{}</textarea>
          </div>
        </div>
      </div>

      <!-- 动态参数表单容器 -->
      <div id="dynamic-params-container" style="display: none;">
        <div class="layui-form-item">
          <label class="layui-form-label">参数配置</label>
          <div class="layui-input-block">
            <div id="dynamic-form-fields"></div>
          </div>
        </div>
      </div>
      <div class="layui-form-item">
        <div class="layui-input-block">
          <button type="button" class="layui-btn" onclick="executeWorkflow()">
            <i class="fa fa-play"></i> 执行工作流
          </button>
          <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
        </div>
      </div>
    </form>
  </div>

  {include file="public/js"/}
  <script>
  layui.use(['form', 'layer', 'element'], function(){
    var form = layui.form;
    var layer = layui.layer;
    var $ = layui.jquery;
    var element = layui.element;
    
    // 当前异步执行的任务
    var asyncTasks = {};
    
    // 运行工作流
    window.runWorkflow = function(workflowId, workflowName, isAsync) {
      var parameters = {};

      // 为特定工作流设置默认参数
      if (workflowId === '7519352650686890038') {
        parameters = {
          'BOT_USER_INPUT': '',
          'gender': '男',
          'image_url': 'https://p9-bot-workflow-sign.byteimg.com/tos-cn-i-mdko3gqilj/3a2b90b00c3847b789552acd74616d11.jpg~tplv-mdko3gqilj-image.image?rk3s=81d4c505&x-expires=1781840170&x-signature=MpZX%2FHwnljK5BWi025xiPjzXEqM%3D&x-wf-file_name=%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250526170910.jpg',
          'zhiye': '医生'
        };
      }

      executeWorkflowRequest(workflowId, workflowName, parameters, isAsync);
    };
    
    // 显示自定义参数弹窗
    window.showWorkflowParams = function(workflowId, workflowName) {
      $('#dialog-workflow-id').val(workflowId);
      $('#dialog-workflow-name').val(workflowName);

      // 获取工作流参数配置
      $.get('{:url("getWorkflowParams")}/workflow_id/' + workflowId, function(res) {
        if (res.code === 1) {
          var workflow = res.data.workflow;
          var params = res.data.params;
          var mode = res.data.mode;

          // 根据参数配置模式显示不同的界面
          if (mode === 'custom' && params.length > 0) {
            // 自定义字段模式：生成动态表单
            generateDynamicForm(params);
            $('#json-params-container').hide();
            $('#dynamic-params-container').show();
          } else {
            // JSON模式：显示JSON输入框
            $('#dynamic-params-container').hide();
            $('#json-params-container').show();

            // 设置默认参数
            var defaultParams = '{}';
            if (workflow.default_params) {
              defaultParams = workflow.default_params;
            } else if (workflowId === '7519352650686890038') {
              // 兼容旧的硬编码参数
              defaultParams = JSON.stringify({
                'BOT_USER_INPUT': '',
                'gender': '男',
                'image_url': 'https://p9-bot-workflow-sign.byteimg.com/tos-cn-i-mdko3gqilj/3a2b90b00c3847b789552acd74616d11.jpg~tplv-mdko3gqilj-image.image?rk3s=81d4c505&x-expires=1781840170&x-signature=MpZX%2FHwnljK5BWi025xiPjzXEqM%3D&x-wf-file_name=%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250526170910.jpg',
                'zhiye': '医生'
              }, null, 2);
            }
            $('#dialog-parameters').val(defaultParams);
          }
        } else {
          // 获取参数配置失败，使用JSON模式
          $('#dynamic-params-container').hide();
          $('#json-params-container').show();
          $('#dialog-parameters').val('{}');
        }
      });

      layer.open({
        type: 1,
        title: '自定义工作流参数: ' + workflowName,
        content: $('#params-dialog'),
        area: ['700px', '600px'],
        btn: false,
        success: function(layero, index) {
          form.render();
        }
      });
    };
    
    // 生成动态表单
    window.generateDynamicForm = function(params) {
      var formHtml = '';

      params.forEach(function(param) {
        var required = param.is_required ? ' <span style="color: red;">*</span>' : '';
        var placeholder = param.placeholder || '请输入' + param.param_name;

        formHtml += '<div class="layui-form-item">';
        formHtml += '<label class="layui-form-label">' + param.param_name + required + '</label>';
        formHtml += '<div class="layui-input-block">';

        switch(param.param_type) {
          case 'text':
            formHtml += '<input type="text" name="' + param.param_key + '" placeholder="' + placeholder + '" class="layui-input" value="' + (param.default_value || '') + '">';
            break;
          case 'number':
            formHtml += '<input type="number" name="' + param.param_key + '" placeholder="' + placeholder + '" class="layui-input" value="' + (param.default_value || '') + '">';
            break;
          case 'textarea':
            formHtml += '<textarea name="' + param.param_key + '" placeholder="' + placeholder + '" class="layui-textarea">' + (param.default_value || '') + '</textarea>';
            break;
          case 'select':
            formHtml += '<select name="' + param.param_key + '" lay-filter="' + param.param_key + '">';
            if (param.param_options && param.param_options.options) {
              param.param_options.options.forEach(function(option) {
                var selected = option.value === param.default_value ? ' selected' : '';
                formHtml += '<option value="' + option.value + '"' + selected + '>' + option.label + '</option>';
              });
            }
            formHtml += '</select>';
            break;
          case 'switch':
            var checked = param.default_value ? ' checked' : '';
            formHtml += '<input type="checkbox" name="' + param.param_key + '" lay-skin="switch" lay-text="是|否"' + checked + '>';
            break;
          case 'date':
            formHtml += '<input type="text" name="' + param.param_key + '" placeholder="' + placeholder + '" class="layui-input" id="date_' + param.param_key + '" value="' + (param.default_value || '') + '">';
            break;
          default:
            formHtml += '<input type="text" name="' + param.param_key + '" placeholder="' + placeholder + '" class="layui-input" value="' + (param.default_value || '') + '">';
        }

        if (param.description) {
          formHtml += '<div class="layui-form-mid layui-word-aux">' + param.description + '</div>';
        }

        formHtml += '</div>';
        formHtml += '</div>';
      });

      $('#dynamic-form-fields').html(formHtml);
      form.render();

      // 初始化日期选择器
      params.forEach(function(param) {
        if (param.param_type === 'date') {
          layui.use('laydate', function(){
            var laydate = layui.laydate;
            laydate.render({
              elem: '#date_' + param.param_key
            });
          });
        }
      });
    };

    // 收集动态表单数据
    window.collectDynamicFormData = function() {
      var formData = {};
      $('#dynamic-form-fields input, #dynamic-form-fields textarea, #dynamic-form-fields select').each(function() {
        var name = $(this).attr('name');
        var value = $(this).val();

        if ($(this).attr('type') === 'checkbox') {
          value = $(this).prop('checked');
        }

        if (name) {
          formData[name] = value;
        }
      });
      return formData;
    };

    // 执行工作流
    window.executeWorkflow = function() {
      var workflowId = $('#dialog-workflow-id').val();
      var workflowName = $('#dialog-workflow-name').val();
      var execType = $('input[name="exec_type"]:checked').val();
      var parameters;

      // 根据当前显示的表单类型收集参数
      if ($('#dynamic-params-container').is(':visible')) {
        // 自定义字段模式：收集动态表单数据
        parameters = collectDynamicFormData();
      } else {
        // JSON模式：解析JSON参数
        var parametersStr = $('#dialog-parameters').val().trim();
        try {
          if(parametersStr) {
            parameters = JSON.parse(parametersStr);
          } else {
            parameters = {};
          }
        } catch(e) {
          layer.msg('参数格式错误，请输入有效的JSON格式', {icon: 2});
          return;
        }
      }

      layer.closeAll();
      executeWorkflowRequest(workflowId, workflowName, parameters, execType === 'async');
    };
    
    // 执行工作流请求
    function executeWorkflowRequest(workflowId, workflowName, parameters, isAsync) {
      var timestamp = new Date().toLocaleString();
      var taskId = 'task_' + Date.now();
      
      // 添加执行开始日志
      addResult('info', timestamp, '开始执行工作流: ' + workflowName + ' (' + (isAsync ? '异步' : '同步') + ')');
      addResult('info', timestamp, '工作流ID: ' + workflowId);
      addResult('info', timestamp, '参数: ' + JSON.stringify(parameters));
      
      var loading = layer.load(1, {shade: [0.1,'#fff']});
      
      $.ajax({
        url: '{:url("runWorkflowDemo")}',
        type: 'POST',
        dataType: 'json',
        data: {
          workflow_id: workflowId,
          parameters: JSON.stringify(parameters),
          custom_params: parameters,
          is_async: isAsync
        },
        success: function(res) {
          layer.close(loading);
          var timestamp = new Date().toLocaleString();
          
          if(res.code === 1) {
            addResult('success', timestamp, '工作流执行成功');

            if(res.data) {
              // 显示执行结果
              addResult('data', timestamp, '执行结果: ' + JSON.stringify(res.data, null, 2));

              if(isAsync) {
                // 异步执行：开始轮询状态
                if(res.data.execute_id) {
                  var executeId = res.data.execute_id;
                  addResult('info', timestamp, '异步执行ID: ' + executeId);

                  // 保存异步任务信息
                  asyncTasks[taskId] = {
                    workflowId: workflowId,
                    executeId: executeId,
                    workflowName: workflowName,
                    startTime: timestamp
                  };

                  // 添加异步控制面板
                  addAsyncControls(taskId, executeId, workflowName);

                  // 开始轮询状态
                  startPolling(taskId);
                }
              } else {
                // 同步执行：直接显示最终结果
                if(res.data.execute_id) {
                  addResult('info', timestamp, '执行ID: ' + res.data.execute_id);
                }
                if(res.data.debug_url) {
                  addResult('info', timestamp, '调试URL: ' + res.data.debug_url);
                }

                // 尝试解析输出结果
                if(res.data.output) {
                  try {
                    var output = JSON.parse(res.data.output);
                    addResult('success', timestamp, '工作流输出: ' + JSON.stringify(output, null, 2));
                  } catch(e) {
                    addResult('success', timestamp, '工作流输出: ' + res.data.output);
                  }
                }
              }
            }
          } else {
            addResult('error', timestamp, '工作流执行失败: ' + (res.msg || '未知错误'));
          }
        },
        error: function(xhr, status, error) {
          layer.close(loading);
          var timestamp = new Date().toLocaleString();
          addResult('error', timestamp, '请求失败: ' + error);
          
          if(xhr.responseText) {
            try {
              var response = JSON.parse(xhr.responseText);
              addResult('error', timestamp, '错误详情: ' + JSON.stringify(response, null, 2));
            } catch(e) {
              addResult('error', timestamp, '错误详情: ' + xhr.responseText);
            }
          }
        }
      });
    }
    
    // 开始轮询异步任务状态
    function startPolling(taskId) {
      var task = asyncTasks[taskId];
      if(!task) return;
      
      var pollInterval = setInterval(function() {
        if(!asyncTasks[taskId]) {
          clearInterval(pollInterval);
          return;
        }
        
        $.ajax({
          url: '{:url("queryWorkflowResult")}',
          type: 'POST',
          dataType: 'json',
          data: {
            workflow_id: task.workflowId,
            execute_id: task.executeId
          },
          success: function(res) {
            var timestamp = new Date().toLocaleString();

            if(res.code === 1 && res.data) {
              // 解析Coze API响应结构
              var cozeData = null;
              var status = 'unknown';

              // 检查响应结构：{code: 1, data: {data: [{execute_status: "Success"}]}}
              if(res.data.data && Array.isArray(res.data.data) && res.data.data.length > 0) {
                cozeData = res.data.data[0];
                status = cozeData.execute_status || 'unknown';
              } else if(res.data.execute_status) {
                cozeData = res.data;
                status = res.data.execute_status;
              }

              // 转换状态值
              var displayStatus = status;
              if(status.toLowerCase() === 'success') {
                displayStatus = 'completed';
              } else if(status.toLowerCase() === 'running') {
                displayStatus = 'running';
              } else if(status.toLowerCase() === 'failed' || status.toLowerCase() === 'error') {
                displayStatus = 'failed';
              }

              updateAsyncStatus(taskId, displayStatus);

              if(displayStatus === 'completed') {
                addResult('success', timestamp, '异步任务完成: ' + task.workflowName);
                if(cozeData && cozeData.output) {
                  try {
                    var output = JSON.parse(cozeData.output);
                    addResult('success', timestamp, '工作流输出: ' + JSON.stringify(output, null, 2));
                  } catch(e) {
                    addResult('success', timestamp, '工作流输出: ' + cozeData.output);
                  }
                }
                addResult('data', timestamp, '完整结果: ' + JSON.stringify(cozeData, null, 2));
                clearInterval(pollInterval);
                delete asyncTasks[taskId];
              } else if(displayStatus === 'failed') {
                addResult('error', timestamp, '异步任务失败: ' + task.workflowName);
                if(cozeData && cozeData.error_message) {
                  addResult('error', timestamp, '失败原因: ' + cozeData.error_message);
                }
                addResult('error', timestamp, '错误详情: ' + JSON.stringify(cozeData, null, 2));
                clearInterval(pollInterval);
                delete asyncTasks[taskId];
              } else {
                addResult('info', timestamp, '异步任务状态: ' + displayStatus + ' (原始: ' + status + ')');
              }
            } else {
              addResult('warning', timestamp, '查询响应格式异常: ' + JSON.stringify(res, null, 2));
            }
          },
          error: function() {
            // 静默处理轮询错误，避免过多错误信息
          }
        });
      }, 3000); // 每3秒查询一次
      
      // 设置最大轮询时间（5分钟）
      setTimeout(function() {
        if(asyncTasks[taskId]) {
          clearInterval(pollInterval);
          var timestamp = new Date().toLocaleString();
          addResult('warning', timestamp, '异步任务轮询超时: ' + task.workflowName);
          delete asyncTasks[taskId];
        }
      }, 300000);
    }
    
    // 添加异步控制面板
    function addAsyncControls(taskId, executeId, workflowName) {
      var html = '<div class="async-controls" id="async-' + taskId + '">' +
                 '<div style="display: flex; justify-content: space-between; align-items: center;">' +
                 '<div>' +
                 '<strong>异步任务: ' + workflowName + '</strong>' +
                 '<span style="margin-left: 10px; color: #1890ff;" id="status-' + taskId + '">运行中...</span>' +
                 '</div>' +
                 '<div>' +
                 '<button type="button" class="layui-btn layui-btn-xs" onclick="queryTaskStatus(\'' + taskId + '\')">查询状态</button>' +
                 '<button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="stopTask(\'' + taskId + '\')">停止监控</button>' +
                 '</div>' +
                 '</div>' +
                 '<div style="margin-top: 5px; font-size: 12px; color: #666;">执行ID: ' + executeId + '</div>' +
                 '</div>';
      
      $('#result-container').append(html);
      scrollToBottom();
    }
    
    // 更新异步任务状态
    function updateAsyncStatus(taskId, status) {
      var statusText = status;
      var statusColor = '#1890ff';
      
      switch(status) {
        case 'completed':
          statusText = '已完成';
          statusColor = '#52c41a';
          break;
        case 'failed':
          statusText = '已失败';
          statusColor = '#ff4d4f';
          break;
        case 'running':
          statusText = '运行中';
          statusColor = '#1890ff';
          break;
      }
      
      $('#status-' + taskId).text(statusText).css('color', statusColor);
    }
    
    // 查询任务状态
    window.queryTaskStatus = function(taskId) {
      var task = asyncTasks[taskId];
      if(!task) {
        layer.msg('任务不存在', {icon: 0});
        return;
      }
      
      var loading = layer.load(1, {shade: [0.1,'#fff']});
      
      $.ajax({
        url: '{:url("queryWorkflowResult")}',
        type: 'POST',
        dataType: 'json',
        data: {
          workflow_id: task.workflowId,
          execute_id: task.executeId
        },
        success: function(res) {
          layer.close(loading);
          var timestamp = new Date().toLocaleString();
          
          if(res.code === 1) {
            addResult('info', timestamp, '手动查询状态: ' + task.workflowName);
            addResult('data', timestamp, '状态结果: ' + JSON.stringify(res.data, null, 2));
            
            if(res.data && res.data.status) {
              updateAsyncStatus(taskId, res.data.status);
            }
          } else {
            addResult('error', timestamp, '查询状态失败: ' + (res.msg || '未知错误'));
          }
        },
        error: function() {
          layer.close(loading);
          layer.msg('查询失败', {icon: 2});
        }
      });
    };
    
    // 停止任务监控
    window.stopTask = function(taskId) {
      if(asyncTasks[taskId]) {
        delete asyncTasks[taskId];
        $('#async-' + taskId).remove();
        var timestamp = new Date().toLocaleString();
        addResult('warning', timestamp, '已停止监控异步任务');
      }
    };
    
    // 添加结果到容器
    function addResult(type, timestamp, content) {
      var iconClass = '';
      var colorClass = '';
      
      switch(type) {
        case 'success':
          iconClass = 'fa-check-circle';
          colorClass = 'color: #52c41a;';
          break;
        case 'error':
          iconClass = 'fa-times-circle';
          colorClass = 'color: #ff4d4f;';
          break;
        case 'warning':
          iconClass = 'fa-exclamation-triangle';
          colorClass = 'color: #faad14;';
          break;
        case 'info':
          iconClass = 'fa-info-circle';
          colorClass = 'color: #1890ff;';
          break;
        case 'data':
          iconClass = 'fa-code';
          colorClass = 'color: #722ed1;';
          break;
      }
      
      var html = '<div style="margin-bottom: 10px; padding: 8px; border-left: 3px solid #e6e6e6; background-color: #fafafa;">' +
                 '<div style="display: flex; align-items: flex-start;">' +
                 '<i class="fa ' + iconClass + '" style="' + colorClass + ' margin-right: 8px; margin-top: 2px;"></i>' +
                 '<div style="flex: 1;">' +
                 '<div style="font-size: 12px; color: #999; margin-bottom: 4px;">' + timestamp + '</div>' +
                 '<div style="' + (type === 'data' ? 'font-family: monospace; white-space: pre-wrap; background-color: #f6f8fa; padding: 8px; border-radius: 3px; font-size: 12px;' : '') + '">' + content + '</div>' +
                 '</div>' +
                 '</div>' +
                 '</div>';
      
      $('#result-container').append(html);
      scrollToBottom();
    }
    
    // 滚动到底部
    function scrollToBottom() {
      var container = document.getElementById('result-container');
      container.scrollTop = container.scrollHeight;
    }
    
    // 清空结果
    window.clearResults = function() {
      $('#result-container').html('<div style="text-align: center; color: #999; padding: 50px 0;">' +
                                  '<i class="fa fa-info-circle" style="font-size: 24px;"></i>' +
                                  '<p style="margin-top: 10px;">点击上方工作流按钮开始执行</p>' +
                                  '</div>');
      
      // 清空所有异步任务
      asyncTasks = {};
    };
  });
  </script>
  {include file="public/copyright"/}
</body>
</html>
