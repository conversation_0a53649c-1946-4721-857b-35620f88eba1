<view class="container"><block wx:if="{{isload}}"><block><block wx:if="{{logintype==1}}"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="title">用户登录</view><view class="loginform"><view class="form-item"><image class="img" src="/static/img/reg-tel.png"></image><input class="input" type="text" placeholder="请输入手机号" placeholder-style="font-size:30rpx;color:#B2B5BE" name="tel" value="" data-event-opts="{{[['input',[['telinput',['$event']]]]]}}" bindinput="__e"/></view><view class="form-item"><image class="img" src="/static/img/reg-pwd.png"></image><input class="input" type="text" placeholder="请输入密码" placeholder-style="font-size:30rpx;color:#B2B5BE" name="pwd" value="" password="{{true}}"/></view><block wx:if="{{xystatus==1}}"><view class="xieyi-item"><checkbox-group data-event-opts="{{[['change',[['isagreeChange',['$event']]]]]}}" bindchange="__e"><label class="flex-y-center"><checkbox class="checkbox" value="1" checked="{{isagree}}"></checkbox>我已阅读并同意</label></checkbox-group><text data-event-opts="{{[['tap',[['showxieyiFun',['$event']]]]]}}" style="{{'color:'+($root.m0)+';'}}" bindtap="__e">{{xyname}}</text><block wx:if="{{xyname2}}"><text data-event-opts="{{[['tap',[['showxieyiFun',['$event']]]]]}}" bindtap="__e">和</text></block><block wx:if="{{xyname2}}"><text data-event-opts="{{[['tap',[['showxieyiFun2',['$event']]]]]}}" style="{{'color:'+($root.m1)+';'}}" bindtap="__e">{{xyname2}}</text></block></view></block><button class="form-btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m2+' 0%,rgba('+$root.m3+',0.8) 100%)')+';'}}" form-type="submit">登录</button><block wx:if="{{!login_mast}}"><button data-event-opts="{{[['tap',[['goback',['$event']]]]]}}" class="form-btn2" bindtap="__e">暂不登录</button></block><block wx:if="{{platform2=='ios'&&logintype_4==true}}"><button data-event-opts="{{[['tap',[['ioslogin',['$event']]]]]}}" class="ioslogin-btn" style="width:100%;" bindtap="__e"><image src="/static/images/apple.png"></image>通过Apple登录</button></block></view></form><view class="regtip"><view data-url="reg" data-opentype="redirect" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">注册账号</view><view class="flex1"></view><block wx:if="{{needsms}}"><view data-url="getpwd" data-opentype="redirect" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">忘记密码？</view></block></view><block wx:if="{{logintype_2||logintype_3}}"><block><view class="othertip"><view class="othertip-line"></view><view class="othertip-text"><text class="txt">其他方式登录</text></view><view class="othertip-line"></view></view><view class="othertype"><block wx:if="{{logintype_3}}"><view data-event-opts="{{[['tap',[['weixinlogin',['$event']]]]]}}" class="othertype-item" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/login-'+platformimg+'.png'}}"></image><text class="txt">{{platformname+"登录"}}</text></view></block><block wx:if="{{logintype_2}}"><view class="othertype-item" data-type="2" data-event-opts="{{[['tap',[['changelogintype',['$event']]]]]}}" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/reg-tellogin.png'}}"></image><text class="txt">手机号登录</text></view></block></view></block></block></block></block><block wx:if="{{logintype==2}}"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="title">用户登录</view><view class="loginform"><view class="form-item"><image class="img" src="/static/img/reg-tel.png"></image><input class="input" type="text" placeholder="请输入手机号" placeholder-style="font-size:30rpx;color:#B2B5BE" name="tel" value="" data-event-opts="{{[['input',[['telinput',['$event']]]]]}}" bindinput="__e"/></view><view class="form-item"><image class="img" src="/static/img/reg-code.png"></image><input class="input" type="text" placeholder="请输入验证码" placeholder-style="font-size:30rpx;color:#B2B5BE" name="smscode" value=""/><view data-event-opts="{{[['tap',[['smscode',['$event']]]]]}}" class="code" style="{{'color:'+($root.m4)+';'}}" bindtap="__e">{{smsdjs||'获取验证码'}}</view></view><block wx:if="{{reg_invite_code&&!parent}}"><view class="form-item"><image class="img" src="/static/img/reg-yqcode.png"></image><input class="input" type="text" placeholder="{{'请输入邀请人'+reg_invite_code_text+'(选填)'}}" placeholder-style="font-size:30rpx;color:#B2B5BE" name="yqcode" value=""/></view></block><block wx:if="{{reg_invite_code&&parent}}"><view class="form-item" style="color:#333;">邀请人：<image style="width:80rpx;height:80rpx;border-radius:50%;" src="{{parent.headimg}}"></image>{{''+parent.nickname+''}}</view></block><block wx:if="{{xystatus==1}}"><view class="xieyi-item"><checkbox-group data-event-opts="{{[['change',[['isagreeChange',['$event']]]]]}}" bindchange="__e"><label class="flex-y-center"><checkbox class="checkbox" value="1" checked="{{isagree}}"></checkbox>我已阅读并同意</label></checkbox-group><text data-event-opts="{{[['tap',[['showxieyiFun',['$event']]]]]}}" style="{{'color:'+($root.m5)+';'}}" bindtap="__e">{{xyname}}</text><block wx:if="{{xyname2}}"><text data-event-opts="{{[['tap',[['showxieyiFun',['$event']]]]]}}" bindtap="__e">和</text></block><block wx:if="{{xyname2}}"><text data-event-opts="{{[['tap',[['showxieyiFun2',['$event']]]]]}}" style="{{'color:'+($root.m6)+';'}}" bindtap="__e">{{xyname2}}</text></block></view></block><button class="form-btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m7+' 0%,rgba('+$root.m8+',0.8) 100%)')+';'}}" form-type="submit">登录</button><block wx:if="{{!login_mast}}"><button data-event-opts="{{[['tap',[['goback',['$event']]]]]}}" class="form-btn2" bindtap="__e">暂不登录</button></block><block wx:if="{{platform2=='ios'&&logintype_4==true}}"><button data-event-opts="{{[['tap',[['ioslogin',['$event']]]]]}}" class="ioslogin-btn" style="width:100%;" bindtap="__e"><image src="/static/images/apple.png"></image>通过Apple登录</button></block></view></form><block wx:if="{{logintype_1||logintype_3}}"><block><view class="othertip"><view class="othertip-line"></view><view class="othertip-text"><text class="txt">其他方式登录</text></view><view class="othertip-line"></view></view><view class="othertype"><block wx:if="{{logintype_3}}"><view data-event-opts="{{[['tap',[['weixinlogin',['$event']]]]]}}" class="othertype-item" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/login-'+platformimg+'.png'}}"></image><text class="txt">{{platformname+"登录"}}</text></view></block><block wx:if="{{logintype_1}}"><view class="othertype-item" data-type="1" data-event-opts="{{[['tap',[['changelogintype',['$event']]]]]}}" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/reg-tellogin.png'}}"></image><text class="txt">密码登录</text></view></block></view></block></block></block></block><block wx:if="{{logintype==3}}"><block><view class="authlogin"><view class="authlogin-logo"><image style="width:100%;height:100%;" src="{{logo}}"></image></view><view class="authlogin-name">{{"授权登录"+name}}</view><button data-event-opts="{{[['tap',[['authlogin',['$event']]]]]}}" class="authlogin-btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m9+' 0%,rgba('+$root.m10+',0.8) 100%)')+';'}}" bindtap="__e">{{platformname+"授权登录"}}</button><block wx:if="{{!login_mast}}"><button data-event-opts="{{[['tap',[['goback',['$event']]]]]}}" class="authlogin-btn2" bindtap="__e">暂不登录</button></block><block wx:if="{{platform2=='ios'&&logintype_4==true}}"><button data-event-opts="{{[['tap',[['ioslogin',['$event']]]]]}}" class="ioslogin-btn" bindtap="__e"><image src="/static/images/apple.png"></image>通过Apple登录</button></block><block wx:if="{{xystatus==1}}"><view class="xieyi-item"><checkbox-group data-event-opts="{{[['change',[['isagreeChange',['$event']]]]]}}" bindchange="__e"><label class="flex-y-center"><checkbox class="checkbox" value="1" checked="{{isagree}}"></checkbox>我已阅读并同意</label></checkbox-group><text data-event-opts="{{[['tap',[['showxieyiFun',['$event']]]]]}}" style="{{'color:'+($root.m11)+';'}}" bindtap="__e">{{xyname}}</text><block wx:if="{{xyname2}}"><text data-event-opts="{{[['tap',[['showxieyiFun',['$event']]]]]}}" bindtap="__e">和</text></block><block wx:if="{{xyname2}}"><text data-event-opts="{{[['tap',[['showxieyiFun2',['$event']]]]]}}" style="{{'color:'+($root.m12)+';'}}" bindtap="__e">{{xyname2}}</text></block></view></block></view></block></block><block wx:if="{{logintype==4}}"><block><view class="authlogin"><view class="authlogin-logo"><image style="width:100%;height:100%;" src="{{logo}}"></image></view><view class="authlogin-name">{{"授权登录"+name}}</view><button class="authlogin-btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m13+' 0%,rgba('+$root.m14+',0.8) 100%)')+';'}}" open-type="getPhoneNumber" data-event-opts="{{[['getphonenumber',[['getPhoneNumber',['$event']]]]]}}" bindgetphonenumber="__e">{{platformname+"授权绑定手机号"}}</button><block wx:if="{{login_bind==1}}"><button data-event-opts="{{[['tap',[['nobindregister',['$event']]]]]}}" class="authlogin-btn2" bindtap="__e">暂不绑定</button></block></view></block></block><block wx:if="{{logintype==5}}"><block><form data-event-opts="{{[['submit',[['setnicknameregister',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="title">请设置头像昵称</view><view class="loginform"><view class="form-item" style="height:120rpx;line-height:120rpx;"><view class="flex1">头像</view><button style="width:100rpx;height:100rpx;" open-type="chooseAvatar" data-event-opts="{{[['chooseavatar',[['onChooseAvatar',['$event']]]]]}}" bindchooseavatar="__e"><image style="width:100%;height:100%;border-radius:50%;" src="{{headimg||default_headimg}}"></image></button></view><view class="form-item" style="height:120rpx;line-height:120rpx;"><view class="flex1">昵称</view><input class="input" style="text-align:right;" type="nickname" placeholder="请输入昵称" name="nickname" placeholder-style="font-size:30rpx;color:#B2B5BE"/></view><button class="form-btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m15+' 0%,rgba('+$root.m16+',0.8) 100%)')+';'}}" form-type="submit">确定</button><block wx:if="{{login_setnickname==1}}"><button data-event-opts="{{[['tap',[['nosetnicknameregister',['$event']]]]]}}" class="form-btn2" bindtap="__e">暂不设置</button></block></view></form></block></block><block wx:if="{{showxieyi}}"><view class="xieyibox"><view class="xieyibox-content"><view style="overflow:scroll;height:100%;"><parse vue-id="24611f63-1" content="{{xycontent}}" data-event-opts="{{[['^navigate',[['navigate']]]]}}" bind:navigate="__e" bind:__l="__l"></parse></view><view data-event-opts="{{[['tap',[['hidexieyi',['$event']]]]]}}" style="{{'position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center;width:50%;height:60rpx;line-height:60rpx;color:#fff;border-radius:8rpx;'+('background:'+('linear-gradient(90deg,'+$root.m17+' 0%,rgba('+$root.m18+',0.8) 100%)')+';')}}" bindtap="__e">已阅读并同意</view></view></view></block><block wx:if="{{showxieyi2}}"><view class="xieyibox"><view class="xieyibox-content"><view style="overflow:scroll;height:100%;"><parse vue-id="24611f63-2" content="{{xycontent2}}" data-event-opts="{{[['^navigate',[['navigate']]]]}}" bind:navigate="__e" bind:__l="__l"></parse></view><view data-event-opts="{{[['tap',[['hidexieyi2',['$event']]]]]}}" style="{{'position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center;width:50%;height:60rpx;line-height:60rpx;color:#fff;border-radius:8rpx;'+('background:'+('linear-gradient(90deg,'+$root.m19+' 0%,rgba('+$root.m20+',0.8) 100%)')+';')}}" bindtap="__e">已阅读并同意</view></view></view></block><block wx:if="{{showAddressConfirm}}"><view class="address-confirm-box"><view class="address-confirm-content"><view class="address-confirm-title">地址确认</view><view class="address-confirm-info"><view>{{"国家："+addressInfo.country}}</view><view>{{"省份："+addressInfo.province}}</view><view>{{"城市："+addressInfo.city}}</view><view>{{"地区："+addressInfo.area}}</view></view><view class="address-confirm-btns"><button data-event-opts="{{[['tap',[['confirmAddress',[true]]]]]}}" class="address-confirm-btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m21+' 0%,rgba('+$root.m22+',0.8) 100%)')+';'}}" bindtap="__e">确认</button><block wx:if="{{addressInfo.can_edit_address}}"><button data-event-opts="{{[['tap',[['confirmAddress',[false]]]]]}}" class="address-confirm-btn" bindtap="__e">修改</button></block></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="24611f63-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="24611f63-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="24611f63-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>