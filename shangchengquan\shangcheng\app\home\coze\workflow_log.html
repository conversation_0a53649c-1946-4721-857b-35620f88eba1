<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>工作流执行日志</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    {include file="public/css"/}
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <h3>工作流执行日志</h3>
                    </div>
                    <div class="layui-card-body">
                        <!-- 搜索区域 -->
                        <div class="layui-form layui-border-box layui-table-view" lay-filter="LAY-app-content-list">
                            <div class="layui-row layui-col-space10 layui-form-item">
                                <div class="layui-col-md3">
                                    <select name="workflow_id" lay-search="">
                                        <option value="">选择工作流</option>
                                        {volist name="workflows" id="workflow"}
                                        <option value="{$workflow.workflow_id}">{$workflow.name} ({$workflow.workflow_id})</option>
                                        {/volist}
                                    </select>
                                </div>
                                <div class="layui-col-md2">
                                    <select name="status">
                                        <option value="">全部状态</option>
                                        <option value="completed">已完成</option>
                                        <option value="running">运行中</option>
                                        <option value="failed">失败</option>
                                        <option value="unknown">未知</option>
                                    </select>
                                </div>
                                <div class="layui-col-md2">
                                    <button class="layui-btn" lay-submit lay-filter="LAY-app-content-search">
                                        <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 工具栏 -->
                        <div class="layui-row layui-col-space10 layui-form-item">
                            <div class="layui-col-md12">
                                <button class="layui-btn layui-btn-danger" id="batchDel">
                                    <i class="layui-icon layui-icon-delete"></i>批量删除
                                </button>
                                <button class="layui-btn layui-btn-warm" id="batchQuery">
                                    <i class="layui-icon layui-icon-search"></i>批量查询状态
                                </button>
                                <button class="layui-btn layui-btn-normal" id="refreshTable">
                                    <i class="layui-icon layui-icon-refresh"></i>刷新
                                </button>
                            </div>
                        </div>

                        <!-- 数据表格 -->
                        <table class="layui-hide" id="LAY-app-content-list" lay-filter="LAY-app-content-list"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 表格行工具栏 -->
    <script type="text/html" id="table-content-list">
        <a class="layui-btn layui-btn-xs" lay-event="detail">详情</a>
        {{# if(d.status == 'running' || d.status == 'Running') { }}
        <a class="layui-btn layui-btn-warm layui-btn-xs" lay-event="query">查询状态</a>
        {{# } }}
        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
    </script>

    <!-- 状态显示模板 -->
    <script type="text/html" id="status-tpl">
        <span class="layui-badge {{d.status_class}}">{{d.status_text}}</span>
    </script>

    <!-- 执行类型模板 -->
    <script type="text/html" id="async-tpl">
        <span class="layui-badge {{d.is_async ? 'layui-bg-blue' : 'layui-bg-green'}}">{{d.is_async_text}}</span>
    </script>

    <!-- 参数显示模板 -->
    <script type="text/html" id="params-tpl">
        {{# if(d.parameters_text && d.parameters_text !== '无参数') { }}
            <a href="javascript:;" lay-event="showParams" class="layui-text">查看参数</a>
        {{# } else { }}
            <span class="layui-text layui-text-muted">无参数</span>
        {{# } }}
    </script>

    <!-- 输出结果模板 -->
    <script type="text/html" id="output-tpl">
        {{# if(d.output_text && d.output_text !== '无输出' && d.output_text !== '无结果') { }}
            <a href="javascript:;" lay-event="showOutput" class="layui-text">查看输出</a>
        {{# } else { }}
            <span class="layui-text layui-text-muted">{{d.output_text}}</span>
        {{# } }}
    </script>

    <!-- 调试链接模板 -->
    <script type="text/html" id="debug-tpl">
        {{# if(d.debug_url) { }}
            <a href="{{d.debug_url}}" target="_blank" class="layui-btn layui-btn-xs layui-btn-normal">
                <i class="layui-icon layui-icon-link"></i>调试
            </a>
        {{# } else { }}
            <span class="layui-text layui-text-muted">无</span>
        {{# } }}
    </script>

    {include file="public/js"/}
    <script>
    layui.use(['table', 'form', 'layer'], function(){
        var $ = layui.$
        ,table = layui.table
        ,form = layui.form
        ,layer = layui.layer;

        // 数据表格
        var tableIns = table.render({
            elem: '#LAY-app-content-list'
            ,url: '{:url("getWorkflowHistory")}'
            ,cols: [[
                {type: 'checkbox', fixed: 'left'}
                ,{field: 'id', width: 80, title: 'ID', sort: true}
                ,{field: 'workflow_id', width: 180, title: '工作流ID'}
                ,{field: 'execute_id', width: 180, title: '执行ID'}
                ,{field: 'status', width: 100, title: '状态', templet: '#status-tpl'}
                ,{field: 'is_async', width: 80, title: '类型', templet: '#async-tpl'}
                ,{field: 'parameters', width: 120, title: '参数', templet: '#params-tpl'}
                ,{field: 'output_text', width: 120, title: '输出', templet: '#output-tpl'}
                ,{field: 'debug_url', width: 100, title: '调试', templet: '#debug-tpl'}
                ,{field: 'create_time_format', width: 160, title: '创建时间'}
                ,{field: 'update_time_format', width: 160, title: '更新时间'}
                ,{title: '操作', width: 150, align: 'center', fixed: 'right', toolbar: '#table-content-list'}
            ]]
            ,page: true
            ,limit: 20
            ,limits: [10, 20, 50, 100]
            ,text: {
                none: '暂无相关数据'
            }
        });

        // 搜索
        form.on('submit(LAY-app-content-search)', function(data){
            var field = data.field;
            tableIns.reload({
                where: field
            });
        });

        // 工具栏事件
        table.on('tool(LAY-app-content-list)', function(obj){
            var data = obj.data;
            if(obj.event === 'del'){
                layer.confirm('确定删除这条记录吗？', function(index){
                    $.post('{:url("delWorkflowLog")}', {id: data.id}, function(res){
                        if(res.code == 1){
                            layer.msg(res.msg, {icon: 1});
                            obj.del();
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    });
                    layer.close(index);
                });
            } else if(obj.event === 'detail'){
                showDetail(data);
            } else if(obj.event === 'query'){
                queryWorkflowStatus(data);
            } else if(obj.event === 'showParams'){
                showParams(data);
            } else if(obj.event === 'showOutput'){
                showOutput(data);
            }
        });

        // 批量删除
        $('#batchDel').on('click', function(){
            var checkStatus = table.checkStatus('LAY-app-content-list');
            var data = checkStatus.data;
            if(data.length === 0){
                layer.msg('请选择要删除的数据');
                return;
            }
            
            layer.confirm('确定删除选中的 ' + data.length + ' 条记录吗？', function(index){
                var ids = [];
                layui.each(data, function(index, item){
                    ids.push(item.id);
                });
                
                $.post('{:url("batchDelWorkflowLog")}', {ids: ids.join(',')}, function(res){
                    if(res.code == 1){
                        layer.msg(res.msg, {icon: 1});
                        tableIns.reload();
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                });
                layer.close(index);
            });
        });

        // 刷新表格
        $('#refreshTable').on('click', function(){
            tableIns.reload();
        });

        // 批量查询状态
        $('#batchQuery').on('click', function(){
            var loadIndex = layer.load(2, {content: '正在查询状态...'});

            $.post('{:url("batchQueryStatus")}', {}, function(res){
                layer.close(loadIndex);
                if(res.code == 1){
                    layer.msg('批量查询完成，共更新 ' + res.data.updated_count + ' 条记录', {icon: 1});
                    tableIns.reload();
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            }).fail(function(){
                layer.close(loadIndex);
                layer.msg('请求失败', {icon: 2});
            });
        });

        // 显示详情
        function showDetail(data) {
            var content = '<div style="padding: 20px;">' +
                '<div class="layui-form-item">' +
                '<label class="layui-form-label">工作流ID:</label>' +
                '<div class="layui-input-block">' + data.workflow_id + '</div>' +
                '</div>' +
                '<div class="layui-form-item">' +
                '<label class="layui-form-label">执行ID:</label>' +
                '<div class="layui-input-block">' + (data.execute_id || '无') + '</div>' +
                '</div>' +
                '<div class="layui-form-item">' +
                '<label class="layui-form-label">执行状态:</label>' +
                '<div class="layui-input-block"><span class="layui-badge ' + data.status_class + '">' + data.status_text + '</span></div>' +
                '</div>' +
                '<div class="layui-form-item">' +
                '<label class="layui-form-label">执行类型:</label>' +
                '<div class="layui-input-block">' + data.is_async_text + '</div>' +
                '</div>' +
                '<div class="layui-form-item">' +
                '<label class="layui-form-label">创建时间:</label>' +
                '<div class="layui-input-block">' + data.create_time_format + '</div>' +
                '</div>' +
                '<div class="layui-form-item">' +
                '<label class="layui-form-label">更新时间:</label>' +
                '<div class="layui-input-block">' + data.update_time_format + '</div>' +
                '</div>';
            
            if(data.debug_url) {
                content += '<div class="layui-form-item">' +
                    '<label class="layui-form-label">调试链接:</label>' +
                    '<div class="layui-input-block"><a href="' + data.debug_url + '" target="_blank">打开调试页面</a></div>' +
                    '</div>';
            }
            
            content += '</div>';
            
            layer.open({
                type: 1,
                title: '执行详情',
                content: content,
                area: ['600px', '500px'],
                btn: ['关闭'],
                yes: function(index) {
                    layer.close(index);
                }
            });
        }

        // 显示参数
        function showParams(data) {
            layer.open({
                type: 1,
                title: '执行参数',
                content: '<div style="padding: 20px;"><pre>' + data.parameters_text + '</pre></div>',
                area: ['600px', '400px'],
                btn: ['关闭'],
                yes: function(index) {
                    layer.close(index);
                }
            });
        }

        // 显示输出
        function showOutput(data) {
            layer.open({
                type: 1,
                title: '执行输出',
                content: '<div style="padding: 20px;"><pre>' + data.output_text + '</pre></div>',
                area: ['600px', '400px'],
                btn: ['关闭'],
                yes: function(index) {
                    layer.close(index);
                }
            });
        }

        // 查询工作流状态
        function queryWorkflowStatus(data) {
            if(!data.workflow_id || !data.execute_id) {
                layer.msg('缺少必要的查询参数', {icon: 2});
                return;
            }

            var loadIndex = layer.load(2, {content: '正在查询状态...'});

            $.post('{:url("queryWorkflowStatus")}', {
                id: data.id,
                workflow_id: data.workflow_id,
                execute_id: data.execute_id
            }, function(res){
                layer.close(loadIndex);
                if(res.code == 1){
                    layer.msg('状态查询成功', {icon: 1});
                    tableIns.reload(); // 刷新表格显示最新状态

                    // 显示查询结果
                    if(res.data && res.data.status) {
                        var statusText = res.data.status === 'Success' ? '已完成' :
                                       res.data.status === 'Failed' ? '失败' :
                                       res.data.status === 'Running' ? '运行中' : res.data.status;
                        layer.msg('当前状态: ' + statusText, {icon: 1, time: 3000});
                    }
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            }).fail(function(){
                layer.close(loadIndex);
                layer.msg('查询请求失败', {icon: 2});
            });
        }
    });
    </script>
</body>
</html>
