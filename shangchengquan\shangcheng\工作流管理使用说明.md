# 工作流管理使用说明

## 🎯 功能概述

工作流管理系统提供了完整的Coze工作流集成功能，包括工作流配置、参数管理、直接测试和执行日志查看。

## 📍 访问路径

- **工作流管理**：`后台管理 → 拓展栏目 → Coze API → 工作流管理`
- **执行日志**：`后台管理 → 拓展栏目 → Coze API → 执行日志`

## 🔧 主要功能

### 1. 工作流列表管理

#### 功能按钮：
- **添加工作流**：创建新的工作流配置
- **同步测试**：直接在列表页面测试工作流（同步执行）
- **异步测试**：直接在列表页面测试工作流（异步执行）
- **编辑**：修改工作流配置和参数
- **删除**：删除工作流配置

#### 测试功能：
1. 点击"同步测试"或"异步测试"按钮
2. 弹出参数输入框，自动加载配置的默认参数
3. 可以修改参数后执行
4. 同步执行立即显示结果，异步执行显示执行ID

### 2. 工作流编辑配置

#### 基本信息：
- **工作流名称**：便于识别的名称
- **工作流ID**：从Coze平台获取的工作流ID
- **描述**：工作流的详细说明

#### 参数配置：
- **默认参数**：JSON格式的默认参数配置
- **参数说明**：参数的详细说明文档
- **格式化JSON**：自动格式化参数JSON
- **验证格式**：验证JSON格式是否正确

#### 参数配置示例：
```json
{
  "BOT_USER_INPUT": "",
  "gender": "男",
  "image_url": "https://example.com/image.jpg",
  "zhiye": "医生"
}
```

#### 参数说明示例：
```
BOT_USER_INPUT: 用户输入内容
gender: 性别选择（男/女）
image_url: 图片链接
zhiye: 职业信息
```

### 3. 执行日志管理

#### 日志信息：
- **工作流ID**：执行的工作流标识
- **执行ID**：单次执行的唯一标识
- **状态**：已完成/运行中/失败/未知
- **类型**：同步/异步执行
- **参数**：执行时使用的参数
- **输出**：工作流的执行结果
- **调试链接**：Coze官方调试页面
- **时间**：创建和更新时间

#### 操作功能：
- **筛选**：按工作流ID和状态筛选
- **查看详情**：查看完整的执行信息
- **查看参数**：查看执行时的参数
- **查看输出**：查看工作流的输出结果
- **删除记录**：删除执行日志
- **批量删除**：批量清理历史记录

## 🚀 使用流程

### 快速测试流程：
1. 进入工作流管理页面
2. 找到要测试的工作流
3. 点击"同步测试"或"异步测试"
4. 在弹出框中确认或修改参数
5. 点击"执行工作流"
6. 查看执行结果

### 配置工作流流程：
1. 点击"添加工作流"或"编辑"
2. 填写基本信息（名称、ID、描述）
3. 配置默认参数（JSON格式）
4. 填写参数说明
5. 使用"格式化JSON"和"验证格式"确保参数正确
6. 保存配置

### 查看执行历史：
1. 进入执行日志页面
2. 使用筛选功能查找特定记录
3. 点击"详情"查看完整信息
4. 点击"查看输出"查看结果
5. 点击调试链接跳转到Coze平台

## 📋 注意事项

1. **参数格式**：默认参数必须是有效的JSON格式
2. **工作流ID**：必须是Coze平台的有效工作流ID
3. **同步vs异步**：
   - 同步执行：立即返回结果，适合快速测试
   - 异步执行：返回执行ID，需要轮询查看结果
4. **权限要求**：需要有效的Coze API密钥配置
5. **日志管理**：定期清理执行日志避免数据过多

## 🔧 数据库更新

如果是新安装或升级，请执行以下SQL：

```sql
-- 添加默认参数字段
ALTER TABLE `ddwx_coze_workflow` 
ADD COLUMN `default_params` TEXT COMMENT '默认参数JSON格式' AFTER `description`;

-- 添加参数说明字段
ALTER TABLE `ddwx_coze_workflow` 
ADD COLUMN `params_description` TEXT COMMENT '参数说明' AFTER `default_params`;
```

## 📞 技术支持

如有问题，请检查：
1. Coze API配置是否正确
2. 工作流ID是否有效
3. 参数格式是否正确
4. 网络连接是否正常

---

**版本**：v1.0  
**更新时间**：2025-07-31
