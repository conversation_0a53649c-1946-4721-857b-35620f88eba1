# 抽奖系统物流功能部署清单

## 🚀 部署前准备

### 1. 数据库备份
- [ ] 备份 `ddwx_choujiang_record` 表
- [ ] 备份相关配置表
- [ ] 确认备份文件完整性

### 2. 代码备份
- [ ] 备份现有抽奖相关文件
- [ ] 确认Git提交记录
- [ ] 准备回滚方案

## 📋 部署步骤

### 1. 数据库升级
```sql
-- 执行数据库升级脚本
source choujiang_logistics_upgrade.sql;

-- 验证字段是否添加成功
DESCRIBE ddwx_choujiang_record;
```

### 2. 后端文件部署
- [ ] 上传 `shangchengquan/shangcheng/app/controller/Choujiang.php`
- [ ] 上传 `shangchengquan/shangcheng/app/controller/ApiChoujiang.php`
- [ ] 上传 `shangchengquan/shangcheng/app/home/<USER>/record.html`

### 3. 前端文件部署
- [ ] 上传 `tiantianshande/activity/xydzp/myprize.vue`
- [ ] 上传 `tiantianshande/activity/choujiang/logistics.vue`
- [ ] 更新 `tiantianshande/pages.json`

### 4. 权限配置
- [ ] 确认管理员有抽奖记录管理权限
- [ ] 确认快递公司配置正确
- [ ] 确认物流查询接口可用

## ✅ 部署后验证

### 1. 数据库验证
```sql
-- 检查新字段
SELECT express_com, express_no, send_time, express_status 
FROM ddwx_choujiang_record 
LIMIT 1;
```

### 2. 后端功能验证
- [ ] 访问抽奖记录页面
- [ ] 确认显示物流信息列
- [ ] 测试发货功能
- [ ] 测试物流查询功能

### 3. 前端功能验证
- [ ] 访问我的奖品页面
- [ ] 确认显示查物流按钮
- [ ] 测试物流查询页面
- [ ] 测试页面跳转

### 4. API接口验证
```bash
# 测试发货接口
curl -X POST "域名/Choujiang/sendExpress" \
  -d "recordid=1&express_com=顺丰速运&express_no=SF1234567890"

# 测试物流查询接口
curl -X GET "域名/ApiChoujiang/getExpress?recordid=1"
```

## 🔧 配置检查

### 1. 快递公司配置
- [ ] 确认 `express_data()` 函数返回正确
- [ ] 确认支持的快递公司列表
- [ ] 测试物流查询接口

### 2. 消息通知配置
- [ ] 确认微信消息模板 `tmpl_orderfahuo` 已配置
- [ ] 测试发货通知发送
- [ ] 确认通知内容正确

### 3. 权限配置
- [ ] 确认管理员权限
- [ ] 确认用户权限
- [ ] 测试跨用户访问拦截

## 🐛 常见问题排查

### 1. 编译错误
**问题**: 页面编译失败
**解决**: 
- 检查 `pages.json` 配置
- 确认文件路径正确
- 检查语法错误

### 2. 接口调用失败
**问题**: API接口返回错误
**解决**:
- 检查接口路径
- 确认参数格式
- 检查权限配置

### 3. 物流信息不显示
**问题**: 物流查询无数据
**解决**:
- 检查物流接口配置
- 确认快递单号格式
- 检查网络连接

### 4. 页面跳转失败
**问题**: 点击查物流无反应
**解决**:
- 检查路由配置
- 确认页面文件存在
- 检查参数传递

## 📊 性能监控

### 1. 接口性能
- [ ] 监控发货接口响应时间
- [ ] 监控物流查询接口响应时间
- [ ] 监控数据库查询性能

### 2. 用户体验
- [ ] 监控页面加载速度
- [ ] 监控错误率
- [ ] 收集用户反馈

## 🔄 回滚方案

### 1. 数据库回滚
```sql
-- 删除新增字段（如需回滚）
ALTER TABLE ddwx_choujiang_record 
DROP COLUMN express_com,
DROP COLUMN express_no,
DROP COLUMN send_time,
DROP COLUMN express_status,
DROP COLUMN address,
DROP COLUMN area;
```

### 2. 代码回滚
- [ ] 恢复备份的文件
- [ ] 回滚Git提交
- [ ] 重新部署旧版本

## 📞 技术支持

### 联系方式
- 开发团队: [联系方式]
- 技术支持: [联系方式]
- 紧急联系: [联系方式]

### 文档资源
- 功能说明: `抽奖系统物流功能说明.md`
- 测试说明: `抽奖物流功能测试说明.md`
- API文档: 见相关接口注释

## 📝 部署记录

### 部署信息
- 部署时间: ___________
- 部署人员: ___________
- 版本号: ___________
- 环境: ___________

### 验证结果
- [ ] 数据库升级成功
- [ ] 后端功能正常
- [ ] 前端功能正常
- [ ] API接口正常
- [ ] 权限配置正确
- [ ] 性能测试通过

### 问题记录
- 问题1: ___________
- 解决方案: ___________
- 问题2: ___________
- 解决方案: ___________

### 签字确认
- 开发负责人: ___________
- 测试负责人: ___________
- 运维负责人: ___________
- 项目负责人: ___________

---

**注意**: 部署完成后请保留此清单作为记录，并及时更新相关文档。
