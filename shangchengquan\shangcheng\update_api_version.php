<?php
/**
 * 更新Coze API版本从v3到v1
 * 修复404错误：The requested API endpoint POST /v3/workflow/run does not exist
 */

// 引入ThinkPHP
require_once __DIR__ . '/vendor/autoload.php';

use think\facade\Db;
use think\facade\Config;

// 初始化应用
$app = new \think\App();
$app->initialize();

try {
    echo "开始更新Coze API版本配置...\n";
    
    // 查找所有使用v3版本的配置
    $configs = Db::name('coze_config')->where('api_version', 'v3')->select();
    
    if ($configs->isEmpty()) {
        echo "没有找到需要更新的v3配置\n";
    } else {
        echo "找到 " . count($configs) . " 个v3配置需要更新\n";
        
        // 更新所有v3配置为v1
        $updateCount = Db::name('coze_config')
            ->where('api_version', 'v3')
            ->update([
                'api_version' => 'v1',
                'update_time' => time()
            ]);
        
        echo "成功更新 {$updateCount} 个配置记录\n";
    }
    
    // 显示当前所有配置
    echo "\n当前所有Coze配置:\n";
    $allConfigs = Db::name('coze_config')->select();
    foreach ($allConfigs as $config) {
        echo "ID: {$config['id']}, AID: {$config['aid']}, API版本: {$config['api_version']}, 状态: " . ($config['status'] ? '启用' : '禁用') . "\n";
    }
    
    echo "\n✅ API版本更新完成！现在所有配置都使用v1版本，应该可以正常调用工作流了。\n";
    
} catch (\Exception $e) {
    echo "❌ 更新失败: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
}
