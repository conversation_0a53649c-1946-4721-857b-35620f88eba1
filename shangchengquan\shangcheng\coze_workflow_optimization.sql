-- 扣子工作流系统优化 - 数据库更新脚本
-- 基于可正常运行的调试代码优化后端接口

-- 更新工作流执行日志表结构，支持异步执行
ALTER TABLE `ddwx_coze_workflow_log` 
ADD COLUMN `execute_id` varchar(100) DEFAULT '' COMMENT '执行ID（异步执行时返回）' AFTER `workflow_id`,
ADD COLUMN `is_async` tinyint(1) DEFAULT 0 COMMENT '是否异步执行：0=同步，1=异步' AFTER `parameters`,
ADD COLUMN `debug_url` text COMMENT '调试URL' AFTER `is_async`,
ADD COLUMN `status` varchar(20) DEFAULT 'completed' COMMENT '执行状态：running=运行中，completed=已完成，failed=失败' AFTER `debug_url`,
ADD COLUMN `result` longtext COMMENT '执行结果JSON' AFTER `status`,
ADD INDEX `idx_execute_id` (`execute_id`),
ADD INDEX `idx_status` (`status`);

-- 更新扣子配置表，支持API版本配置
ALTER TABLE `ddwx_coze_config` 
MODIFY COLUMN `api_version` varchar(10) DEFAULT 'v1' COMMENT 'API版本，默认v1';

-- 更新现有配置记录的API版本为v1
UPDATE `ddwx_coze_config` SET `api_version` = 'v1' WHERE `api_version` = 'v3' OR `api_version` IS NULL;

-- 创建对话记录表（如果不存在）
CREATE TABLE IF NOT EXISTS `ddwx_coze_conversation` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `aid` int(11) NOT NULL DEFAULT 0 COMMENT '应用ID',
  `conversation_id` varchar(100) NOT NULL DEFAULT '' COMMENT '对话ID',
  `bot_id` varchar(100) NOT NULL DEFAULT '' COMMENT 'Bot ID',
  `mid` int(11) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0=禁用，1=启用',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_aid` (`aid`),
  KEY `idx_conversation_id` (`conversation_id`),
  KEY `idx_bot_id` (`bot_id`),
  KEY `idx_mid` (`mid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扣子对话记录表';

-- 创建消息记录表（如果不存在）
CREATE TABLE IF NOT EXISTS `ddwx_coze_message` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `aid` int(11) NOT NULL DEFAULT 0 COMMENT '应用ID',
  `conversation_id` varchar(100) NOT NULL DEFAULT '' COMMENT '对话ID',
  `bot_id` varchar(100) NOT NULL DEFAULT '' COMMENT 'Bot ID',
  `mid` int(11) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `user_message` text COMMENT '用户消息',
  `bot_response` longtext COMMENT 'Bot响应JSON',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_aid` (`aid`),
  KEY `idx_conversation_id` (`conversation_id`),
  KEY `idx_bot_id` (`bot_id`),
  KEY `idx_mid` (`mid`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扣子消息记录表';

-- 插入测试数据（如果需要）
INSERT IGNORE INTO `ddwx_coze_workflow` (`aid`, `name`, `workflow_id`, `description`, `status`, `create_time`, `update_time`) VALUES
(1, '测试工作流（优化版）', '7519352650686890038', '基于调试代码优化的测试工作流，支持异步执行', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1, '图像分析工作流', '7476690468139860006', '支持图像上传和分析的工作流，异步执行', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 插入测试配置（如果需要）
INSERT IGNORE INTO `ddwx_coze_config` (`aid`, `name`, `api_key`, `bot_id`, `base_url`, `api_version`, `status`, `create_time`, `update_time`) VALUES
(1, '默认配置（优化版）', 'pat_DPKdPgz27jXNZooKwHy2OQ6jMp6tY9Ga0nHdbjx9YRbbA2AKYbWH0gxKZ5Ta5GzR', '7519352650686890038', 'https://api.coze.cn', 'v1', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
