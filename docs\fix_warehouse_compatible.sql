-- 兼容性修复warehouse_voucher表字段的SQL脚本
-- 解决 "fields not exists:[remark]" 错误
-- 适用于所有MySQL版本

-- 1. 检查当前表结构
DESCRIBE `ddwx_warehouse_voucher`;

-- 2. 使用存储过程方式添加字段（兼容所有MySQL版本）

-- 添加remark字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_schema = DATABASE() 
     AND table_name = 'ddwx_warehouse_voucher' 
     AND column_name = 'remark') > 0,
    'SELECT "remark字段已存在" as result',
    'ALTER TABLE `ddwx_warehouse_voucher` ADD COLUMN `remark` varchar(255) NOT NULL DEFAULT "" COMMENT "备注信息"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加order_id字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_schema = DATABASE() 
     AND table_name = 'ddwx_warehouse_voucher' 
     AND column_name = 'order_id') > 0,
    'SELECT "order_id字段已存在" as result',
    'ALTER TABLE `ddwx_warehouse_voucher` ADD COLUMN `order_id` int(11) NOT NULL DEFAULT 0 COMMENT "关联订单ID"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加updatetime字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_schema = DATABASE() 
     AND table_name = 'ddwx_warehouse_voucher' 
     AND column_name = 'updatetime') > 0,
    'SELECT "updatetime字段已存在" as result',
    'ALTER TABLE `ddwx_warehouse_voucher` ADD COLUMN `updatetime` int(11) NOT NULL DEFAULT 0 COMMENT "更新时间"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加name字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_schema = DATABASE() 
     AND table_name = 'ddwx_warehouse_voucher' 
     AND column_name = 'name') > 0,
    'SELECT "name字段已存在" as result',
    'ALTER TABLE `ddwx_warehouse_voucher` ADD COLUMN `name` varchar(255) NOT NULL DEFAULT "" COMMENT "单据名称"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 创建warehouse_stock_log表
CREATE TABLE IF NOT EXISTS `ddwx_warehouse_stock_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `aid` int(11) NOT NULL DEFAULT '0',
  `bid` int(11) NOT NULL DEFAULT '0',
  `product_id` int(11) NOT NULL DEFAULT '0',
  `product_name` varchar(255) NOT NULL DEFAULT '',
  `before_stock` int(11) NOT NULL DEFAULT '0',
  `after_stock` int(11) NOT NULL DEFAULT '0',
  `adjust_num` int(11) NOT NULL DEFAULT '0',
  `type` tinyint(1) NOT NULL DEFAULT '1',
  `remark` varchar(255) NOT NULL DEFAULT '',
  `voucher_id` int(11) NOT NULL DEFAULT '0',
  `operator` varchar(50) NOT NULL DEFAULT '',
  `createtime` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`),
  KEY `voucher_id` (`voucher_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 4. 检查修复结果
SELECT 'warehouse_voucher表修复后结构：' as info;
DESCRIBE `ddwx_warehouse_voucher`;

-- 5. 显示所有字段
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'ddwx_warehouse_voucher'
ORDER BY ORDINAL_POSITION;

SELECT '字段修复完成，请重新测试出库单保存功能' as message;
