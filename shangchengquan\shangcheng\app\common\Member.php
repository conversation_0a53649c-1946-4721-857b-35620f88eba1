<?php
/**
 * 鲸犀商城 - 微信公众号小程序商城系统!
 * Copyright © 2020 鲸犀网络科技有限公司 保留所有权利
 * =========================================================
 * 版本：供应链版本
 * 授权主体：无限制版本
 * 授权域名：@@@
     
 * ----------------------------------------------
 * 您只能在商业授权范围内使用，不可二次转售、分发、分享、传播
 * 任何企业和个人不得对代码以任何目的任何形式的再发布
 * =========================================================
 */

namespace app\common;
use think\facade\Db;
use think\facade\Log;

use app\model\Member as m;

class Member
{
    /**
     * 自动注册会员（静默登录功能）
     * @param int $aid 应用ID
     * @param string $sessionid 会话ID
     * @param string $platform 平台标识
     * @return array|false 会员信息或失败
     */
    public static function autoReg($aid, $sessionid, $platform)
    {
        \think\facade\Log::write('=== Member::autoReg调用 ===');
        \think\facade\Log::write('Member::autoReg参数: aid='.$aid.', sessionid='.$sessionid.', platform='.$platform);
        \think\facade\Log::write('Member::autoReg调用堆栈: '.json_encode(debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 5)));
        
        $member = Db::name('member')->where('aid', $aid)->where('session_id', $sessionid)->find();
        if(!$member){
            // 创建新会员数据
            $data = [];
            $data['aid'] = $aid;
            $data['sex'] = 3;
            $data['nickname'] = '用户'.random(6);
            $data['headimg'] = PRE_URL.'/static/img/touxiang.png';
            $data['createtime'] = time();
            $data['session_id'] = $sessionid;
            $data['last_visittime'] = time();
            $data['platform'] = $platform;
            $data['checkst'] = 1; // 默认审核通过
            $data['isfreeze'] = 0; // 默认未冻结

            // 添加会员并更新session
            $mid = \app\model\Member::add($aid, $data);
            Db::name('session')->where('aid', $aid)->where('session_id', $sessionid)->update([
                'mid' => $mid,
                'login_time' => time()
            ]);
            $member = Db::name('member')->where('aid', $aid)->where('id', $mid)->find();
        }else{
            $mid = $member['id'];
            Db::name('session')->where('aid', $aid)->where('session_id', $sessionid)->update([
                'mid' => $mid,
                'login_time' => time()
            ]);
        }
        
        // 设置缓存时间（默认7天）
        $sessionid_time = 7*86400;
        // 检查后台设置的免登录天数
        $nologin_day = Db::name('admin_set')->where('aid', $aid)->value('nologin_day');
        if($nologin_day > 0){
            $sessionid_time = $nologin_day*86400;
        }
        cache($sessionid.'_mid', $mid, $sessionid_time);
        return $member;
    }

    //升级
 public static function uplv($aid,$mid,$orderid=0,$levelid=0){
        // 检查是否启用异步升级处理
        $admin_set = Db::name('admin_set')->where('aid', $aid)->find();

        \think\facade\Log::write("=== 异步升级检查 === AID:{$aid}, MID:{$mid}, 异步启用:" . ($admin_set['uplv_async_enabled'] ?? '0'));

        // 检查是否启用异步处理
        if($admin_set && isset($admin_set['uplv_async_enabled']) && $admin_set['uplv_async_enabled'] == 1) {
            // 获取当前用户团队数量
            $team_count = self::getTeamCountForAsync($aid, $mid);
            $threshold = $admin_set['uplv_async_threshold'] ?? 10000;

            \think\facade\Log::write("当前用户{$mid}团队数量: {$team_count}, 阈值: {$threshold}");

            // 如果当前用户团队人数超过阈值，加入异步队列
            if($team_count > $threshold) {
                \think\facade\Log::write("当前用户{$mid}团队数量超过阈值，加入异步队列");
                return self::addToUplvQueue($aid, $mid, $orderid, $levelid, $team_count);
            }

            // 检查上级用户是否是大团队
            $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->find();
            if($member && $member['path']) {
                $parent_ids = explode(',', $member['path']);
                $parent_ids = array_filter($parent_ids);

                \think\facade\Log::write("检查上级用户，上级数量: " . count($parent_ids));

                foreach($parent_ids as $parent_id) {
                    $parent_team_count = self::getTeamCountForAsync($aid, $parent_id);

                    \think\facade\Log::write("上级用户{$parent_id}团队数量: {$parent_team_count}");

                    if($parent_team_count > $threshold) {
                        \think\facade\Log::write("上级用户{$parent_id}是大团队，将整个升级流程加入异步队列");
                        // 上级是大团队，将整个升级流程加入异步队列
                        return self::addToUplvQueue($aid, $mid, $orderid, $levelid, $parent_team_count);
                    }
                }
            }

            \think\facade\Log::write("当前用户及上级都不是大团队，继续同步处理");
        }

        // 原有升级逻辑
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->find();
        if(!$member['id']) return;
        self::douplv($aid,$member,$orderid,$levelid); //提升该用户等级;

        //他的上级
        if($member['path']){  //主要检测上级用户等级并对满足条件的上级进行提升;
            $parentList = Db::name('member')->where('aid',$aid)->where('id','in',$member['path'])->order(Db::raw('field(id,'.$member['path'].')'))->select()->toArray();
            if($parentList){
                $parentList = array_reverse($parentList);
                foreach($parentList as $parent){
                    self::douplv($aid,$parent,$orderid,$levelid);
                }
            }
        }
    }

    /**
     * 获取用户团队数量（用于异步队列判断）- 不使用缓存
     */
    public static function getTeamCountForAsync($aid, $mid) {
        try {
            // 直接查询数据库，不使用缓存，不调用getdownmids方法
            $count = Db::name('member')
                ->where('aid', $aid)
                ->where('find_in_set('.$mid.',path)')
                ->count();
            return $count;
        } catch(\Exception $e) {
            return 0;
        }
    }

    /**
     * 添加用户到升级队列
     */
    public static function addToUplvQueue($aid, $mid, $orderid, $levelid, $team_count) {
        // 检查是否已经在队列中
        $existing = Db::name('member_uplv_queue')
            ->where('aid', $aid)
            ->where('mid', $mid)
            ->where('status', 'in', '0,1') // 待处理或处理中
            ->find();

        if($existing) {
            return ['status' => 'exists', 'msg' => '升级任务已在队列中，请稍后查看结果'];
        }

        // 添加到队列
        $queue_data = [
            'aid' => $aid,
            'mid' => $mid,
            'orderid' => $orderid,
            'levelid' => $levelid,
            'team_count' => $team_count,
            'status' => 0,
            'priority' => 1,
            'created_time' => date('Y-m-d H:i:s')
        ];

        try {
            $result = Db::name('member_uplv_queue')->insert($queue_data);

            if($result) {
                return ['status' => 'queued', 'msg' => '团队人数较多，升级任务已加入后台队列处理，请稍后查看结果'];
            } else {
                return ['status' => 'error', 'msg' => '加入队列失败，请重试'];
            }
        } catch(\Exception $e) {
            return ['status' => 'error', 'msg' => '加入队列异常: ' . $e->getMessage()];
        }
    }

    /**
     * 强制执行升级（用于队列处理）
     */
    public static function uplv_force($aid, $mid, $orderid=0, $levelid=0) {
        // 直接执行升级，不检查异步队列
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->find();
        if(!$member['id']) return;
        self::douplv($aid,$member,$orderid,$levelid);

        if($member['path']){
            $parentList = Db::name('member')->where('aid',$aid)->where('id','in',$member['path'])->order(Db::raw('field(id,'.$member['path'].')'))->select()->toArray();
            if($parentList){
                $parentList = array_reverse($parentList);
                foreach($parentList as $parent){
                    self::douplv($aid,$parent,$orderid,$levelid);
                }
            }
        }
    }

    public static function douplv($aid,$member,$orderid=0,$levelid=0){
        $mid = $member['id'];
        
        $wxpaymoney = 0 + Db::name('wxpay_log')->where('aid',$aid)->where('mid',$mid)->sum('total_fee'); //查找微信支付日志; //获取微信支付总额;
        $ordermoney = 0 + Db::name('shop_order')->where('aid',$aid)->where('mid',$mid)->where('status','in','1,2,3')->sum('product_price'); //获取商店支付总额;
        
        $rechargemoney = 0 + Db::name('recharge_order')->where('aid',$aid)->where('mid',$mid)->where('status',1)->sum('money');

        self::upLevel($aid, $member, $member, $ordermoney, $wxpaymoney, $rechargemoney, 0,$orderid,$levelid);

        //其他分组等级
        }

    /**
     * @param $aid
     * @param $member
     * @param $levelInfo 等级信息 levelid,levelstarttime
     * @param $ordermoney
     * @param $wxpaymoney
     * @param $rechargemoney
     * @param $cid 其他分组等级为空时使用此字段
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function upLevel($aid, $member, $levelInfo, $ordermoney, $wxpaymoney, $rechargemoney, $cid = 0,$orderid=0,$levelid=0)
    {
        $mid = $member['id'];
        $nowlv = ['sort' => -1];
         if($levelInfo['levelid'])
        
        if($levelInfo['levelid'] ==1)
        {
         $nowlv = Db::name('member_level')->where('id',$levelInfo['levelid'])->find();   
        }else{
            $nowlv = Db::name('member_level')->where('aid',$aid)->where('id',$levelInfo['levelid'])->find();   
        }
        
        
        $cid = $cid ? $cid : $nowlv['cid'];
        //等级列表
        if($levelid>0){
            $lvlist = Db::name('member_level')->where('aid',$aid)->where('id','=',$levelid)->order('sort,id')->select(); //
        }else{
            //->where('sort', '>',$nowlv['sort'])
             $lvlist = Db::name('member_level')->where('aid',$aid)->where('cid', $cid)->where('can_up',1)->order('sort,id')->select();
        }
        $newlv = $nowlv;
        $ordermoneys  = 0;
        if($orderid >0)
        {
            $order = Db::name('shop_order')->where('id',$orderid)->find();
            if($mid == $order['mid']){
               $ordermoneys    = $order['product_price'];
            }else{
               $ordermoneys  =  0;
            }
        }
        foreach($lvlist as $lv){
            $isup = false;
            $condition_or = false;
            if($lv['maxnum'] > 0){
                $lvmembercount = Db::name('member')->where('aid',$aid)->where('levelid',$lv['id'])->count();
                if($lvmembercount >= $lv['maxnum']) continue;
            }
            
            if(($lv['up_ordermoney']>0 && $lv['up_ordermoney'] <= $ordermoney) || ($lv['up_wxpaymoney']>0 && $lv['up_wxpaymoney'] <= $wxpaymoney)|| ($lv['up_rechargemoney']>0 && $lv['up_rechargemoney'] <= $rechargemoney) || ($member['card_code'] && $lv['up_getmembercard']==1) || ($lv['up_mydengji'] >0 && $lv['up_mydengji'] == $member['levelid']) ||($levelid >0) || ($lv['oneordermoney']>0 && $lv['oneordermoney'] <= $ordermoneys) ){
                $isup = true;
            }
           
            
            if(!$isup && $lv['up_fxordermoney'] > 0 && $lv['up_fxorder_condition'] == 'or'){
                if($lv['up_fxordermoney_removemax'] ==1){
                    $downmids = self::getdownmids_removemax($aid,$mid,$lv['up_fxorderlevelnum'],$lv['up_fxorderlevelid']);
                    //\think\facade\Log::write('---'.$mid.'去除最高的业绩后会员id---');
                    //\think\facade\Log::write($downmids);
                }else{
                    $downmids = self::getdownmids($aid,$mid,$lv['up_fxorderlevelnum'],$lv['up_fxorderlevelid']);
                }
                $fxordermoney = 0 + Db::name('shop_order_goods')->where('status','in','1,2,3')->where('mid','in',$downmids)->where('is_yeji','1')->sum('real_totalprice');
                if($fxordermoney >= $lv['up_fxordermoney']){
                    $isup = true;
                }
            }


            //或的时候
            if(!$isup && $lv['up_fxordermoney_two'] > 0 && $lv['up_fxorder_condition_two'] == 'or'){
                //var_dump('走这里111');
                if($lv['up_fxordermoney_removemax'] ==1){
                    $downmids = self::getdownmids_removemax($aid,$mid,$lv['up_fxorderlevelnum_two'],$lv['up_fxorderlevelid_two']);
                }else{
                    $downmids = self::getdownmids($aid,$mid,1,$lv['up_fxorderlevelid_two']);
                }
                $downmids[] = $mid;
                // var_dump($downmids);die;
                        $performanceList = [];
                    $line_totals = [];
                    foreach ($downmids as $subordinateMid) {
                        // var_dump($subordinateMid);die;
                        if ($subordinateMid == $mid) {
                            // 如果是自己的ID，只计算自己的业绩
                            $subordinatePerformance = Db::name('shop_order_goods')
                                ->where('status', 'in', '1,2,3')
                                ->where('is_yeji', '1')
                                ->where('mid', $mid)
                                ->sum('real_totalprice');
                        } else {
                            // 否则，计算包括所有下级的总业绩
                            $allSubordinates = self::getdownmids($aid, $subordinateMid, $lv['up_fxorderlevelnum']);
                            $allSubordinates[] = $subordinateMid; // 包含直推下级自身
                            //  var_dump($allSubordinates);die;
                            $subordinatePerformance = Db::name('shop_order_goods')
                                ->where('status', 'in', '1,2,3')
                                ->where('is_yeji', '1')
                                ->where('mid', 'in', $allSubordinates)
                                ->sum('real_totalprice');
                        }
                    
                        $line_totals[$subordinateMid] = $subordinatePerformance;
                    }
                    // 当只有一个直推下级时的特殊处理
                    // var_dump( $line_totals);die;
                    if (count($line_totals) == 1) {
                        // 当只有一个下级时，大区业绩为该下级的业绩，小区业绩为0
                        $max_totalprice = reset($line_totals); // 获取该下级的业绩作为大区业绩
                        $min_totalprice = 0; // 小区业绩设置为0
                    } else {
                        // 计算大小区
                        arsort($line_totals); // 业绩降序排序
                        $max_totalprice = reset($line_totals); // 最大业绩
                        array_shift($line_totals); // 移除大区业绩
                        $min_totalprice = array_sum($line_totals); // 小区总业绩
                    }


//  \think\facade\Log::write('用户: ' . $mid);
 
//  \think\facade\Log::write('小区业绩: ' . $min_totalprice);
 
//  \think\facade\Log::write('大区业绩: ' . $max_totalprice);
//                 \think\facade\Log::write('当前业绩计算值: ' . $fxordermoney);
//                 \think\facade\Log::write('目标业绩阈值: ' . $lv['up_fxordermoney_two']);

                    //      var_dump('self'.$max_line);
                    //      var_dump('self'.$self_line_total);
                    // var_dump('大区'.$max_totalprice);
                    // var_dump('小区'.$min_totalprice);die;
                    // 小区业绩*3倍大于大区业绩就按照 ，总业绩就等于所有业绩之和 如果小区业绩*3倍小于等于大区业绩，总业绩就等于所有小区业绩之和乘以2
                    if($min_totalprice*2 > $max_totalprice){
                        $fxordermoney=$min_totalprice+$max_totalprice;
                    }else{
                        $fxordermoney=$min_totalprice*2;
                    }
                    
                    //小区业绩*3倍大于大区业绩,总业绩就等于所有业绩之和  或  小区业绩*3倍小于等于大区业绩，总业绩就等于所有小区业绩之和
                    // if($min_totalprice*3 <= $max_totalprice){
                    //     $fxordermoney=$min_totalprice;
                    // }
                    // //如果小区业绩2倍大于等于大区业绩就按照 ，它的考核总业绩就等于所有业绩之和如果小区业绩2倍小于大区业绩，它的考核总业绩就等于所有小区业绩的3倍
                    // if($min_totalprice*2<$max_totalprice){
                    //     $fxordermoney=$min_totalprice*3;
                    // }
                
                // var_dump($fxordermoney);die;
                // var_dump($lv['up_fxordermoney_two']);
                //加上自己的业绩
                // $midordermoney= 0 + Db::name('shop_order_goods')->where('status','in','1,2,3')->where('is_yeji','1')->where('mid',$mid)->sum('real_totalprice');
                // // var_dump($midordermoney);die;
                // $fxordermoney=$fxordermoney+$midordermoney;
                    
                if($fxordermoney >= $lv['up_fxordermoney_two']){
                    // var_dump(555);
                    // var_dump($lv['up_fxordermoney_two']);die;
                    $isup_up_fxordermoney = true;
                    $isup = true;
                   
                } else {
                    // var_dump(666);
                    $isup = false;
                }
                // die;
             
            }
// var_dump($isup);die;
            //小区业绩（去除一个大区）条件判断
          
            if(!$isup && $lv['up_fxordermoney_three'] > 0 && $lv['up_fxorder_condition_three'] == 'or'){
                // 使用新方法计算去除最大区的业绩
                $fxordermoney = self::getmytuanduiyeji_removemax($aid, $mid);
                // 记录日志
                //\think\facade\Log::write('用户: ' . $mid);
               // \think\facade\Log::write('小区业绩（去除一个大区）: ' . $fxordermoney);
               // \think\facade\Log::write('目标业绩阈值: ' . $lv['up_fxordermoney_three']);
                 
                if($fxordermoney >= $lv['up_fxordermoney_three']){
                    $isup = true;
                }
             
            }
            if(!$isup && $lv['up_fxordermoney_three'] > 0 && $lv['up_fxorder_condition_three'] == 'and'){
                // 使用新方法计算去除最大区的业绩
                $fxordermoney = self::getmytuanduiyeji_removemax($aid, $mid);
                // 记录日志
               // \think\facade\Log::write('and用户: ' . $mid);
               // \think\facade\Log::write('小区业绩（去除一个大区）: ' . $fxordermoney);
               // \think\facade\Log::write('目标业绩阈值: ' . $lv['up_fxordermoney_three']);
                 
                if($fxordermoney >= $lv['up_fxordermoney_three']){
                    $isup = true;
                }else {
                    // var_dump(666);
                   $isup = false;
        $condition_or = true; // 小区业绩条件不满足
                }
             
            }

            if(!$isup && $lv['up_fxordermoney_xiao'] > 0){
                $downmids = self::getdownmids_xiao($aid,$mid,$lv['up_fxorderlevelnum_xiao'],$lv['up_fxorderlevelid_xiao']);
                $fxordermoney = 0 + Db::name('shop_order_goods')->where('status','in','1,2,3')->where('is_yeji','1')->where('mid','in',$downmids)->sum('real_totalprice');
                if($fxordermoney >= $lv['up_fxordermoney_xiao']){
                    $isup = true;
                }
            }

            if(!$isup && $lv['up_jifen'] > 0 && $lv['up_jifen_condition'] == 'or'){
                // $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->field('shengjiscore')->find();
                if($member['shengjiscore'] >= $lv['up_jifen']){
                    $isup = true;
                }
            }

            if(!$isup && ($lv['up_fxdowncount']>0 || $lv['up_fxdowncount2']>0 || $lv['up_fxdowncount3']>0)){
                $downmidcount1 = 0;
                $downmidcount2 = 0;
                $downmidcount3 = 0;
                $up_fxdowncount = intval($lv['up_fxdowncount']);
                $up_fxdowncount2 = intval($lv['up_fxdowncount2']);
                $up_fxdowncount3 = intval($lv['up_fxdowncount3']);
                if($lv['up_fxdowncount'] > 0){
                    $downmids = self::getdownmids($aid,$mid,$lv['up_fxdownlevelnum'],$lv['up_fxdownlevelid']);
                    $downmidcount1 = count($downmids);
                }
                if($lv['up_fxdowncount2'] > 0){
                    $downmids2 = self::getdownmids($aid,$mid,$lv['up_fxdownlevelnum2'],$lv['up_fxdownlevelid2']);
                    $downmidcount2 = count($downmids2);
                }
                if($lv['up_fxdowncount3'] > 0){
                    $downmids3 = self::getdownmids($aid,$mid,$lv['up_fxdownlevelnum3'],$lv['up_fxdownlevelid3']);
                    $downmidcount3 = count($downmids3);
                }
                if($downmidcount1 >= $up_fxdowncount && $downmidcount2 >= $up_fxdowncount2 && $downmidcount3 >= $up_fxdowncount3){
                    $isup = true;
                }
            }
            // 第一个
            if(!$isup && ($lv['up_fxdowncount_new1']>0 || $lv['up_fxdowncount2_new1']>0)){
                $downmidcount1 = 0;
                $downmidcount2 = 0;
                $downmidcount3 = 0;
                $up_fxdowncount = intval($lv['up_fxdowncount_new1']);
                $up_fxdowncount2 = intval($lv['up_fxdowncount2_new1']);
                if($lv['up_fxdowncount_new1'] > 0){
                    $downmids = self::getdownmids($aid,$mid,$lv['up_fxdownlevelnum_new1'],$lv['up_fxdownlevelid_new1']);
                    $downmidcount1 = count($downmids);
                }
                if($lv['up_fxdowncount2_new1'] > 0){
                    $downmids2 = self::getdownmids($aid,$mid,$lv['up_fxdownlevelnum2_new1'],$lv['up_fxdownlevelid2_new1']);
                    $downmidcount2 = count($downmids2);
                }
                if($downmidcount1 >= $up_fxdowncount && $downmidcount2 >= $up_fxdowncount2){
                    $isup = true;
                }
            }   
            //第二个
            if(!$isup && ($lv['up_fxdowncount_new2']>0 || $lv['up_fxdowncount2_new2']>0)){
                $downmidcount1 = 0;
                $downmidcount2 = 0;
                $downmidcount3 = 0;
                $up_fxdowncount = intval($lv['up_fxdowncount_new2']);
                $up_fxdowncount2 = intval($lv['up_fxdowncount2_new2']);
                if($lv['up_fxdowncount_new2'] > 0){
                    $downmids = self::getdownmids($aid,$mid,$lv['up_fxdownlevelnum_new2'],$lv['up_fxdownlevelid_new2']);
                    $downmidcount1 = count($downmids);
                }
                if($lv['up_fxdowncount2_new2'] > 0){
                    $downmids2 = self::getdownmids($aid,$mid,$lv['up_fxdownlevelnum2_new2'],$lv['up_fxdownlevelid2_new2']);
                    $downmidcount2 = count($downmids2);
                }
                if($downmidcount1 >= $up_fxdowncount && $downmidcount2 >= $up_fxdowncount2){
                    $isup = true;
                }
            }   
            //第三个
            if(!$isup && ($lv['up_fxdowncount_new3']>0 || $lv['up_fxdowncount2_new3']>0)){
                $downmidcount1 = 0;
                $downmidcount2 = 0;
                $downmidcount3 = 0;
                $up_fxdowncount = intval($lv['up_fxdowncount_new3']);
                $up_fxdowncount2 = intval($lv['up_fxdowncount2_new3']);
                if($lv['up_fxdowncount_new3'] > 0){
                    $downmids = self::getdownmids($aid,$mid,$lv['up_fxdownlevelnum_new3'],$lv['up_fxdownlevelid_new3']);
                    $downmidcount1 = count($downmids);
                }
                if($lv['up_fxdowncount2_new3'] > 0){
                    $downmids2 = self::getdownmids($aid,$mid,$lv['up_fxdownlevelnum2_new3'],$lv['up_fxdownlevelid2_new3']);
                    $downmidcount2 = count($downmids2);
                }
                if($downmidcount1 >= $up_fxdowncount && $downmidcount2 >= $up_fxdowncount2){
                    $isup = true;
                }
            }       
            //第四个
            if(!$isup && ($lv['up_fxdowncount_new4']>0 || $lv['up_fxdowncount2_new4']>0)){
                $downmidcount1 = 0;
                $downmidcount2 = 0;
                $downmidcount3 = 0;
                $up_fxdowncount = intval($lv['up_fxdowncount_new4']);
                $up_fxdowncount2 = intval($lv['up_fxdowncount2_new4']);
                if($lv['up_fxdowncount_new4'] > 0){
                    $downmids = self::getdownmids($aid,$mid,$lv['up_fxdownlevelnum_new4'],$lv['up_fxdownlevelid_new4']);
                    $downmidcount1 = count($downmids);
                }
                if($lv['up_fxdowncount2_new4'] > 0){
                    $downmids2 = self::getdownmids($aid,$mid,$lv['up_fxdownlevelnum2_new3'],$lv['up_fxdownlevelid2_new4']);
                    $downmidcount2 = count($downmids2);
                }
                if($downmidcount1 >= $up_fxdowncount && $downmidcount2 >= $up_fxdowncount2){
                    $isup = true;
                }
            }       
            //第五个
            if(!$isup && ($lv['up_fxdowncount_new5']>0 || $lv['up_fxdowncount2_new5']>0)){
                $downmidcount1 = 0;
                $downmidcount2 = 0;
                $downmidcount3 = 0;
                $up_fxdowncount = intval($lv['up_fxdowncount_new5']);
                $up_fxdowncount2 = intval($lv['up_fxdowncount2_new5']);
                if($lv['up_fxdowncount_new5'] > 0){
                    $downmids = self::getdownmids($aid,$mid,$lv['up_fxdownlevelnum_new5'],$lv['up_fxdownlevelid_new5']);
                    $downmidcount1 = count($downmids);
                }
                if($lv['up_fxdowncount2_new5'] > 0){
                    $downmids2 = self::getdownmids($aid,$mid,$lv['up_fxdownlevelnum2_new5'],$lv['up_fxdownlevelid2_new5']);
                    $downmidcount2 = count($downmids2);
                }
                if($downmidcount1 >= $up_fxdowncount && $downmidcount2 >= $up_fxdowncount2){
                    $isup = true;
                }
            }       
                
            if(!$isup && ($lv['up_proid']!='0' && $lv['up_proid']!='')){ //购买指定商品
                $up_proids = explode(',',str_replace('，',',',$lv['up_proid']));
                $up_pronums = explode(',',str_replace('，',',',$lv['up_pronum']));
                if(count($up_pronums) > 1) {
                    foreach($up_proids as $k=>$up_proid){
                        $pronum = $up_pronums[$k];
                        if(!$pronum) $pronum = 1;
                        $buynum = Db::name('shop_order_goods')->where('aid',$aid)->where('mid',$mid)->where('is_yeji','1')->where('proid',$up_proid)->where('status','in','1,2,3')->sum('num');
                        if($buynum >= $pronum){
                            $isup = true;
//                        break;
                        }
                    }
                } else {
                    $pronum = $up_pronums[0];
                    if(!$pronum) $pronum = 1;
                    $buynum = 0;
                    foreach($up_proids as $k=>$up_proid){
                        $buynum += Db::name('shop_order_goods')->where('aid',$aid)->where('mid',$mid)->where('is_yeji','1')->where('proid',$up_proid)->where('status','in','1,2,3')->sum('num');
                        if($buynum >= $pronum){
                            $isup = true;
//                        break;
                        }
                    }
                }
            }
            // var_dump($lv['up_zt_new']);
            //判断直推2等级id1业绩达到5000
            //up_zt_new  up_zt_new_dengjiid  up_zt_new_dengjiyeji
           
            if(!$isup && ($lv['up_zt_new']>0))
            {
                if($lv['up_zt_new_dengjiid']>0)
                {
                    $up_zt_new_dengjiid = Db::name('member_level')->where('aid',$aid)->where('id','=',$lv['up_zt_new_dengjiid'])->find();
                    $up_zt_new_dengjiid_dayu = Db::name('member_level')->where('aid',$aid)->where('sort','>=',$up_zt_new_dengjiid['sort'])->order('sort,id')->select()->toArray();
                }else{
                   $up_zt_new_dengjiid_dayu = Db::name('member_level')->where('aid',$aid)->order('sort,id')->select()->toArray(); 
                }
                $up_zt_new_dengjiid_dayuids = array_column($up_zt_new_dengjiid_dayu,'id');
                $up_zt_new_dengjiid_dayuids = implode(',',$up_zt_new_dengjiid_dayuids);
                ///获取我的直推下级
                $memberlist = Db::name('member')->field('id,nickname,createtime')->where('aid',$aid)->where('pid',$mid)->where('levelid','in',$up_zt_new_dengjiid_dayuids)->select()->toArray();
                $memberlistcount = count($memberlist);
                if($memberlistcount >=$lv['up_zt_new'])
                {
                    // var_dump(3444);
                    if($lv['up_zt_new_dengjiyeji'] >0)
                    {
                        //设置业绩判断团队业绩是否
                        $mytuandui = self::getmytuanduiyeji($aid,$mid);;
                        // var_dump($mytuandui);
                        if($mytuandui >= $lv['up_zt_new_dengjiyeji'])
                        {
                            $isup = true;
                        }
                    }else{
                         $isup = true;
                    }
                }
            }
            // var_dump($lv['up_zt_renshu']);
            //判断 直推3人且至少2个市场出现等级id 2 且团队业绩达到 5000元
            //up_zt_renshu  up_zt_renshu_count  up_zt_renshu_shichang   up_zt_renshu_tuanduiyeji
            if(!$isup && ($lv['up_zt_renshu']>0))
            {
                $zhituicount = Db::name('member')->field('id,nickname,createtime')->where('aid',$aid)->where('pid',$mid)->select()->toArray();
                // var_dump(count($zhituicount));
                if(count($zhituicount) >= $lv['up_zt_renshu']){
                    // var_dump($lv['up_zt_renshu_count']);
                    // var_dump($lv['up_zt_renshu_shichang']);
                    if($lv['up_zt_renshu_count']>0 && $lv['up_zt_renshu_shichang']>0)
                    {
                        $up_zt_new_dengjiid = Db::name('member_level')->where('aid',$aid)->where('id','=',$lv['up_zt_renshu_shichang'])->find();
                        $up_zt_new_dengjiid_dayu = Db::name('member_level')->where('aid',$aid)->where('can_up',1)->where('sort','>=',$up_zt_new_dengjiid['sort'])->order('sort,id')->select()->toArray();
                    }else{
                         $up_zt_new_dengjiid_dayu = Db::name('member_level')->where('aid',$aid)->order('sort,id')->select()->toArray(); 
                    }
                    $up_zt_new_dengjiid_dayuids = array_column($up_zt_new_dengjiid_dayu,'id');
                    $up_zt_new_dengjiid_dayuids = implode(',',$up_zt_new_dengjiid_dayuids);
                    // var_dump($up_zt_new_dengjiid_dayuids);
                     ///获取我的直推下级
                    $memberlist = Db::name('member')->field('id,nickname,createtime')->where('aid',$aid)->where('pid',$mid)->where('levelid','in',$up_zt_new_dengjiid_dayuids)->select()->toArray();
                    $memberlistcount = count($memberlist);
                    // var_dump($memberlistcount);
                    if($memberlistcount>=$lv['up_zt_renshu_count'])
                    {
                        if($lv['up_zt_renshu_tuanduiyeji']>0)
                        {
                            //设置业绩判断团队业绩是否
                            $mytuandui = self::getmytuanduiyeji($aid,$mid);;
                            if($mytuandui >= $lv['up_zt_renshu_tuanduiyeji'])
                            {
                                $isup = true;
                            }
                        }else{
                            $isup = true; 
                        }
                    }
                }
                
            }
            /**或 判断直推多少人,多少市场出现等级idn*/
             if(!$isup && ($lv['up_zt_new3_duo']>0))
             {
                $q = 0;
                $zhituicount = Db::name('member')->field('id,nickname,createtime')->where('aid',$aid)->where('pid',$mid)->select()->toArray();
                if(count($zhituicount) >= $lv['up_zt_new3_duo'])
                {
                    $qumemberid = '';
                    if($lv['up_zt_new3_dengjiid_shichang1'] >0 && $lv['up_zt_new3_dengjiid_duo1'] > 0 )
                    {
                        $memberlist = Db::name('member')->field('id,nickname,createtime,levelid')->where('aid',$aid)->where('pid',$mid)->select()->toArray();
                        $up_zt_new3_dengjiid_shichang1 = 0;
                        foreach($memberlist as $membervalue)
                        {
                            if($up_zt_new3_dengjiid_shichang1 <= $lv['up_zt_new3_dengjiid_shichang1']){
                                    $up_zt_new2_dengjiid_duo1ids = explode(',',$lv['up_zt_new3_dengjiid_duo1']);
                                    if(in_array($membervalue['levelid'],$up_zt_new2_dengjiid_duo1ids) !==false)
                                    {
                                        $membervaluelist = $membervalue;
                                    }else{
                                        $membervaluelist = Db::name('member')->field('id,nickname,createtime,levelid')->where('aid',$aid)->where('find_in_set('.$membervalue['id'].',path)')->whereIn('levelid',$up_zt_new2_dengjiid_duo1ids)->select()->toArray();
                                    }
                                if(!empty($membervaluelist))
                                {
                                    $up_zt_new3_dengjiid_shichang1 = $up_zt_new3_dengjiid_shichang1+1;
                                    $qumemberid = ','.$membervalue['id'];
                                }
                            }
                        }
                        if($up_zt_new3_dengjiid_shichang1 >= $lv['up_zt_new3_dengjiid_shichang1'])
                        {
                             $isup = true;
                             $q = 1;
                        }
                    }else{
                         $q=1;
                    }
                    if($lv['up_zt_new3_dengjiid_shichang2'] >0 && $lv['up_zt_new3_dengjiid_duo2'] > 0  && $q==1)
                    {
                        if($qumemberid)
                            {
                                $qumemberid = substr($qumemberid,0,1);
                                $memberlist = Db::name('member')->field('id,nickname,createtime,levelid')->where('aid',$aid)->where('pid',$mid)->where('id','not in',$qumemberid)->select()->toArray();
                            }else{
                                 $memberlist = Db::name('member')->field('id,nickname,createtime,levelid')->where('aid',$aid)->where('pid',$mid)->select()->toArray();
                            }
                            $up_zt_new3_dengjiid_shichang2 = 0;
                            foreach($memberlist as $membervalue)
                            {
                                $up_zt_new2_dengjiid_duo1ids = explode(',',$lv['up_zt_new3_dengjiid_duo2']);
                                if($up_zt_new3_dengjiid_shichang2 <= $lv['up_zt_new3_dengjiid_duo2']){
                                     if(in_array($membervalue['levelid'],$up_zt_new2_dengjiid_duo1ids) !==false)
                                    {
                                        $membervaluelist = $membervalue;
                                    }else{
                                        $membervaluelist = Db::name('member')->field('id,nickname,createtime,levelid')->where('aid',$aid)->where('find_in_set('.$membervalue['id'].',path)')->whereIn('levelid',$up_zt_new2_dengjiid_duo1ids)->select()->toArray();
                                    }
                                    if(!empty($membervaluelist))
                                    {
                                        $up_zt_new3_dengjiid_shichang2 = $up_zt_new3_dengjiid_shichang2+1;
                                    }
                                }
                            }
                            if($up_zt_new3_dengjiid_shichang2 >= $lv['up_zt_new3_dengjiid_shichang2'])
                            {
                                 $isup = true;
                                 $q = 1;
                            }
                    }else{
                        $q=1;
                    }
                    if($lv['up_zt_new3_tuanduiyeji'] > 0  && $q==1)
                    {
                        $mytuandui = self::getmytuanduiyeji($aid,$mid);
                        if($mytuandui >= $lv['up_zt_new3_tuanduiyeji'])
                        {
                            $isup = true;
                        }
                    }
                    
                }
                
             }
            // var_dump('第一个');
            //是否包含or的条件
            if($lv['up_ordermoney']>0  || $lv['oneordermoney']>0 || $lv['up_wxpaymoney']>0 || $lv['up_rechargemoney']>0 || $lv['up_getmembercard']==1 || $lv['up_mydengji'] >0 || ($lv['up_fxordermoney'] > 0 && $lv['up_fxorder_condition'] == 'or') || $lv['up_fxordermoney_xiao'] > 0
             || $lv['up_fxdowncount']>0 || $lv['up_fxdowncount2']>0 || $lv['up_fxdowncount3']>0 || ($lv['up_proid']!='0' && $lv['up_proid']!='') || ($lv['up_zt_new']>0) ||($lv['up_zt_renshu']>0) ||($lv['up_zt_new3_duo']>0) || ($lv['up_jifen'] > 0 && $lv['up_jifen_condition'] == 'or') || (($lv['up_market_ab_total'] > 0 || $lv['up_market_other_min'] > 0) && $lv['up_market_condition'] == 'or')) {
                 $condition_or = true;
            }
            if(($isup || ($isup === false && $condition_or === false)) && $lv['up_fxordermoney'] > 0 && $lv['up_fxorder_condition'] == 'and'){
                if($lv['up_fxordermoney_removemax'] ==1){
                    $downmids = self::getdownmids_removemax($aid,$mid,$lv['up_fxorderlevelnum'],$lv['up_fxorderlevelid']);
                }else{
                    $downmids = self::getdownmids($aid,$mid,$lv['up_fxorderlevelnum'],$lv['up_fxorderlevelid']);
                }
                $fxordermoney = 0 + Db::name('shop_order_goods')->where('status','in','1,2,3')->where('is_yeji','1')->where('mid','in',$downmids)->sum('real_totalprice');

                if($fxordermoney >= $lv['up_fxordermoney']){
                    $isup_up_fxordermoney = true;
                } else {
                    $isup = false;
                }
            }
        
            //var_dump(111);
            //var_dump($lv['up_fxordermoney_two']);
            //var_dump($lv['up_fxorder_condition_two']);
            if(($isup || ($isup === false && $condition_or === false)) && $lv['up_fxordermoney_two'] > 0 && $lv['up_fxorder_condition_two'] == 'and'){
                //var_dump('走这里222');
                  if($lv['up_fxordermoney_removemax'] ==1){
                    $downmids = self::getdownmids_removemax($aid,$mid,$lv['up_fxorderlevelnum_two'],$lv['up_fxorderlevelid_two']);
                }else{
                    $downmids = self::getdownmids($aid,$mid,1,$lv['up_fxorderlevelid_two']);
                }
                $downmids[] = $mid;
                // var_dump($downmids);die;
                        $performanceList = [];
                    $line_totals = [];
                    foreach ($downmids as $subordinateMid) {
                        // var_dump($subordinateMid);die;
                        if ($subordinateMid == $mid) {
                            // 如果是自己的ID，只计算自己的业绩
                            $subordinatePerformance = Db::name('shop_order_goods')
                                ->where('status', 'in', '1,2,3')
                                ->where('is_yeji', '1')
                                ->where('mid', $mid)
                                ->sum('real_totalprice');
                        } else {
                            // 否则，计算包括所有下级的总业绩
                            $allSubordinates = self::getdownmids($aid, $subordinateMid, $lv['up_fxorderlevelnum']);
                            $allSubordinates[] = $subordinateMid; // 包含直推下级自身
                            //  var_dump($allSubordinates);die;
                            $subordinatePerformance = Db::name('shop_order_goods')
                                ->where('status', 'in', '1,2,3')
                                ->where('is_yeji', '1')
                                ->where('mid', 'in', $allSubordinates)
                                ->sum('real_totalprice');
                        }
                    
                        $line_totals[$subordinateMid] = $subordinatePerformance;
                    }
                    // 当只有一个直推下级时的特殊处理
                    // var_dump( $line_totals);die;
                    if (count($line_totals) == 1) {
                        // 当只有一个下级时，大区业绩为该下级的业绩，小区业绩为0
                        $max_totalprice = reset($line_totals); // 获取该下级的业绩作为大区业绩
                        $min_totalprice = 0; // 小区业绩设置为0
                    } else {
                        // 计算大小区
                        arsort($line_totals); // 业绩降序排序
                        $max_totalprice = reset($line_totals); // 最大业绩
                        array_shift($line_totals); // 移除大区业绩
                        $min_totalprice = array_sum($line_totals); // 小区总业绩
                    }


//  \think\facade\Log::write('用户: ' . $mid);
 
//  \think\facade\Log::write('小区业绩: ' . $min_totalprice);
 
//  \think\facade\Log::write('大区业绩: ' . $max_totalprice);
//                 \think\facade\Log::write('当前业绩计算值: ' . $fxordermoney);
//                 \think\facade\Log::write('目标业绩阈值: ' . $lv['up_fxordermoney_two']);

                    //      var_dump('self'.$max_line);
                    //      var_dump('self'.$self_line_total);
                    // var_dump('大区'.$max_totalprice);
                    // var_dump('小区'.$min_totalprice);die;
                    // 小区业绩*3倍大于大区业绩就按照 ，总业绩就等于所有业绩之和 如果小区业绩*3倍小于等于大区业绩，总业绩就等于所有小区业绩之和乘以2
                    if($min_totalprice*2 > $max_totalprice){
                        $fxordermoney=$min_totalprice+$max_totalprice;
                    }else{
                        $fxordermoney=$min_totalprice*2;
                    }
                    
                    //小区业绩*3倍大于大区业绩,总业绩就等于所有业绩之和  或  小区业绩*3倍小于等于大区业绩，总业绩就等于所有小区业绩之和
                    // if($min_totalprice*3 <= $max_totalprice){
                    //     $fxordermoney=$min_totalprice;
                    // }
                    // //如果小区业绩2倍大于等于大区业绩就按照 ，它的考核总业绩就等于所有业绩之和如果小区业绩2倍小于大区业绩，它的考核总业绩就等于所有小区业绩的3倍
                    // if($min_totalprice*2<$max_totalprice){
                    //     $fxordermoney=$min_totalprice*3;
                    // }
                
                // var_dump($fxordermoney);die;
                // var_dump($lv['up_fxordermoney_two']);
                //加上自己的业绩
                // $midordermoney= 0 + Db::name('shop_order_goods')->where('status','in','1,2,3')->where('is_yeji','1')->where('mid',$mid)->sum('real_totalprice');
                // // var_dump($midordermoney);die;
                // $fxordermoney=$fxordermoney+$midordermoney;
                    
                if($fxordermoney >= $lv['up_fxordermoney_two']){
                    // var_dump(555);
                    // var_dump($lv['up_fxordermoney_two']);die;
                    $isup_up_fxordermoney = true;
                    $isup = true;
                   
                } else {
                    // var_dump(666);
                    $isup = false;
                }
                // die;
             
            }
             if(($isup || ($isup === false && $condition_or === false)) && $lv['up_jifen'] > 0 && $lv['up_jifen_condition'] == 'and'){
                // $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->field('shengjiscore')->find();
                if($member['shengjiscore'] >= $lv['up_jifen']){
                    // $isup = true;
                   $isup = true;
                }else{
                     $isup = false;
                     
                }
            }

            if(false){}else{
                //没设置或条件$isup === false && $condition_or === false
                if(($isup || ($isup === false && $condition_or === false)) && ($lv['up_fxdowncount_and']>0 || $lv['up_fxdowncount2_and']>0)){
                    $downmidcount1 = 0;
                    $downmidcount2 = 0;
                    $up_fxdowncount = intval($lv['up_fxdowncount_and']);
                //  var_dump($up_fxdowncount);
                    $up_fxdowncount2 = intval($lv['up_fxdowncount2_and']);
                //  var_dump($up_fxdowncount2);
                    if($lv['up_fxdowncount_and'] > 0){
                        $downmids = self::getdownmids($aid,$mid,$lv['up_fxdownlevelnum_and'],$lv['up_fxdownlevelid_and']);
                        $downmidcount1 = count($downmids);
                    }
                    if($lv['up_fxdowncount2_and'] > 0){
                        $downmids2 = self::getdownmids($aid,$mid,$lv['up_fxdownlevelnum2_and'],$lv['up_fxdownlevelid2_and']);
                        $downmidcount2 = count($downmids2);
                    }
                //  var_dump($up_fxdowncount);
                //  var_dump($downmidcount1);
                    if($downmidcount1 >= $up_fxdowncount && $downmidcount2 >= $up_fxdowncount2){
                        $isup_up_fxdowncount_and = true;
                    } else {
                        $isup = false;
                    }
                }
                

                
                
            }
//          die;
            if($isup || ($isup === false && $condition_or === false)) {
                if(($lv['up_fxordermoney'] > 0 && $lv['up_fxorder_condition'] == 'and') && ($lv['up_fxdowncount_and']>0 || $lv['up_fxdowncount2_and']>0)) {
                    if($isup_up_fxordermoney && $isup_up_fxdowncount_and) {
                        $isup = true;
                    }
                }elseif(!($lv['up_fxordermoney'] > 0 && $lv['up_fxorder_condition'] == 'and') && ($lv['up_fxdowncount_and']>0 || $lv['up_fxdowncount2_and']>0)) {
                    if($isup_up_fxdowncount_and) {
                        $isup = true;
                    }
                }elseif(($lv['up_fxordermoney'] > 0 && $lv['up_fxorder_condition'] == 'and') && !($lv['up_fxdowncount_and']>0 || $lv['up_fxdowncount2_and']>0)) {
                    if($isup_up_fxordermoney) {
                        $isup = true;
                    }
                }
            }

           
if(!$isup && ($lv['up_market_ab_total'] > 0 && $lv['up_market_other_min'] >= 0) && $lv['up_market_condition'] == 'or'){
    // 获取所有直推下级
    $directDowns = Db::name('member')->field('id')->where('aid', $aid)->where('pid', $mid)->select()->toArray();
    $directDownIds = array_column($directDowns, 'id');
    
    //var_dump(111);die;
    // 计算每个市场的业绩
    $marketPerformances = [];
    foreach($directDownIds as $downId) {
        // 获取该下级及其所有下级
        $allSubordinates = self::getdownmids($aid, $downId, 0);
        $allSubordinates[] = $downId; // 包含直推下级自身
        
        // 计算该市场的业绩
        $marketPerformance = Db::name('shop_order_goods')
            ->where('status', 'in', '1,2,3')
            ->where('is_yeji', '1')
            ->where('mid', 'in', $allSubordinates)
            ->sum('real_totalprice');
            
        $marketPerformances[$downId] = $marketPerformance;
    }
    
    // 按业绩排序
    arsort($marketPerformances);
    
    // 检查条件1: A+B≥25万
    $topTwoMarkets = array_slice($marketPerformances, 0, 2, true);
    $abTotal = array_sum($topTwoMarkets);
    
    // 检查条件2: 其它任意一个市场业绩≥25万
    $otherMarkets = array_slice($marketPerformances, 2);
    $otherMarketMeetsCondition = false;
    
    // 特殊处理：当阈值为0且存在其他市场时，直接视为满足条件
    if ($lv['up_market_other_min'] == 0 || !empty($otherMarkets)) {
        $otherMarketMeetsCondition = true;
    } else {
        foreach($otherMarkets as $performance) {
            if($performance >= $lv['up_market_other_min']) {
                $otherMarketMeetsCondition = true;
                break;
            }
        }
    }
    
    // 两个条件都满足时升级
    if($abTotal >= $lv['up_market_ab_total'] && $otherMarketMeetsCondition) {
        $isup = true;
    }
}

// 如果是"且"条件，则需要与其他条件一起判断
if(($isup || ($isup === false && $condition_or === false)) && ($lv['up_market_ab_total'] > 0 || $lv['up_market_other_min'] > 0) && $lv['up_market_condition'] == 'and'){
    // 获取所有直推下级
    $directDowns = Db::name('member')->field('id')->where('aid', $aid)->where('pid', $mid)->select()->toArray();
    $directDownIds = array_column($directDowns, 'id');
    
    // 计算每个市场的业绩
    $marketPerformances = [];
    foreach($directDownIds as $downId) {
        // 获取该下级及其所有下级
        $allSubordinates = self::getdownmids($aid, $downId, 0);
        $allSubordinates[] = $downId; // 包含直推下级自身
        
        // 计算该市场的业绩
        $marketPerformance = Db::name('shop_order_goods')
            ->where('status', 'in', '1,2,3')
            ->where('is_yeji', '1')
            ->where('mid', 'in', $allSubordinates)
            ->sum('real_totalprice');
            
        $marketPerformances[$downId] = $marketPerformance;
    }
    
    // 按业绩排序
    arsort($marketPerformances);
    
    // 检查条件1: A+B≥25万
    $topTwoMarkets = array_slice($marketPerformances, 0, 2, true);
    $abTotal = array_sum($topTwoMarkets);
    
    // 检查条件2: 其它任意一个市场业绩≥25万
    $otherMarkets = array_slice($marketPerformances, 2);
    $otherMarketMeetsCondition = false;
    foreach($otherMarkets as $performance) {
        if($performance >= $lv['up_market_other_min']) {
            $otherMarketMeetsCondition = true;
            break;
        }
    }
    
    // 两个条件都满足时才保持升级状态
    if(!($abTotal >= $lv['up_market_ab_total'] && $otherMarketMeetsCondition)) {
        $isup = false;
    }
}


// ... existing code ...
          ///// var_dump('第二个');
        ///  var_dump($isup);
            if($isup )
        {
    if($lv['up_zishen_levelid'] > 0)
    {
        $levelidsss = (string)$member['levelid'];
        // 将等级 ID 列表转换为数组
        $levelid_array = explode(',', str_replace('，', ',', $lv['up_zishen_levelid']));
        // 使用 in_array 精确匹配
        if(in_array($levelidsss, $levelid_array))
        {
            $isup = true;
        } else {
            $isup = false;
            $condition_or = true;
        }
    }
}


            /**且 判断直推多少人,多少市场出现等级idn*/
            if($isup || ($isup === false && $condition_or === false))
            {
                if($lv['up_zt_new2_duo'] >0)
                {
                 // var_dump('条件直推'.$lv['up_zt_new2_duo']);
                    $q = 0;
                    $zhituicount = Db::name('member')->field('id,nickname,createtime')->where('aid',$aid)->where('pid',$mid)->select()->toArray();
                   // var_dump('实际直推'.count($zhituicount));
                // var_dump(count($zhituicount));
                    if(count($zhituicount) >= $lv['up_zt_new2_duo'])
                    {
                        $qumemberid = '';
                       // var_dump('条件几个市场'.$lv['up_zt_new2_dengjiid_shichang1']);
                      //  var_dump('条件等级'.$lv['up_zt_new2_dengjiid_duo1']);
                        if($lv['up_zt_new2_dengjiid_shichang1'] >0 && $lv['up_zt_new2_dengjiid_duo1'] > 0 )
                        {
                            $memberlist = Db::name('member')->field('id,nickname,createtime,levelid')->where('aid',$aid)->where('pid',$mid)->select()->toArray();
                            $up_zt_new2_dengjiid_shichang1 = 0;
                            foreach($memberlist as $membervalue)
                            {
                                if($up_zt_new2_dengjiid_shichang1 <= $lv['up_zt_new2_dengjiid_shichang1']){
                                   // var_dump('当前我的等级'.$membervalue['levelid']);
                                    $up_zt_new2_dengjiid_duo1ids = explode(',',$lv['up_zt_new2_dengjiid_duo1']);
                                    if(in_array($membervalue['levelid'],$up_zt_new2_dengjiid_duo1ids) !==false)
                                    {
                                        $membervaluelist = $membervalue;
                                    }else{
                                        $membervaluelist = Db::name('member')->field('id,nickname,createtime,levelid')->where('aid',$aid)->where('find_in_set('.$membervalue['id'].',path)')->whereIn('levelid',$up_zt_new2_dengjiid_duo1ids)->select()->toArray();
                                        
                                    } 
                                    //var_dump(Db::getlastsql());
                                    if(!empty($membervaluelist))
                                    {
                                        $up_zt_new2_dengjiid_shichang1 = $up_zt_new2_dengjiid_shichang1+1;
                                        $qumemberid = ','.$membervalue['id'];
                                    }
                                }
                            }
                           // var_dump('去除的市场'.$qumemberid);
                           // var_dump('实际市场'.$up_zt_new2_dengjiid_shichang1);
                            if($up_zt_new2_dengjiid_shichang1 >= $lv['up_zt_new2_dengjiid_shichang1'])
                            {
                                 $isup = true;
                                 $q = 1;
                            }else{
                                $isup = false;
                                $condition_or = true; 
                                $q = 0;
                            }
                        }else{
                            $q = 1;
                        }
                        //var_dump('条件一市场条件');var_dump($isup);
                        if($lv['up_zt_new2_dengjiid_shichang2'] >0 && $lv['up_zt_new2_dengjiid_duo2'] > 0 && $q==1)
                        {
                            if($qumemberid)
                            {
                                $qumemberid = substr($qumemberid,0,1);
                                $memberlist = Db::name('member')->field('id,nickname,createtime,levelid')->where('aid',$aid)->where('pid',$mid)->where('id','not in',$qumemberid)->select()->toArray();
                            }else{
                                 $memberlist = Db::name('member')->field('id,nickname,createtime,levelid')->where('aid',$aid)->where('pid',$mid)->select()->toArray();
                            }
                            $up_zt_new2_dengjiid_shichang2 = 0;
                            foreach($memberlist as $membervalue)
                            {
                                $up_zt_new2_dengjiid_duo1ids = explode(',',$lv['up_zt_new2_dengjiid_duo2']);
                                if($up_zt_new2_dengjiid_shichang2 <= $lv['up_zt_new2_dengjiid_duo2']){
                                    if(in_array($membervalue['levelid'],$up_zt_new2_dengjiid_duo1ids) !==false)
                                    {
                                        $membervaluelist = $membervalue;
                                    }else{
                                        $membervaluelist = Db::name('member')->field('id,nickname,createtime,levelid')->where('aid',$aid)->where('find_in_set('.$membervalue['id'].',path)')->whereIn('levelid',$up_zt_new2_dengjiid_duo1ids)->select()->toArray();
                                    }
                                    if(!empty($membervaluelist))
                                    {
                                        $up_zt_new2_dengjiid_shichang2 = $up_zt_new2_dengjiid_shichang2+1;
                                    }
                                }
                            }
                            if($up_zt_new2_dengjiid_shichang2 >= $lv['up_zt_new2_dengjiid_shichang2'])
                            {
                                 $isup = true;
                                 $q = 1;
                            }else{
                                $isup = false;
                                $condition_or = true; 
                                $q = 0;
                            }
                        }else{
                            $q = 1;
                        }
                        if($lv['up_zt_new2_tuanduiyeji'] > 0 && $q==1)
                        {
                            $mytuandui = self::getmytuanduiyeji($aid,$mid);
                            if($mytuandui >= $lv['up_zt_new2_tuanduiyeji'])
                            {
                                $isup = true;
                            }else{
                                $isup = false;
                                $condition_or = true;
                            }
                        }
                    }else{
                        $isup = false;
                        $condition_or = true;
                    }
                }
            }
           // var_dump('判断直推多少人');
           // var_dump($isup);

            
            // var_dump('自身等级');var_dump($isup);
            if($isup || ($isup === false && $condition_or === false))
            {
               // var_dump('下单金额'.$ordermoneys);
                //var_dump('设置金额'.$lv['up_mei_order_monnnney'] );
                if($lv['up_mei_order_monnnney']  >  0)
                {
                    if($lv['up_mei_order_monnnney']  <= $ordermoneys)
                    {
                        $isup = true;
                    }else{
                         $isup = false;
                         $condition_or = true;
                    }
                }
            }
          /// var_dump('每次消费');var_dump($isup);
         ///  var_dump($isup);
           //  \think\facade\Log::write('and: ' . $isup);
            if($isup || ($isup === false && $condition_or === false))
            {
                //var_dump($lv['up_zt_new_duo']);
             //   \think\facade\Log::write('进来了 ' );
                if($lv['up_zt_new_duo']>0)
                {
                  // var_dump($lv['up_zt_new_dengjiid_duo']);
                    if(!empty($lv['up_zt_new_dengjiid_duo']))
                    {
                        $up_zt_new_dengjiid_duo_dayuids = $lv['up_zt_new_dengjiid_duo'];
                    }else{
                        $up_zt_new_dengjiid_duo_dayuids = Db::name('member_level')->where('aid',$aid)->order('sort,id')->select()->toArray(); 
                        $up_zt_new_dengjiid_duo_dayuids = array_column($up_zt_new_dengjiid_duo_dayuids,'id');
                        $up_zt_new_dengjiid_duo_dayuids = implode(',',$up_zt_new_dengjiid_duo_dayuids);
                    }
                    ///获取我的直推下级
                    $memberlist = Db::name('member')->field('id,nickname,createtime')->where('aid',$aid)->where('pid',$mid)->where('levelid','in',$up_zt_new_dengjiid_duo_dayuids)->select()->toArray();
                    $memberlistcount = count($memberlist);
                    if($memberlistcount >=$lv['up_zt_new_duo'])
                    {
                      //  var_dump($lv['up_zt_new_dengjiyeji_duo']);
                        if($lv['up_zt_new_dengjiyeji_duo'] >0)
                        {
                            $mytuandui = self::getmytuanduiyeji2($aid,$mid,$memberlist);
                           //var_dump($mytuandui);
                            if($mytuandui >= $lv['up_zt_new_dengjiyeji_duo'])
                            {
                                $isup = true;
                            }else{
                                 $isup = false;
                                 $condition_or = true;
                            }
                        }else{
                            $isup = true;
                        }
                    }else{
                         $isup = false;
                         $condition_or = true;
                    }
                }
              //  \think\facade\Log::write('出去了 ' . $isup);
            }
           ///var_dump('最多人数');var_dump($isup);
           ///var_dump($isup);
           
             $condition_or2 = false;
            //   && $lv['up_mydengji'] ==0
            if(($isup || ($isup === false && $condition_or === false)) && $lv['up_tuandui'] >0){
                $myxiatwodi = self::getZaoTwo($aid,$mid);
                if($myxiatwodi >= $lv['up_tuandui'])
                {
                     $isup = true;
                   
                }else{
                     $isup = false;
                     $condition_or2 = true;
                   
                }
                
            }
          //  var_dump('团队');var_dump($isup);
            if(($isup || ($isup === false && $condition_or2 === false && $condition_or === false)) && $lv['up_orderpaymoney'] >0 && $lv['up_mydengji'] ==0)
            {
                if($orderid >0)
                {
                    $order = Db::name('shop_order')->where('id',$orderid)->find();
                    if($mid == $order['mid']){
                        if($order['product_price'] >= $lv['up_orderpaymoney'])
                        {
                            $isup = true;
                        }else{
                            $isup = false;
                        }
                    }else{
                        $resss = Db::name('shop_order')->where('aid',$aid)->where('mid',$mid)->where('product_price','>=',$lv['up_orderpaymoney'])->where('status','in','1,2,3')->select()->toArray();
                        if(empty($resss)){
                            $isup = false;
                        }else{
                            $isup = true;
                        }
                    }
                }else{
                    $isup = false;
                }
            }
           // var_dump('最后一个');var_dump($isup);
            // if(($isup || ($isup === false && $condition_or2 === false && $condition_or === false))
            // {
            //     if($levelid)
            //     {
                    
            //     }
            // }
//                         var_dump($isup);
// var_dump($condition_or);die;
//var_dump($lv);die;
            if($isup) $newlv = $lv;
            
        }
        // die;
       //判断等级 是否进行了降级
        $member_dis=Db::name('member')->where('aid', $aid)->where('id', $mid)->find();
        // 获取当前会员等级的排序值
        $current_level = Db::name('member_level')->where('aid',$aid)->where('id',$member_dis['levelid'])->find();
        
        // die;
        // 修改判断条件，先比较sort字段，再比较id字段
        // 原来的判断: if($newlv && $newlv['id'] != $levelInfo['levelid'] && $newlv['id']>$member_dis['levelid'])
        if($newlv && $newlv['id'] != $levelInfo['levelid'] && 
           ($newlv['sort'] > $current_level['sort'] || ($newlv['sort'] == $current_level['sort'] && $newlv['id'] > $member_dis['levelid']))) {
            
            $curlevelid = Db::name('member_level')->where('aid',$aid)->where('id',$member_dis['levelid'])->find();
            if($curlevelid['huigui_status'] ==1 && $member_dis['ylevelid'] >0)
            {
                $ylevelidArr = Db::name('member_level')->where('aid',$aid)->where('id',$member_dis['ylevelid'])->find();
                if(!empty($ylevelidArr))
                {
                    $newlv = $ylevelidArr;
                }
            }
            if ($newlv['yxqdate'] > 0) {
                $levelendtime = strtotime(date('Y-m-d')) + 86400 + 86400 * $newlv['yxqdate'];
            } else {
                $levelendtime = 0;
            }
            //判断是否默认分组
            if($newlv['cid'] > 0)
                $is_default = Db::name('member_level_category')->where('id', $newlv['cid'])->value('isdefault');
            if ($is_default || $newlv['cid'] == 0) {
                Db::name('member')->where('aid', $aid)->where('id', $mid)->update(['levelid' => $newlv['id'], 'levelendtime' => $levelendtime]);
                if(false){}else {
                    Db::name('member')->where('aid', $aid)->where('id', $mid)->update(['levelstarttime' => time()]);
                }
            } else {
                }
            /**
             * 锁定培养人
             * */
            self::getsuoding($aid,$newlv['id'],$mid);
            
            Wechat::updatemembercard($aid, $mid);
            //赠送积分
            if($newlv['up_give_score'] > 0) {
                self::addscore($aid, $mid, $newlv['up_give_score'], '升级奖励');
            }
            //升级赠送现金券
            if($newlv['up_give_xianjinquan'] >0)
            {
                 self::addhei($aid, $mid, $newlv['up_give_xianjinquan'], '升级奖励');
            }
            //奖励佣金
            if($newlv['up_give_commission'] > 0) {
                self::addcommission($aid,$mid,0,$newlv['up_give_commission'],'升级奖励');
            }
            //奖励余额
            if($newlv['up_give_money'] > 0) {
                self::addmoney($aid,$mid,$newlv['up_give_money'],'升级奖励');
            }
                        //奖励贡献值
            if($newlv['up_give_gongxianzhi'] > 0) {
                self::addgongxianzhi($aid,$mid,$newlv['up_give_gongxianzhi'],'升级奖励贡献值');
            }
             //赠送上级贡献值
            if($newlv['up_give_parent_gongxianzhi'] > 0 && $member['pid']) {
                self::addgongxianzhi($aid, $member['pid'], $mid, $newlv['up_give_parent_gongxianzhi'], '直推奖励贡献值');
            }
            //赠送上级佣金
            if($newlv['up_give_parent_money'] > 0 && $member['pid']) {
                self::addcommission($aid, $member['pid'], $mid, $newlv['up_give_parent_money'], '直推奖');
            }
            self::sendpyj($aid,$mid, $newlv['id']);
            //升级完 判断是否需要脱离
            self::liandongtuoli($aid,$mid);
            //脱离完 判断是否解冻
            self::jiedongpanduan($aid,$mid);
            
            if($newlv['up_give_parent_coupon_ids'] && $newlv['up_give_parent_coupon_nums'] && $member['pid']){
                $coupon_ids = explode(',',$newlv['up_give_parent_coupon_ids']);
                $coupon_nums = explode(',',$newlv['up_give_parent_coupon_nums']);
                if($coupon_ids){
                    foreach($coupon_ids as $ck=>$coupon_id){
                        if(!$coupon_nums[$ck]){
                            $coupon_nums[$ck] = 1;
                        }
                        for($i=0;$i<$coupon_nums[$ck];$i++){
                            \app\common\Coupon::send($aid,$member['pid'],$coupon_id,true);
                        }
                    }
                }
            }

            //升级赠送优惠券
            //升级记录
            $order = [
                'aid' => $aid,
                'mid' => $mid,
                'from_mid' => $mid,
                'levelid' => $newlv['id'] ,
                'title' => '自动升级',
                'totalprice' => 0,
                'createtime' => time(),
                'levelup_time' => time(),
                'beforelevelid' => $levelInfo['levelid'],
                'form0' => '类型^_^自动升级',
                'platform' => platform,
                'status' => 2
            ];
            Db::name('member_levelup_order')->insert($order);

            $tmplcontent = [];
            $tmplcontent['first'] = '恭喜您成功升级为'.$newlv['name'];
            $tmplcontent['remark'] = '点击进入查看~';
            $tmplcontent['keyword1'] = $newlv['name']; //会员等级
            $tmplcontent['keyword2'] = '已生效';//审核状态
            $rs = \app\common\Wechat::sendtmpl($aid,$mid,'tmpl_uplv',$tmplcontent,m_url('pages/my/usercenter', $aid));

            }

        if(!$isup && $newlv['id'] == $nowlv['id']){
            }
    }
    
    /**
     * 锁定培养人
     * */
     public static function getsuoding($aid,$newlevelid,$mid)
     {
         $suo1 = 0;
         $sou2 = 0;
         $member = Db::name('member')->field('id,path,levelid')->where('aid',$aid)->where('id',$mid)->find();
         $memberlevel = Db::name('member_level')->where('aid',$aid)->where('id',$newlevelid)->find();
         $pathArr = explode(',',$member['path']);
         $pathArr = array_reverse($pathArr);
         $jiangliArr1 = explode(',',$memberlevel['suoding_levelid1']);
         $jiangliArr2 = explode(',',$memberlevel['suoding_levelid2']);
         foreach($pathArr as $v1)
         {
              $membershangji = Db::name('member')->field('id,path,levelid')->where('aid',$aid)->where('id',$v1)->find();
               if(in_array($membershangji['levelid'],$jiangliArr1) !== false && $suo1 ==0)
               {
                   $suo1 = $v1;
               }
               if(in_array($membershangji['levelid'],$jiangliArr2) !== false  && $sou2 ==0)
               {
                   $sou2 = $v1;
               }
               if($suo1 != 0 && $sou2!=0)
               {
                   break;
               }
         }
         $data =  ['suo1'=>$suo1,'suo2'=>$sou2,'suo1jiang'=>$memberlevel['suodingpeiyang1'],'suo2jiang'=>$memberlevel['suodingpeiyang2']];
         Db::name('member')->where('aid',$aid)->where('id',$mid)->update($data);
     }
    /**
     * 培育奖
     * 下级升级到等级id  0 拿该会员团队业绩的 0.00%  ,去除团队内等级id  的销售业绩
     * */
    public static function sendpyj($aid,$mid,$levelid)
    {
         $member = Db::name('member')->field('id,path,levelid')->where('aid',$aid)->where('id',$mid)->find();
         $memberlevel = Db::name('member_level')->where('aid',$aid)->where('id',$member['levelid'])->find();
         $memberzhi = Db::name('member')->field('id,path,levelid')->where('aid',$aid)->where('pid',$mid)->select()->toArray();
         $pathArr = explode(',',$member['path']);
         foreach($pathArr as $k1=>$v1)
         {
             //上级信息
             $membershangji = Db::name('member')->field('id,path,levelid')->where('aid',$aid)->where('id',$v1)->find();
             $memberlevelshangji = Db::name('member_level')->where('aid',$aid)->where('id',$membershangji['levelid'])->find();//上级获取levelArr
             //获取上级的培育奖信息
             if($memberlevelshangji['pyj_dengji'] >0 && $memberlevelshangji['pyj_dengji'] == $levelid)
             {
                 if($memberlevelshangji['pyj_qudengji'] >0)
                 {
                     $yaozhituixiaji= [];
                     foreach($memberzhi as $k2=>$v2)
                     {
                         if($v2['levelid'] != $memberlevelshangji['pyj_qudengji'])
                         {
                             $yaozhituixiaji[] = $v2['id'];
                         }
                     }
                     $myxiaji = [];
                     foreach ($yaozhituixiaji as $v3)
                     {
                         $fenxiji = self::getdownmids($aid,$v3);
                         $fenxiji[] =$v3;
                         $myxiaji = array_merge($myxiaji,$fenxiji);
                     }
                     
                    //  var_dump($myxiaji);die;
                     
                 }else{
                     $myxiaji = self::getdownmids($aid,$mid);
                 }
                  $myxiajistr = implode(',',$myxiaji);
                  $myxiajicount = Db::name('shop_order')->where('aid',$aid)->where('mid','in',$myxiajistr)->where('status','in','1,2,3')->sum('product_price');
                  $fanqian = $myxiajicount * $memberlevelshangji['pyj_baifenbi']*0.01;//pyj_baifenbi
                   if($fanqian > 0){
                    //   self::addmoney($aid,$mid,$fanqian,'培育奖:'.$fanqian);
                      self::addcommissionmy($aid,$mid,$mid,$fanqian,'培育奖:'.$fanqian,'佣金');
                   }
             }
         }
    }
    
    /**
     * 我的团队业绩
     * */
    public static function getmytuanduiyeji($aid,$mid)
    {
        $myxiaji = self::getdownmids($aid,$mid);
        $myxiajistr = implode(',',$myxiaji);
         //分别获取团队业绩
        $myxiajicount = Db::name('shop_order')->where('aid',$aid)->where('mid','in',$myxiajistr)->where('status','in','1,2,3')->sum('product_price');
        return $myxiajicount;
    }
    
    /**
     * 根据会员id获取 业绩
     * */
     public static function getmytuanduiyeji2($aid,$mid,$memberlist)
     {
         $mids = array_column($memberlist,'id');
         $midstr = implode(',',$mids);
         //分别获取团队业绩
         $myxiajicount = Db::name('shop_order')->where('aid',$aid)->where('mid','in',$midstr)->where('status','in','1,2,3')->sum('product_price');
         return $myxiajicount;
     }
    /**
     * 我的团队业绩去等级
     * */
     
    public static function getmytuanduiyejiqudengji($aid,$mid,$cyjj_qudengji)
    {
        $myxiaji = self::getdownmids2($aid,$mid,$cyjj_qudengji);
        $myxiajistr = implode(',',$myxiaji);
        
         //分别获取团队业绩
        $myxiajicount = Db::name('shop_order')->where('aid',$aid)->where('mid','in',$myxiajistr)->where('status','in','1,2,3')->sum('totalprice');
        return $myxiajicount;
    }
    /**
    *发开发奖 开发奖直推下级所有业绩每达到25元就奖励12800
    **/
    public static function sendkfj($aid,$mid)
    {
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->find();
        if(!empty($member))
        {
            
            $memberlevel = Db::name('member_level')->where('aid',$aid)->where('id',$member['levelid'])->find();
            if(!empty($memberlevel))
            {
                //我的直属下级
                $memberlist = Db::name('member')->field('id')->where('aid',$aid)->where('pid',$mid)->order('createtime','asc')->select()->toArray();
                if(!empty($memberlist)){
                    $memberids = array_column($memberlist,'id');
                    $myxiajistr = implode(',',$memberids);
                     // kfj_shengyu
                    $myxiajiorder= Db::name('shop_order')->where('aid',$aid)->field('id,kfj_shengyu')->where('mid','in',$myxiajistr)->where('status','in','1,2,3')->where('kfj_shengyu','>',0)->select()->order('id','desc')->toArray();
                    $kfj_shengyu = 0;
                    foreach($myxiajiorder as $k=>$v)
                    {
                       $kfj_shengyu = $kfj_shengyu+ $v['kfj_shengyu'];
                    }
                    $bei = floor($kfj_shengyu/$memberlevel['kfj_yeji']);
                    $fanqian = $memberlevel['kfj_jiangli']*$bei;
                    if($fanqian > 0){
                        $shengyu = $kfj_shengyu - ($bei*$memberlevel['kfj_yeji']);
                         self::addcommissionmy($aid,$mid,$mid,$fanqian,'直推下级所有业绩每达到'.$memberlevel['kfj_yeji'].'元,奖励'.$memberlevel['kfj_jiangli'].'元,现业绩达到'.$kfj_shengyu.'元,剩余:'.$shengyu.'元,奖励开发奖'.$fanqian,'佣金');
                        // self::addmoney($aid,$mid,$fanqian,'直推下级所有业绩每达到'.$memberlevel['kfj_yeji'].'元,奖励'.$memberlevel['kfj_jiangli'].'元,现业绩达到'.$kfj_shengyu.'元,剩余:'.$shengyu.'元,奖励开发奖'.$fanqian,'元');
                        $orderids = array_column($myxiajiorder,'id');
                        $orderids = implode(',',$orderids);
                        Db::name('shop_order')->where('aid',$aid)->where('id','in',$orderids)->update(['kfj_shengyu'=>0]);
                        Db::name('shop_order')->where('aid',$aid)->where('id',$myxiajiorder[0]['id'])->update(['kfj_shengyu'=>$shengyu]);
                    }
                }
            }
        }
    }
    
    /** 创业基金
     * 团队业绩每累积到  0.00 元且不包含等级id  0  的业绩,达到就奖励  0.00  佣金,往上找第一个等级id  奖励 元
     * */
     public static function  sendcyjijin($aid,$mid)
     {
         //获取我的所有上级
         $member = Db::name('member')->field('id,path')->where('aid',$aid)->where('id',$mid)->find();
         $pathArr = explode(',',$member['path']);
         foreach($pathArr as $k=>$v)
         {
           
              $memberbenren = Db::name('member')->field('id,levelid')->where('aid',$aid)->where('id',$v)->find();
              $memberlevel = Db::name('member_level')->where('aid',$aid)->where('id',$memberbenren['levelid'])->find();
              if(!empty($memberlevel) && $memberlevel['cyjj_tuandui'] >0){
                      //获取我的不包含等级 levelid 的 全部下级
                      if($memberlevel['cyjj_qudengji'] >0)
                      {
                            $myxiaji = self::getdownmids2($aid,$v,$memberlevel['cyjj_qudengji']);
                      }else{
                            $myxiaji = self::getdownmids($aid,$v);
                      }
                      $myxiajistr = implode(',',$myxiaji);
                      $myxiajiorder= Db::name('shop_order')->where('aid',$aid)->field('id,cyjj_shengyu')->where('mid','in',$myxiajistr)->where('status','in','1,2,3')->where('cyjj_shengyu','>',0)->select()->order('id','desc')->toArray();
                      $cyjj_shengyu = 0;
                      if(!empty($myxiajiorder)){
                          foreach($myxiajiorder as $k2=>$v2)
                          {
                               $cyjj_shengyu = $cyjj_shengyu+ $v2['cyjj_shengyu'];
                          }
                          if($cyjj_shengyu >= $memberlevel['cyjj_tuandui'])
                          {
                              $bei = floor($cyjj_shengyu/$memberlevel['cyjj_tuandui']);
                              $fanqian = $memberlevel['cyjj_jiangli']*$bei;
                              if($fanqian > 0){
                                  $shengyu = $cyjj_shengyu - ($bei*$memberlevel['cyjj_tuandui']);
                                  //獎勵我佣金
                                  self::addcommissionmy($aid,$v,$mid,$fanqian,'创业基金:团队业绩每累积到'.$memberlevel['cyjj_tuandui'].'奖励'.$memberlevel['cyjj_jiangli'].',共奖励佣金'.$fanqian);
                                  $orderids = array_column($myxiajiorder,'id');
                                  $orderids = implode(',',$orderids);
                                  Db::name('shop_order')->where('aid',$aid)->where('id','in',$orderids)->update(['cyjj_shengyu'=>0]);
                                  Db::name('shop_order')->where('aid',$aid)->where('id',$myxiajiorder[0]['id'])->update(['cyjj_shengyu'=>$shengyu]);
                                  //往上找第一个等级id  奖励 元
                                  if($memberlevel['cyjj_shangdengji'] >0 && $memberlevel['cyjj_shangjiangli'] >0)
                                  {
                                       $shangid = self::getshangdengji($aid,$memberlevel['cyjj_shangdengji'],$v);
                                       if($shangid >0){
                                            self::addcommissionmy($aid,$shangid,$mid,$memberlevel['cyjj_shangjiangli'],'创业基金:往上找第一个等级id'.$memberlevel['cyjj_shangdengji'].',奖励 '.$memberlevel['cyjj_shangjiangli'].'佣金');
                                        //   self::addmoney($aid,$shangid,$memberlevel['cyjj_shangjiangli'],);
                                       }
                                  }
                              }
                          }
                        
                      }
               }
         }
     }
     
     /**
      * 获得最近的上一级
      * */
    public static function getshangdengji($aid,$cyjj_shangdengji,$mid)
    {
         $memberbenren = Db::name('member')->field('id,path')->where('aid',$aid)->where('id',$mid)->find();
         $pathArr = explode(',',$memberbenren['path']);
         $pathArr = array_reverse($pathArr);
         $returnid = 0;
         foreach($pathArr as $v)
         {
            $bbbmember = Db::name('member')->field('id,levelid')->where('aid',$aid)->where('id',$v)->find();
          
            if($bbbmember['levelid'] == $cyjj_shangdengji)
            {
                return $v;
            }
         }
         return $returnid;
    }
    
    /**
     * 获取最早推荐的两个人的团队业绩
     * */
    public static function getZaoTwo($aid,$mid)
    {
        $res = Db::name('member')->field('id,nickname,createtime')->where('aid',$aid)->where('pid',$mid)->order('createtime','asc')->limit(2)->select()->toArray();
        $returnArr =[];
        foreach($res as $k=>$v)
        {
            //分别获取两个用户的下级
            $myxiaji = self::getdownmids($aid,$v['id']);
            if (!empty($myxiaji)) {
                 $myxiajistr = $v['id'].','.implode(',',$myxiaji);
            }else{
                 $myxiajistr = (string)$v['id'];
            }
            //分别获取团队业绩
            $myxiajicount = Db::name('shop_order')->where('aid',$aid)->where('mid','in',$myxiajistr)->where('status','in','1,2,3')->sum('totalprice');
            if($myxiajicount)
            {
                 $returnArr[]=$myxiajicount;
            }else{
                 $returnArr[]=0;  
            }
           
        }
        if(count($returnArr) == 1)
        {
            return 0;
        }else{
            if($returnArr[0] >= $returnArr[1])
            {
                return $returnArr[1];
            }else{
              return $returnArr[0]; 
            } 
        }
    }
    
    /**
     * 获取最早推荐两个人的团队
     * */
    public static function getQianTuandui($aid,$mid)
    {
        $res = Db::name('member')->field('id,nickname,createtime')->where('aid',$aid)->where('pid',$mid)->order('createtime','asc')->limit(2)->select()->toArray();
        $return = [];
        foreach($res as $k=>$v)
        {
            // $return[]  = $v['id'];
            //分别获取两个用户的全部下级
            $myxiaji = self::getdownmids($aid,$v['id']);
            $return = array_merge($return,$myxiaji);
        }
        return $return;
    }
//加欠款
public static function addarrears($aid, $mid, $arrears, $remark, $frommid = 0)
{
    if ($arrears == 0) return;
    $member = Db::name('member')->where('aid', $aid)->where('id', $mid)->lock(true)->find();
    if (!$member) return ['status' => 0, 'msg' => t('会员') . '不存在'];

    $after = $member['arrears'] + $arrears;
    Db::name('member')->where('aid', $aid)->where('id', $mid)->update(['arrears' => $after]);

    $data = [];
    $data['aid'] = $aid;
    $data['mid'] = $mid;
    $data['arrears'] = $arrears;
    $data['after'] = $after;
    $data['createtime'] = time();
    $data['remark'] = $remark;
    if (getcustom('arrears_transfer')) {
        $data['from_mid'] = $frommid;
    }
    Db::name('member_arrearslog')->insert($data);
    self::uplv($aid, $mid);
    Wechat::updatemembercard($aid, $mid);

    $tmplcontent = [];
    $tmplcontent['first'] = '您的' . t('欠款') . '发生变动，变动金额：' . $arrears;
    $tmplcontent['remark'] = '点击进入查看~';
    $tmplcontent['keyword1'] = date('Y-m-d H:i'); //变动时间
    $tmplcontent['keyword2'] = $remark;  //变动类型
    $tmplcontent['keyword3'] = (string)round($arrears, 2);  //变动金额
    $tmplcontent['keyword4'] = (string)round($after, 2);  //当前欠款
    $tmplcontentNew = [];
    $tmplcontentNew['thing2'] = str_replace(',', '', mb_substr($remark, 0, 5)); //消费项目
    $tmplcontentNew['amount3'] = round($arrears, 2); //消费金额
    $tmplcontentNew['amount4'] = round($after, 2); //卡内欠款
    $tmplcontentNew['time6'] = date('Y-m-d H:i'); //变动时间
    $rs = \app\common\Wechat::sendtmpl($aid, $mid, 'tmpl_arrearschange', $tmplcontent, m_url('pages/my/usercenter', $aid), $tmplcontentNew);
    //变动通知
    if (getcustom('sms_temp_arrears_use')) {
        if ($member['tel'] && $arrears < 0) {
            $rs = \app\common\Sms::send($aid, $member['tel'], 'tmpl_arrears_use', ['arrears' => $arrears, 'real_arrears' => $arrears, 'sy_arrears' => $after]);
        }
    }
    return ['status' => 1, 'msg' => ''];
}

     //加余额
    public static function addmoney($aid,$mid,$money,$remark,$frommid=0,$paytype=''){
        if($money==0) return ;
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
        if(!$member) return ['status'=>0,'msg'=>t('会员').'不存在'];

        if(getcustom('w7moneyscore')) {
            $w7moneyscore = db('admin_set')->where(['aid'=>$aid])->value('w7moneyscore');
            if($w7moneyscore == 1){
                return self::addw7moneyscore($aid,$member,2,$money,$remark);
            }else{
                // 使用高精度计算，支持5位小数
                $after = bcadd($member['money'], $money, 5);
                Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['money'=>$after]);
            }
        } else {
            // 使用高精度计算，支持5位小数
            $after = bcadd($member['money'], $money, 5);
            Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['money'=>$after]);
        }

        $data = [];
        $data['aid'] = $aid;
        $data['mid'] = $mid;
        $data['money'] = $money;
        $data['after'] = $after;
        $data['createtime'] = time();
        $data['remark'] = $remark;
        //$data['paytype'] = $paytype;
        if(getcustom('money_transfer') || getcustom('money_friend_transfer')) {
            $data['from_mid'] = $frommid;
        }
        Db::name('member_moneylog')->insert($data);
        self::uplv($aid,$mid);
        Wechat::updatemembercard($aid,$mid);

        $tmplcontent = [];
        $tmplcontent['first'] = '您的'.t('余额').'发生变动，变动金额：'.$money;
        $tmplcontent['remark'] = '点击进入查看~';
        $tmplcontent['keyword1'] = date('Y-m-d H:i'); //变动时间
        $tmplcontent['keyword2'] = $remark;  //变动类型
        $tmplcontent['keyword3'] = (string) round($money,2);  //变动金额
        $tmplcontent['keyword4'] = (string) round($after,2);  //当前余额
        $tmplcontentNew = [];
        $tmplcontentNew['thing2'] = str_replace(',','',mb_substr($remark,0,5));//消费项目
        $tmplcontentNew['amount3'] = round($money,2);//消费金额
        $tmplcontentNew['amount4'] = round($after,2);//卡内余额
        $tmplcontentNew['time6'] = date('Y-m-d H:i'); //变动时间
        $rs = \app\common\Wechat::sendtmpl($aid,$mid,'tmpl_moneychange',$tmplcontent,m_url('pages/my/usercenter', $aid),$tmplcontentNew);
         //变动通知
        if(getcustom('sms_temp_money_use')){
            if($member['tel'] && $money < 0){
                $rs = \app\common\Sms::send($aid,$member['tel'],'tmpl_money_use',['money'=>$money,'real_money'=>$money,'sy_money'=>$after]);
            }
        }
        return ['status'=>1,'msg'=>''];
    }
    //加额度
    public static function addtuozhanedu($aid,$mid,$money,$remark,$orderid=0){
        if($money==0) return ;
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
        if(!$member) return ['status'=>0,'msg'=>t('会员').'不存在'];

        if(false){}else {
            $after = $member['tuozhanedu'] + $money;
            Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['tuozhanedu'=>$after]);
        }

        $data = [];
        $data['aid'] = $aid;
        $data['mid'] = $mid;
        $data['money'] = $money;
        $data['after'] = $after;
        $data['createtime'] = time();
        $data['remark'] = $remark;
        Db::name('member_edulog')->insert($data);
        $tmplcontent = [];
        $tmplcontent['first'] = '您的'.t('拓展额度').'发生变动，变动金额：'.$money;
        $tmplcontent['remark'] = '点击进入查看~';
        $tmplcontent['keyword1'] = date('Y-m-d H:i'); //变动时间
        $tmplcontent['keyword2'] = $remark;  //变动类型
        $tmplcontent['keyword3'] = (string) round($money,2);  //变动金额
        $tmplcontent['keyword4'] = (string) round($after,2);  //当前余额
        $rs = \app\common\Wechat::sendtmpl($aid,$mid,'tmpl_moneychange',$tmplcontent,m_url('pages/my/usercenter', $aid));
        return ['status'=>1,'msg'=>''];
    }
    //加heijifen
    public static function addhei($aid,$mid,$money,$remark,$orderid=0){
        if($money==0) return ;
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
        if(!$member) return ['status'=>0,'msg'=>t('会员').'不存在'];
        if(false){}else {
            // 使用高精度计算，支持5位小数
            $after = bcadd($member['heiscore'], $money, 5);
            Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['heiscore'=>$after]);
        }

        $data = [];
        $data['aid'] = $aid;
        $data['mid'] = $mid;
        $data['money'] = $money;
        $data['after'] = $after;
        $data['createtime'] = time();
        $data['remark'] = $remark;
        Db::name('member_heiscorelog')->insert($data);
        // self::uplv($aid,$mid,$orderid);
        Wechat::updatemembercard($aid,$mid);

        
        $tmplcontent = [];
        $tmplcontent['first'] = '您的'.t('现金券').'发生变动，变动金额：'.$money;
        $tmplcontent['remark'] = '点击进入查看~';
        $tmplcontent['keyword1'] = date('Y-m-d H:i'); //变动时间
        $tmplcontent['keyword2'] = $remark;  //变动类型
        $tmplcontent['keyword3'] = (string) round($money,2);  //变动金额
        $tmplcontent['keyword4'] = (string) round($after,2);  //当前余额
        $tmplcontentNew = [];
        $tmplcontentNew['thing2'] = '您的'.t('现金券').'发生变动，变动金额：'.$money;;//消费项目
        $tmplcontentNew['amount3'] = round($money,2);//消费金额
        $tmplcontentNew['amount4'] = round($after,2);//卡内余额
        $tmplcontentNew['time6'] = date('Y-m-d H:i'); //变动时间
        $rs = \app\common\Wechat::sendtmpl($aid,$mid,'tmpl_moneychange',$tmplcontent,m_url('pages/my/usercenter', $aid),$tmplcontentNew);
        
        return ['status'=>1,'msg'=>''];
    }
    
    //加heijifen
    public static function addshengjiscore($aid,$mid,$money,$remark,$orderid=0){
        if($money==0) return ;
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
        if(!$member) return ['status'=>0,'msg'=>t('会员').'不存在'];
        if(false){}else {
            $after = $member['shengjiscore'] + $money;
            Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['shengjiscore'=>$after]);
        }

        $data = [];
        $data['aid'] = $aid;
        $data['mid'] = $mid;
        $data['money'] = $money;
        $data['after'] = $after;
        $data['createtime'] = time();
        $data['remark'] = $remark;
        Db::name('member_shengjiscorelog')->insert($data);
        // self::uplv($aid,$mid,$orderid);
        Wechat::updatemembercard($aid,$mid);

        
        $tmplcontent = [];
        $tmplcontent['first'] = '您的'.t('升级积分').'发生变动，变动金额：'.$money;
        $tmplcontent['remark'] = '点击进入查看~';
        $tmplcontent['keyword1'] = date('Y-m-d H:i'); //变动时间
        $tmplcontent['keyword2'] = $remark;  //变动类型
        $tmplcontent['keyword3'] = (string) round($money,2);  //变动金额
        $tmplcontent['keyword4'] = (string) round($after,2);  //当前余额
        $tmplcontentNew = [];
        $tmplcontentNew['thing2'] = '您的'.t('升级积分').'发生变动，变动金额：'.$money;;//消费项目
        $tmplcontentNew['amount3'] = round($money,2);//消费金额
        $tmplcontentNew['amount4'] = round($after,2);//卡内余额
        $tmplcontentNew['time6'] = date('Y-m-d H:i'); //变动时间
        $rs = \app\common\Wechat::sendtmpl($aid,$mid,'tmpl_moneychange',$tmplcontent,m_url('pages/my/usercenter', $aid),$tmplcontentNew);
        
        return ['status'=>1,'msg'=>''];
    }
    
    //分红收益
    public static function addFenhongShouyi($aid, $mid, $fenhongShouyi, $remark, $orderid=0){
    if($fenhongShouyi == 0) return ['status' => 0, 'msg' => '变动金额不能为零'];
    
    // 加锁查询以保证数据一致性
    $member = Db::name('member')->where('aid', $aid)->where('id', $mid)->lock(true)->find();
    if(!$member) return ['status' => 0, 'msg' => '会员不存在'];
    
    // 更新分红收益
    $after = $member['zijifh'] + $fenhongShouyi;
    Db::name('member')->where('aid', $aid)->where('id', $mid)->update(['zijifh' => $after]);

    // 插入分红收益变动日志
    $data = [];
    $data['aid'] = $aid;
    $data['mid'] = $mid;
    $data['money'] = $fenhongShouyi;
    $data['after'] = $after;
    $data['createtime'] = time();
    $data['remark'] = $remark;
    Db::name('member_fenhongshouyilog')->insert($data); // 确保有对应的数据表和字段

    // 此处可添加任何额外的业务逻辑，比如更新会员等级等
    // self::uplv($aid, $mid, $orderid);

    // 更新会员的微信卡信息（如果有必要）
    // \app\common\Wechat::updatemembercard($aid, $mid);

    // 发送微信模板消息通知用户（如果有必要）
    /*
    $tmplcontent = [];
    $tmplcontent['first'] = '您的分红收益发生变动，变动金额：'.$fenhongShouyi;
    $tmplcontent['remark'] = '点击进入查看~';
    $tmplcontent['keyword1'] = date('Y-m-d H:i'); //变动时间
    $tmplcontent['keyword2'] = $remark; //变动原因
    $tmplcontent['keyword3'] = (string) round($fenhongShouyi,2); //变动金额
    $tmplcontent['keyword4'] = (string) round($after,2); //当前余额
    \app\common\Wechat::sendtmpl($aid, $mid, 'tmpl_moneychange', $tmplcontent, m_url('pages/my/usercenter', $aid));
    */
    
    return ['status' => 1, 'msg' => '分红收益更新成功'];
}

    //加duihuanfen
    public static function addduihuanfen($aid,$mid,$money,$remark,$orderid=0){
        if($money==0) return ;
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
        if(!$member) return ['status'=>0,'msg'=>t('会员').'不存在'];
        if(false){}else {
            $after = $member['duihuanfen'] + $money;
            Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['duihuanfen'=>$after]);
        }

        $data = [];
        $data['aid'] = $aid;
        $data['mid'] = $mid;
        $data['money'] = $money;
        $data['after'] = $after;
        $data['createtime'] = time();
        $data['remark'] = $remark;
        Db::name('member_duihuanfenlog')->insert($data);
        // self::uplv($aid,$mid,$orderid);
        Wechat::updatemembercard($aid,$mid);

        $tmplcontent = [];
        $tmplcontent['first'] = '您的'.t('兑换虚拟账号积分').'发生变动，变动金额：'.$money;
        $tmplcontent['remark'] = '点击进入查看~';
        $tmplcontent['keyword1'] = date('Y-m-d H:i'); //变动时间
        $tmplcontent['keyword2'] = $remark;  //变动类型
        $tmplcontent['keyword3'] = (string) round($money,2);  //变动金额
        $tmplcontent['keyword4'] = (string) round($after,2);  //当前余额
        $rs = \app\common\Wechat::sendtmpl($aid,$mid,'tmpl_moneychange',$tmplcontent,m_url('pages/my/usercenter', $aid));
        return ['status'=>1,'msg'=>''];
    }
  //加红包后台
    public static function addhuang($aid,$mid,$money,$remark,$orderid=0){
        if($money==0) return ;
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
        if(!$member) return ['status'=>0,'msg'=>t('会员').'不存在'];
        if(false){}else {
            // 使用高精度计算，支持5位小数
            $after = bcadd($member['scorehuang'], $money, 5);
            Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['scorehuang'=>$after]);
        }

        $data = [];
        $data['aid'] = $aid;
        $data['mid'] = $mid;
        $data['score'] = $money;
        $data['after'] = $after;
        $data['createtime'] = time();
        $data['remark'] = $remark;
        Db::name('member_scoreloghuang')->insert($data);
        // self::uplv($aid,$mid,$orderid);
        Wechat::updatemembercard($aid,$mid);

        $tmplcontent = [];
        $tmplcontent['first'] = '您的'.t('红包').'发生变动，变动金额：'.$money;
        $tmplcontent['remark'] = '点击进入查看~';
        $tmplcontent['keyword1'] = date('Y-m-d H:i'); //变动时间
        $tmplcontent['keyword2'] = $remark;  //变动类型
        $tmplcontent['keyword3'] = (string) round($money,2);  //变动金额
        $tmplcontent['keyword4'] = (string) round($after,2);  //当前余额
        $rs = \app\common\Wechat::sendtmpl($aid,$mid,'tmpl_moneychange',$tmplcontent,m_url('pages/my/usercenter', $aid));
        return ['status'=>1,'msg'=>''];
    }
    
    
      //加股权后台
    public static function addguquan($aid,$mid,$money,$remark,$orderid=0){
        if($money==0) return ;
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
        if(!$member) return ['status'=>0,'msg'=>t('会员').'不存在'];
        if(false){}else {
            $after = $member['guquan'] + $money;
            Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['guquan'=>$after]);
        }

        $data = [];
        $data['aid'] = $aid;
        $data['mid'] = $mid;
        $data['money'] = $money;
        $data['after'] = $after;
        $data['createtime'] = time();
        $data['remark'] = $remark;
        Db::name('member_guquanscorelog')->insert($data);
        // self::uplv($aid,$mid,$orderid);
        Wechat::updatemembercard($aid,$mid);

        $tmplcontent = [];
        $tmplcontent['first'] = '您的'.t('红包').'发生变动，变动金额：'.$money;
        $tmplcontent['remark'] = '点击进入查看~';
        $tmplcontent['keyword1'] = date('Y-m-d H:i'); //变动时间
        $tmplcontent['keyword2'] = $remark;  //变动类型
        $tmplcontent['keyword3'] = (string) round($money,2);  //变动金额
        $tmplcontent['keyword4'] = (string) round($after,2);  //当前余额
        $rs = \app\common\Wechat::sendtmpl($aid,$mid,'tmpl_moneychange',$tmplcontent,m_url('pages/my/usercenter', $aid));
        return ['status'=>1,'msg'=>''];
    }
    
      //加贡献值后台
    public static function addgongxianzhi($aid,$mid,$money,$remark,$orderid=0){
        if($money==0) return ;
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
        if(!$member) return ['status'=>0,'msg'=>t('会员').'不存在'];
        if(false){}else {
            // 使用高精度计算，支持5位小数
            $after = bcadd($member['contribution_num'], $money, 5);
            Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['contribution_num'=>$after]);
        }

        $data = [];
        $data['aid'] = $aid;
        $data['mid'] = $mid;
        $data['money'] = $money;
        $data['after'] = $after;
        $data['createtime'] = time();
        $data['remark'] = $remark;
        Db::name('member_gongxianzhiscorelog')->insert($data);
        // self::uplv($aid,$mid,$orderid);
        Wechat::updatemembercard($aid,$mid);

        $tmplcontent = [];
        $tmplcontent['first'] = '您的'.t('贡献值').'发生变动，变动金额：'.$money;
        $tmplcontent['remark'] = '点击进入查看~';
        $tmplcontent['keyword1'] = date('Y-m-d H:i'); //变动时间
        $tmplcontent['keyword2'] = $remark;  //变动类型
        $tmplcontent['keyword3'] = (string) round($money,2);  //变动金额
        $tmplcontent['keyword4'] = (string) round($after,2);  //当前余额
        $rs = \app\common\Wechat::sendtmpl($aid,$mid,'tmpl_moneychange',$tmplcontent,m_url('pages/my/usercenter', $aid));
        return ['status'=>1,'msg'=>''];
    }

    /**
     * @return 加贡献值
     */
    public static function getAddContribution($aid,$mid,$money,$remark){
        if($money==0) return ;
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
        //获取相应的等级 每消费多少元  赠送多少贡献值
        $member_level=Db::name('member_level')->where('aid',$aid)->where('id',$member['levelid'])->lock(true)->find();
        //需要计算的累计消费金额=(总消费-处理过的消费)
        //获取用户总消费
        $lj_nums=Db::name('payorder')->where('aid',$aid)->where('mid',$mid)->field('sum(money) as money')->find();
        //获取已经处理过的消费
        $contribution_dis=Db::name('member_contribution_logs')->where('aid',$aid)->where('mid',$mid)->order('lj_month desc')->find();
        //已处理过的
        $lj_monty=0;
        if($contribution_dis){
            $lj_monty=$contribution_dis['lj_month'];
        }
        $js_money=($lj_nums['money']-$lj_monty);
        $nums=0.00;
        if($member_level['gxz_xf_nums']>0 && $member_level['gxz_xf_zs']>0 && $js_money>=$member_level['gxz_xf_nums']){
            //计算需要添加的贡献值
            $nums=round(round($js_money/$member_level['gxz_xf_nums'],2)*$member_level['gxz_xf_zs'],2);
        }
        //修改用户的贡献值
        Db::name('member')->where('aid',$aid)->where('id',$mid)->inc('contribution_num', $nums)->update();

        //给用户添加贡献值记录
        $data = [];
        $data['aid'] = $aid;
        $data['mid'] = $mid;
        $data['add_nums'] = $nums;
        $data['xin_nums'] = $nums+$member['contribution_num'];
        if($nums>0){
            $data['lj_month'] = $lj_nums['money'];
        }else{
            $data['lj_month'] = $lj_monty;
        }
        $data['bc_month'] = $js_money;
        $data['createtime'] = date('Y-m-d H:i:s');
        $data['remark'] = $remark;
        Db::name('member_contribution_logs')->insert($data);

        //给上级添加贡献值  直推用户每累计消费_______元  送________贡献值
        //判断当前用户累计消费了多少
        //判断是否有上级
        if($member['pid']){
            //获取用户的累计消费值
            //判断总消费金额是否已经到了
            $f_nums=0.00;
            if($member_level['gxz_ljxf_nums']>0 && $member_level['gxz_ljxf_zs']>0 && $js_money>=$member_level['gxz_xf_nums']){
                //计算需要添加的贡献值
                $f_nums=round(round($js_money/$member_level['gxz_ljxf_nums'],2)*$member_level['gxz_ljxf_zs'],2);
            }
            //修改用户的贡献值
            Db::name('member')->where('aid',$aid)->where('id',$member['pid'])->inc('contribution_num', $f_nums)->update();

            //给用户添加贡献值记录
            $f_data = [];
            $f_data['aid'] = $aid;
            $f_data['mid'] = $member['pid'];
            $f_data['add_nums'] = $f_nums;
            $f_data['xin_nums'] = $f_nums;
            $f_data['createtime'] = date('Y-m-d H:i:s');
            $f_data['remark'] = '直推用户奖励贡献值';
            Db::name('member_contribution_logs')->insert($f_data);

        }
    }

    /**
     * @return 消费添加金币积分
     *
     */
    public static function getAddjinbi($aid,$mid,$money,$ordernum,$remark){
         if($money==0) return ;
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
        //获取相应的等级 每消费多少元  赠送多少贡献值
        $member_level=Db::name('member_level')->where('aid',$aid)->where('id',$member['levelid'])->lock(true)->find();
        //需要计算的累计消费金额=(总消费-处理过的消费) - 废弃 
        //获取用户总消费
        // $lj_nums=Db::name('payorder')->where('aid',$aid)->where('mid',$mid)->field('sum(money) as money')->find();
        // //获取已经处理过的消费
        // $contribution_dis=Db::name('member_scoreloghuang')->where('aid',$aid)->where('mid',$mid)->order('lj_month desc')->find();
        // //已处理过的
        // $lj_monty=0;
        // if($contribution_dis){
        //     $lj_monty=$contribution_dis['lj_month'];
        // }
        // $js_money=($lj_nums['money']-$lj_monty);
        // $nums=0.00;
        // if($member_level['hjf_xf_nums']>0 && $member_level['hjf_xf_zs']>0 && $js_money>=$member_level['hjf_xf_nums']){
        //     //计算需要添加的红包
        //     $nums=round(round($js_money/$member_level['hjf_xf_nums'],2)*$member_level['hjf_xf_zs'],2);
        // }
          //计算需要添加的红包
          //获取订单的商品 
          $money=Db::name('shop_order_goods')->where('aid',$aid)->where('ordernum',$ordernum)->where('is_yeji',1)->sum('real_totalprice'); 
                 
          $nums=0.00;
          if($member_level['jinbi_xf_nums']>0 && $member_level['jinbi_xf_zs']>0 && $money>=$member_level['jinbi_xf_nums']){
            //计算需要添加的红包
            //$b_nums=round($money/$member_level['hjf_xf_nums'],2);
            $b_nums=round($money/$member_level['jinbi_xf_nums'],6);
            $b_nums=sprintf("%.4f",substr(sprintf("%.5f",$b_nums),0,-1));
            if($b_nums>=1){
                 $nums=round($b_nums*$member_level['jinbi_xf_zs']);
            }
        }
        
        // var_dump($nums);

        //修改用户的积分
        Db::name('member')->where('aid',$aid)->where('id',$mid)->inc('score', $nums)->update();

        //给用户添加贡献值记录
        $data = [];
        $data['aid'] = $aid;
        $data['mid'] = $mid;
        $data['score'] = $nums;
        $data['after'] = $nums+$member['guquan'];
        if($nums>0){
            $data['lj_month'] = $lj_nums['money'];
        }else{
            $data['lj_month'] = $lj_monty;
        }
        $data['bc_month'] = $js_money;
        $data['createtime'] = time();
        $data['type'] = 1;
        $data['remark'] = $remark;
        Db::name('member_scorelogjinbi')->insert($data);
    }


    /**
     * @return 消费添加股权
     *
     */
    public static function getAddguqian($aid,$mid,$money,$ordernum,$remark){
        if($money==0) return ;
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
        //获取相应的等级 每消费多少元  赠送多少贡献值
        $member_level=Db::name('member_level')->where('aid',$aid)->where('id',$member['levelid'])->lock(true)->find();
        //需要计算的累计消费金额=(总消费-处理过的消费)
        //获取用户总消费
        // $lj_nums=Db::name('payorder')->where('aid',$aid)->where('mid',$mid)->field('sum(money) as money')->find();
        // //获取已经处理过的消费
        // $contribution_dis=Db::name('member_scorelogguquan')->where('aid',$aid)->where('mid',$mid)->order('lj_month desc')->find();
        // //已处理过的
        // $lj_monty=0;
        // if($contribution_dis){
        //     $lj_monty=$contribution_dis['lj_month'];
        // }
        
        // $js_money=($lj_nums['money']-$lj_monty);
        //获取订单的商品 
        $money=Db::name('shop_order_goods')->where('aid',$aid)->where('ordernum',$ordernum)->where('is_yeji',1)->sum('real_totalprice'); 
        $nums=0.00;
        if($member_level['gq_xf_nums']>0 && $member_level['gq_xf_zs']>0 && $money>=$member_level['gq_xf_nums']){
            //计算需要添加的红包
            $b_nums=round($money/$member_level['gq_xf_nums'],6);
            $b_nums=sprintf("%.4f",substr(sprintf("%.5f",$b_nums),0,-1));
            if($b_nums>=1){
                 $nums=round($b_nums*$member_level['gq_xf_zs']);
            }
        }
        //修改用户的红包
        Db::name('member')->where('aid',$aid)->where('id',$mid)->inc('guquan', $nums)->update();

        //给用户添加贡献值记录
        $data = [];
        $data['aid'] = $aid;
        $data['mid'] = $mid;
        $data['score'] = $nums;
        $data['after'] = $nums+$member['guquan'];
        if($nums>0){
            $data['lj_month'] = $lj_nums['money'];
        }else{
            $data['lj_month'] = $lj_monty;
        }
        $data['bc_month'] = $js_money;
        $data['createtime'] = time();
        $data['type'] = 1;
        $data['remark'] = $remark;
        Db::name('member_scorelogguquan')->insert($data);
    }




    /**
     * @return 消费添加红包
     *
     */
    public static function getAddhuangjifen($aid,$mid,$money,$ordernum,$remark){
        if($money==0) return ;
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
        //获取相应的等级 每消费多少元  赠送多少贡献值
        $member_level=Db::name('member_level')->where('aid',$aid)->where('id',$member['levelid'])->lock(true)->find();
        //需要计算的累计消费金额=(总消费-处理过的消费) - 废弃 
        //获取用户总消费
        // $lj_nums=Db::name('payorder')->where('aid',$aid)->where('mid',$mid)->field('sum(money) as money')->find();
        // //获取已经处理过的消费
        // $contribution_dis=Db::name('member_scoreloghuang')->where('aid',$aid)->where('mid',$mid)->order('lj_month desc')->find();
        // //已处理过的
        // $lj_monty=0;
        // if($contribution_dis){
        //     $lj_monty=$contribution_dis['lj_month'];
        // }
        // $js_money=($lj_nums['money']-$lj_monty);
        // $nums=0.00;
        // if($member_level['hjf_xf_nums']>0 && $member_level['hjf_xf_zs']>0 && $js_money>=$member_level['hjf_xf_nums']){
        //     //计算需要添加的红包
        //     $nums=round(round($js_money/$member_level['hjf_xf_nums'],2)*$member_level['hjf_xf_zs'],2);
        // }
          //计算需要添加的红包
          //获取订单的商品 
          $money=Db::name('shop_order_goods')->where('aid',$aid)->where('ordernum',$ordernum)->where('is_yeji',1)->sum('real_totalprice'); 
                 
          $nums=0.00;
          if($member_level['hjf_xf_nums']>0 && $member_level['hjf_xf_zs']>0 && $money>=$member_level['hjf_xf_nums']){
            //计算需要添加的红包
            //$b_nums=round($money/$member_level['hjf_xf_nums'],2);
            $b_nums=round($money/$member_level['hjf_xf_nums'],6);
            $b_nums=sprintf("%.4f",substr(sprintf("%.5f",$b_nums),0,-1));
            if($b_nums>=1){
                 $nums=round($b_nums*$member_level['hjf_xf_zs']);
            }
        }
        // var_dump($nums);
        
        //修改用户的红包
        Db::name('member')->where('aid',$aid)->where('id',$mid)->inc('scorehuang', $nums)->update();

        //给用户添加贡献值记录
        $data = [];
        $data['aid'] = $aid;
        $data['mid'] = $mid;
        $data['score'] = $nums;
        $data['after'] = $nums+$member['scorehuang'];
        if($nums>0){
            $data['lj_month'] = $lj_nums['money'];
        }else{
            $data['lj_month'] = $lj_monty;
        }
        $data['bc_month'] = $js_money;
        $data['createtime'] = time();
        $data['type'] = 1;
        $data['remark'] = $remark;
        Db::name('member_scoreloghuang')->insert($data);
    }


    /**
     * @param $aid
     * @return 更新红包  - 不做判断 直接更新
     */
    public static function getUpdateshuangjifen($aid,$mid,$money,$remark){
        if($money==0) return ;
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
        if(!$member) return ['status'=>0,'msg'=>t('会员').'不存在'];

        $scorehuang_num = $member['scorehuang'] + $money;
        Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['scorehuang'=>$scorehuang_num]);

        //给用户添加贡献值记录
        $data = [];
        $data['aid'] = $aid;
        $data['mid'] = $mid;
        $data['score'] = $money;
        $data['after'] = $scorehuang_num;
        $data['createtime'] = time();
        $data['remark'] = $remark;
        Db::name('member_scoreloghuang')->insert($data);

    }



    /**
     * @param $aid
     * @return 更新贡献值  - 不做判断 直接更新
     */
    public static function getUpdateContribution($aid,$mid,$money,$remark){
        if($money==0) return ;
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
        if(!$member) return ['status'=>0,'msg'=>t('会员').'不存在'];

        $contribution_num = $member['contribution_num'] + $money;
        Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['contribution_num'=>$contribution_num]);

        //给用户添加贡献值记录
        $data = [];
        $data['aid'] = $aid;
        $data['mid'] = $mid;
        $data['add_nums'] = $money;
        $data['xin_nums'] = $contribution_num;
        $data['createtime'] = date('Y-m-d H:i:s');
        $data['remark'] = $remark;
        Db::name('member_contribution_logs')->insert($data);

    }
    
    //分享送贡献值
    public static function  getAddContributionFx($aid,$mid){
        //分享______（几人）注册成为_______(等级id）送____贡献值
        //获取用户的下级是等级几的用户
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
        //获取上级的详情
        $f_member=Db::name('member')->where('aid',$aid)->where('id',$member['pid'])->lock(true)->find();
        $member_level=Db::name('member_level')->where('aid',$aid)->where('id',$f_member['levelid'])->lock(true)->find();
        if($member_level['gxz_fx_dj']){
            $gxz_fx_dj=explode(',',$member_level['gxz_fx_dj']);
            $member_nums = Db::name('member')->where('aid',$aid)->where('pid',$f_member['id'])->whereIn('levelid',$gxz_fx_dj)->count();
            $nums=0.00;
            //获取已经处理的分享用户数量
            $contribution_dis=Db::name('member_contribution_logs')->where('aid',$aid)->where('mid',$mid)->order('lj_rs_nums desc')->find();
            $lj_nums=0;
            if($contribution_dis){
                $lj_nums=$contribution_dis['lj_rs_nums'];
            }
            $js_nums=($member_nums-$lj_nums);
            if($js_nums>=$member_level['gxz_fx_nums']){
                //赠送贡献值
                $nums=$member_level['gxz_fx_zs'];
            }
            //修改用户的贡献值
            Db::name('member')->where('aid',$aid)->where('id',$f_member['id'])->inc('contribution_num', $nums)->update();
            //给用户添加贡献值记录
            $data = [];
            $data['aid'] = $aid;
            $data['mid'] = $f_member['id'];
            $data['add_nums'] = $nums;
            $data['xin_nums'] = $nums;
            if($nums>0){
                $data['lj_rs_nums'] = $member_nums;
            }else{
                $data['lj_rs_nums'] = $lj_nums;
            }
            $data['bc_rs_nums'] = $js_nums;
            $data['createtime'] = date('Y-m-d H:i:s');
            $data['remark'] = '分享注册 赠送贡献值';
            Db::name('member_contribution_logs')->insert($data);
        }
    }
    //加自己分红
    public static function addzijifh($aid,$mid,$money,$remark)
    {
            if($money==0) return ;
            $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
            if(!$member) return ['status'=>0,'msg'=>t('会员').'不存在'];
    
            if(false){}else {
                $after = $member['zijifh'] + $money;
                Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['zijifh'=>$after]);
            }
    
            $data = [];
            $data['aid'] = $aid;
            $data['mid'] = $mid;
            $data['money'] = $money;
            $data['after'] = $after;
            $data['createtime'] = time();
            $data['remark'] = $remark;
            Db::name('member_zijifhlog')->insert($data);
            // self::uplv($aid,$mid,$orderid);
            Wechat::updatemembercard($aid,$mid);
    
            $tmplcontent = [];
            $tmplcontent['first'] = '您的'.t('自己分红池').'发生变动，变动金额：'.$money;
            $tmplcontent['remark'] = '点击进入查看~';
            $tmplcontent['keyword1'] = date('Y-m-d H:i'); //变动时间
            $tmplcontent['keyword2'] = $remark;  //变动类型
            $tmplcontent['keyword3'] = (string) round($money,2);  //变动金额
            $tmplcontent['keyword4'] = (string) round($after,2);  //当前余额
            $rs = \app\common\Wechat::sendtmpl($aid,$mid,'tmpl_moneychange',$tmplcontent,m_url('pages/my/usercenter', $aid));
            return ['status'=>1,'msg'=>''];
        
    }
    //加分享分红池
    public static function addfxfhc($aid,$mid,$money,$remark)
    {
            if($money==0) return ;
            $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
            if(!$member) return ['status'=>0,'msg'=>t('会员').'不存在'];
    
            if(false){}else {
                $after = $member['fxfhc'] + $money;
                Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['fxfhc'=>$after]);
            }
    
            $data = [];
            $data['aid'] = $aid;
            $data['mid'] = $mid;
            $data['money'] = $money;
            $data['after'] = $after;
            $data['createtime'] = time();
            $data['remark'] = $remark;
            Db::name('member_fxfhclog')->insert($data);
            // self::uplv($aid,$mid,$orderid);
            Wechat::updatemembercard($aid,$mid);
    
            $tmplcontent = [];
            $tmplcontent['first'] = '您的'.t('分享分红池').'发生变动，变动金额：'.$money;
            $tmplcontent['remark'] = '点击进入查看~';
            $tmplcontent['keyword1'] = date('Y-m-d H:i'); //变动时间
            $tmplcontent['keyword2'] = $remark;  //变动类型
            $tmplcontent['keyword3'] = (string) round($money,2);  //变动金额
            $tmplcontent['keyword4'] = (string) round($after,2);  //当前余额
            $rs = \app\common\Wechat::sendtmpl($aid,$mid,'tmpl_moneychange',$tmplcontent,m_url('pages/my/usercenter', $aid));
            return ['status'=>1,'msg'=>''];
    }
    //收益池
    public static function addsyc($aid,$mid,$money,$remark,$q=0)
    {
        if($money==0) return ;
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
        if(!$member) return ['status'=>0,'msg'=>t('会员').'不存在'];

        if(false){}else {
            $after = $member['syc'] + $money;
            Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['syc'=>$after]);
        }

        $data = [];
        $data['aid'] = $aid;
        $data['mid'] = $mid;
        $data['money'] = $money;
        $data['after'] = $after;
        $data['createtime'] = time();
        $data['remark'] = $remark;
        $data['q']=$q;
        Db::name('member_syclog')->insert($data);
        // self::uplv($aid,$mid,$orderid);
        Wechat::updatemembercard($aid,$mid,$remark);

        $tmplcontent = [];
        $tmplcontent['first'] = '您的'.t('收益池').'发生变动，变动金额：'.$money;
        $tmplcontent['remark'] = '点击进入查看~';
        $tmplcontent['keyword1'] = date('Y-m-d H:i'); //变动时间
        $tmplcontent['keyword2'] = $remark;  //变动类型
        $tmplcontent['keyword3'] = (string) round($money,2);  //变动金额
        $tmplcontent['keyword4'] = (string) round($after,2);  //当前余额
        $rs = \app\common\Wechat::sendtmpl($aid,$mid,'tmpl_moneychange',$tmplcontent,m_url('pages/my/usercenter', $aid));
        return ['status'=>1,'msg'=>''];
    }
    //加排队返现积分
    public static function addscore3($aid,$mid,$bid,$score,$remark){
        if($score==0) return ;
        $score = $score;
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
        if(!$member) return ['status'=>0,'msg'=>t('会员').'不存在'];
        if($score < 0 && $member['score3'] < $score*-1) return ['status'=>0,'msg'=>t('积分').'不足'];
        if(false){}else {
            // 使用高精度计算，支持5位小数
            $after = bcadd($member['score3'], $score, 5);
            Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['score3'=>$after]);
        }

        $data = [];
        $data['aid'] = $aid;
        $data['mid'] = $mid;
        $data['bid'] = $bid;
        $data['score'] = $score;
        $data['after'] = $after;
        $data['createtime'] = time();
        $data['remark'] = $remark;
        // $data['type'] = 1;
        // $data['channel'] = $channel;
        Db::name('member_score3log')->insert($data);
        return ['status'=>1,'msg'=>''];
    }


    //加积分
    //@update 22-7-21 增加渠道
    public static function addscore($aid,$mid,$score,$remark,$channel=''){
        if($score==0) return ;
        $score = $score;
      
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
        
        if(!$member) return ['status'=>0,'msg'=>t('会员').'不存在'];
        $member['score'] = self::getscore($member);
        if($score < 0 && $member['score'] < $score*-1) return ['status'=>0,'msg'=>t('积分').'不足'];

        if(false){}else {
            $after = $member['score'] + $score;
            Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['score'=>$after]);
        }

        $data = [];
        $data['aid'] = $aid;
        $data['mid'] = $mid;
        $data['score'] = $score;
        $data['after'] = $after;
        $data['createtime'] = time();
        $data['remark'] = $remark;
        $data['type'] = 1;
        $data['channel'] = $channel;
         
        Db::name('member_scorelog')->insert($data);
        Wechat::updatemembercard($aid,$mid,$remark);
        return ['status'=>1,'msg'=>''];
    }
    //加信用分
    //加绿积分
    public static function adjustCredit($aid,$mid,$score,$remark,$channel='')
    {
        if($score==0) return ;
        $score = $score;
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
        if(!$member) return ['status'=>0,'msg'=>t('会员').'不存在'];
        
        if($score < 0 && $member['credit_score'] < $score*-1) return ['status'=>0,'msg'=>t('积分').'不足'];

        if(false){}else {
            $after = $member['credit_score'] + $score;
            Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['credit_score'=>$after]);
        }

        $data = [];
        $data['aid'] = $aid;
        $data['mid'] = $mid;
        $data['score'] = $score;
        $data['after'] = $after;
        $data['create_time'] = date('Y-m-d H:i:s', time()); // 修改这里，将时间戳转换为datetime格式
        $data['remark'] = $remark;
        $data['type'] = 1;
        
        Db::name('zhaopin_credit_log')->insert($data);
        Wechat::updatemembercard($aid,$mid,$remark);
        return ['status'=>1,'msg'=>''];
    }
    //加绿积分
    public static function addscorelv($aid,$mid,$score,$remark,$channel='')
    {
        if($score==0) return ;
        $score = $score;
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
        if(!$member) return ['status'=>0,'msg'=>t('会员').'不存在'];
        $member['scorelv'] = self::getscorelv($member);
        if($score < 0 && $member['scorelv'] < $score*-1) return ['status'=>0,'msg'=>t('积分').'不足'];

        if(false){}else {
            $after = $member['scorelv'] + $score;
            Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['scorelv'=>$after]);
        }

        $data = [];
        $data['aid'] = $aid;
        $data['mid'] = $mid;
        $data['score'] = $score;
        $data['after'] = $after;
        $data['createtime'] = time();
        $data['remark'] = $remark;
        $data['type'] = 1;
        $data['channel'] = $channel;
        Db::name('member_scoreloglv')->insert($data);
        Wechat::updatemembercard($aid,$mid,$remark);
        return ['status'=>1,'msg'=>''];
    }

            // 加减配资金额
            public static function updateAllocation($aid, $mid, $allocationAmount, $remark, $frommid = 0, $actionType = '增加') {
                if ($allocationAmount == 0) return;
            
                // 锁定用户信息
                $member = Db::name('member')->where('aid', $aid)->where('id', $mid)->lock(true)->find();
                if (!$member) return ['status' => 0, 'msg' => t('会员') . '不存在'];
            
                // 计算新的配资金额
                $newAllocation = $member['jishoupeizi'] + $allocationAmount;
            
                // 检查配资金额不能为负（如果是扣减操作）
                if ($newAllocation < 0) {
                    return ['status' => 0, 'msg' => '配资金额不足'];
                }
            
                // 更新用户配资金额
                Db::name('member')->where('aid', $aid)->where('id', $mid)->update(['jishoupeizi' => $newAllocation]);
            
                // 记录配资日志
                $data = [
                    'aid' => $aid,
                    'mid' => $mid,
                    'money' => $allocationAmount,
                    'after' => $newAllocation,
                    'createtime' => time(),
                    'remark' => $remark,
                ];
                Db::name('user_allocation_log')->insert($data);
            
                // 提示用户更新信息，微信通知
                $tmplcontent = [];
                $tmplcontent['first'] = '您的配资金额发生变动，变动金额：' . $allocationAmount;
                $tmplcontent['remark'] = '点击进入查看详情~';
                $tmplcontent['keyword1'] = date('Y-m-d H:i'); // 变动时间
                $tmplcontent['keyword2'] = $remark;           // 变动类型
                $tmplcontent['keyword3'] = (string) round($allocationAmount, 2);  // 变动金额
                $tmplcontent['keyword4'] = (string) round($newAllocation, 2);     // 当前配资余额
            
                $tmplcontentNew = [];
                $tmplcontentNew['thing2'] = str_replace(',', '', mb_substr($remark, 0, 5)); // 消费项目
                $tmplcontentNew['amount3'] = round($allocationAmount, 2);   // 消费金额
                $tmplcontentNew['amount4'] = round($newAllocation, 2);      // 卡内配资余额
                $tmplcontentNew['time6'] = date('Y-m-d H:i');               // 变动时间
            
                \app\common\Wechat::sendtmpl($aid, $mid, 'tmpl_allocation_change', $tmplcontent, m_url('pages/my/usercenter', $aid), $tmplcontentNew);
            
                // 如果启用了短信提醒（配资减少时）
                if (getcustom('sms_temp_allocation_use') && $member['tel'] && $allocationAmount < 0) {
                    \app\common\Sms::send($aid, $member['tel'], 'tmpl_allocation_use', [
                        'amount' => $allocationAmount,
                        'remaining' => $newAllocation
                    ]);
                }
            
                return ['status' => 1, 'msg' => '操作成功'];
            }



    
    public static function getscorelv($member)
    {
        if(!$member || !$member['id']) return '0';
        $member = db('member')->where(['id'=>$member['id']])->find();

        return $member['scorelv'];
    }
    
    //加消费值
    public static function addscorexiaofeizhi($aid,$mid,$score,$remark,$channel='')
    {
        if($score==0) return ;
        $score = $score;
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
        if(!$member) return ['status'=>0,'msg'=>t('会员').'不存在'];
        $member['scorexiaofeizhi'] = self::getscorexiaofeizhi($member);
        if($score < 0 && $member['scorexiaofeizhi'] < $score*-1) return ['status'=>0,'msg'=>t('积分').'不足'];

        if(false){}else {
            $after = $member['scorexiaofeizhi'] + $score;
            Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['scorexiaofeizhi'=>$after]);
        }

        $data = [];
        $data['aid'] = $aid;
        $data['mid'] = $mid;
        $data['score'] = $score;
        $data['after'] = $after;
        $data['createtime'] = time();
        $data['remark'] = $remark;
        $data['type'] = 1;
        $data['channel'] = $channel;
        Db::name('member_scorelogxiaofeizhi')->insert($data);
        Wechat::updatemembercard($aid,$mid,$remark);
        return ['status'=>1,'msg'=>''];
    }
    
    public static function getscorexiaofeizhi($member)
    {
        if(!$member || !$member['id']) return '0';
        $member = db('member')->where(['id'=>$member['id']])->find();

        return $member['scorexiaofeizhi'];
    }
     //计算创业值
     public static function getscorebus($member)
    {
        if(!$member) return '0';
        $member = db('member')->where(['id'=>$member['id']])->find();
        return $member['bus_total'];
    }
    
    
    //计算显示现金券
        public static function getscoheijifen($member)
    {
        if(!$member) return '0';
        $member = db('member')->where(['id'=>$member['id']])->find();
        return $member['heiscore'];
    }
       //计算显示总损失金额
        public static function getsunshijine($member)
    {
        if(!$member) return '0';
        // $member = db('member')->where(['id'=>$member['id']])->find();
        // $member = Db::name('member_xingjilog')->where('mid',mid)->sum('money');
        $rdata['total_sunshi'] = Db::name('member_xingjilog')->where('mid',mid)->where('aid',1)->sum('money');
        //  $member = db('member')->where(['id'=>$member['id']])->find();
        return $rdata['total_sunshi'] ;
    }
     
    //计算显示贡献值
        public static function getgongxianzhi($member)
    {
        if(!$member) return '0';
        $member = db('member')->where(['id'=>$member['id']])->find();
        return $member['contribution_num'];
    }
    
        public static function getshouyichi($member)
    {
        if(!$member) return '0';
        $member = db('member')->where(['id'=>$member['id']])->find();
        return $member['syc'];
    }
    
        public static function getpeizi($member)
    {
        if(!$member) return '0';
        $member = db('member')->where(['id'=>$member['id']])->find();
        return $member['jishoupeizi'];
    }
    
        //计算显示贡献值
        public static function getguquan($member)
    {
        if(!$member) return '0';
        $member = db('member')->where(['id'=>$member['id']])->find();
        return $member['guquan'];
    }
    
    
    
    //家黄积
    public static function addscorehuang($aid,$mid,$score,$remark,$channel='')
    {
        if($score==0) return ;
        $score = $score;
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
        if(!$member) return ['status'=>0,'msg'=>t('会员').'不存在'];
        $member['scorehuang'] = self::getscorehuang($member);
        if($score < 0 && $member['scorehuang'] < $score*-1) return ['status'=>0,'msg'=>t('积分').'不足'];

        if(false){}else {
            // 使用高精度计算，支持5位小数
            $after = bcadd($member['scorehuang'], $score, 5);
            Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['scorehuang'=>$after]);
        }

        $data = [];
        $data['aid'] = $aid;
        $data['mid'] = $mid;
        $data['score'] = $score;
        $data['after'] = $after;
        $data['createtime'] = time();
        $data['remark'] = $remark;
        $data['type'] = 1;
        $data['channel'] = $channel;
        Db::name('member_scoreloghuang')->insert($data);
        Wechat::updatemembercard($aid,$mid,$remark);
        return ['status'=>1,'msg'=>''];
    }
    
    public static function getscorehuang($member)
    {
        if(!$member || !$member['id']) return '0';
        $member = db('member')->where(['id'=>$member['id']])->find();

        return $member['scorehuang'];
    }
    public static function getcreditscore($member)
    {
        if(!$member || !$member['id']) return '0';
        $member = db('member')->where(['id'=>$member['id']])->find();

        return $member['credit_score'];
    }
    
    public static function getjobcount($member)
    {
        if(!$member || !$member['id']) return '0';
        
        // 统计用户已入职的工作数量
        $jobcount = Db::name('zhaopin_apply')
            ->where([
                'mid' => $member['id'],
                'status' => 6  // 6表示已入职状态
            ])
            ->count();

        return $jobcount;
    }
    public static function getteamcount($member)
    {
        if(!$member || !$member['id']) return '0';
        
        // 初始化团队人数
        $teamnum = 0;
        
        // 获取所有等级>=2的等级ID
        $levelids = Db::name('member_level')
            ->where('aid', $member['aid'])
            ->where('id', '>=', 2)
            ->column('id');
        
        // 从当前会员开始向下遍历
        $topids = [$member['id']];
        while(true) {
            // 获取下一级成员
            $childs = Db::name('member')
                ->whereIn('pid', $topids)
                ->select()
                ->toArray();
            
            if(empty($childs)) {
                break;
            } else {
                $topids = [];
                foreach($childs as $item) {
                    $topids[] = $item['id'];
                    // 如果成员等级>=2,计入团队人数
                    if($item['levelid'] >= 2) {
                        $teamnum += 1;
                    }
                }
            }
        }
        
        return $teamnum;
    }
    
    public static function addscore2($aid,$mid,$score,$remark,$channel=''){
        if($score==0) return ;
        $score = intval($score);
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
        if(!$member) return ['status'=>0,'msg'=>t('会员').'不存在'];
        $member['score'] = self::getscore($member);
        if($score < 0 && $member['score2'] < $score*-1) return ['status'=>0,'msg'=>t('积分').'不足'];

        if(false){}else {
            $after = $member['score2'] + $score;
            Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['score2'=>$after]);
        }

        $data = [];
        $data['aid'] = $aid;
        $data['mid'] = $mid;
        $data['score'] = $score;
        $data['after'] = $after;
        $data['createtime'] = time();
        $data['remark'] = $remark;
        $data['type'] = 1;
        $data['channel'] = $channel;
        Db::name('member_scorelog')->insert($data);
        Wechat::updatemembercard($aid,$mid,$remark);
        return ['status'=>1,'msg'=>''];
    }
    //静态加积分
    public static function addscoreA($aid,$mid,$score,$remark,$channel=''){
        if($score==0) return ;
        $score = $score;
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
        if(!$member) return ['status'=>0,'msg'=>t('会员').'不存在'];
        $member['scoreA'] = self::getscoreA($member);
        if($score < 0 && $member['scoreA'] < $score*-1) return ['status'=>0,'msg'=>t('积分').'不足'];

        if(false){}else {
            $after = $member['scoreA'] + $score;
            Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['scoreA'=>$after]);
        }

        $data = [];
        $data['aid'] = $aid;
        $data['mid'] = $mid;
        $data['score'] = $score;
        $data['after'] = $after;
        $data['createtime'] = time();
        $data['remark'] = $remark;
        $data['type'] = 1;
        $data['channel'] = $channel;
        Db::name('member_scoreloga')->insert($data);
        return ['status'=>1,'msg'=>''];
    }
        //动态加积分
    public static function addscoreB($aid,$mid,$score,$remark,$channel=''){
        if($score==0) return ;
        $score = $score;
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
        if(!$member) return ['status'=>0,'msg'=>t('会员').'不存在'];
        $member['scoreB'] = self::getscoreB($member);
        if($score < 0 && $member['scoreB'] < $score*-1) return ['status'=>0,'msg'=>t('积分').'不足'];

        if(false){}else {
            $after = $member['scoreB'] + $score;
            Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['scoreB'=>$after]);
        }

        $data = [];
        $data['aid'] = $aid;
        $data['mid'] = $mid;
        $data['score'] = $score;
        $data['after'] = $after;
        $data['createtime'] = time();
        $data['remark'] = $remark;
        $data['type'] = 'B';
        $data['channel'] = $channel;
        Db::name('member_scorelogb')->insert($data);
        return ['status'=>1,'msg'=>''];
    }
    //加提现积分
    public static function addscore_withdraw($aid,$mid,$score,$remark){
        if($score==0) return ;
        $score = intval($score);
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
        if(!$member) return ['status'=>0,'msg'=>t('会员').'不存在'];
        if($score < 0 && $member['score_withdraw'] < $score*-1) return ['status'=>0,'msg'=>t('积分').'不足'];

        $after = $member['score_withdraw'] + $score;
        Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['score_withdraw'=>$after]);

        $data = [];
        $data['aid'] = $aid;
        $data['mid'] = $mid;
        $data['score'] = $score;
        $data['after'] = $after;
        $data['createtime'] = time();
        $data['remark'] = $remark;
        $data['type'] = 2;
        Db::name('member_scorelog')->insert($data);
        return ['status'=>1,'msg'=>''];
    }
    //佣金增加微信通知
    public static function addcommission_wxnotify($type_str,$amount,$aid,$frommid,$proname=''){
        //self::addcommission_wxnotify($type_str,$amount,$aid,$formmid,$proname='');
        //添加佣金发放通知给用户
        //订阅消息
        $nickname = Db::name('member')->where('aid',$aid)->where('id',$frommid)->value('nickname');

        $tmplcontent = [];
        $tmplcontent['thing1'] = $type_str;//'分佣类型';
        $tmplcontent['amount2'] = $amount;
        $tmplcontent['thing3'] = $nickname;//'下线昵称';
        $tmplcontent['thing4'] = $proname;//'购买商品';
        $tmplcontent['time5'] = date('Y-m-d H:i');
                    
        $tmplcontentnew = [];
        $tmplcontentnew['thing1'] = $type_str;//'分佣类型';
        $tmplcontentnew['amount2'] = $amount;
        $tmplcontentnew['thing3'] = $nickname;//'下线昵称';
        $tmplcontentnew['thing4'] = $proname;//'购买商品';
        $tmplcontentnew['time5'] = date('Y-m-d H:i');
                    
        \app\common\Wechat::sendwxtmpl($aid,$mid,'tmpl_tixiansuccess',$tmplcontentnew,'pages/my/usercenter',$tmplcontent);
        
    }
    //添加佣金增加日志;
    public static function addcommissionmy($aid,$mid,$frommid,$commission,$remark,$addtotal=1,$fhtype='')
    {
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
        if(!$member) return ['status'=>0,'msg'=>t('会员').'不存在'];
        $after = $member['commission'] + $commission;
         $update_member = ['totalcommission'=>$totalcommission,'commission'=>$after];
        Db::name('member')->where('aid',$aid)->where('id',$mid)->update($update_member);
       
        $data = [];
        $data['aid'] = $aid;
        $data['mid'] = $mid;
        $data['frommid'] = $frommid;
        $data['commission'] = $commission;
        $data['after'] = $after;
        $data['createtime'] = time();
        $data['remark'] = $remark;
        Db::name('member_commissionlog')->insert($data);

    }
    //加创业值
    public static function addbusTotal($aid,$mid,$frommid,$commission,$remark,$addtotal=1,$fhtype='')
    {
        //   var_dump($mid);
        //   var_dump($frommid);
        //   exit();
          if($commission==0) return ;

          $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
          if(!$member) return ['status'=>0,'msg'=>t('会员').'不存在'];
        //
          $m_bus_user = intval($member['bus_total']);
           Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['bus_total'=>$m_bus_user+intval($commission)]);
          
            $after = $m_bus_user+intval($commission);
            $data = [];
            $data['aid'] = $aid;
            $data['mid'] = $mid;
            $data['frommid'] = $frommid;
            $data['busTotal'] = $commission;
            $data['after'] = $after;
            $data['createtime'] = time();
            $data['remark'] = $remark;
            Db::name('member_bustotallog')->insert($data);


            
            return ['status'=>1,'msg'=>''];
    }

    //收益抽佣奖励：享受下级_______(等级id)收益_______%(设置后，下级得到多少佣金，就会给对应的用户返收益的百分比）
    public static function addParentcommissionLogs($aid,$mid,$orderid,$ogid,$commission,$remark){
        //收益抽佣奖励：享受下级_______(等级id)收益_______%(设置后，下级得到多少佣金，就会给对应的用户返收益的百分比）
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
        $f_member =  Db::name('member')->where('aid',$aid)->where('id',$member['pid'])->lock(true)->find();
        if($f_member['id']){
            //判断是否有上级
            //获取用户相应等级的 收益抽佣奖励
            $f_member_level=Db::name('member_level')->where('aid',$aid)->where('id',$f_member['levelid'])->lock(true)->find();
            //判断下级的等级是否在上级的收益抽佣奖励范围内
            $f_member_level_arr=explode(',',$f_member_level['sy_xj_dj']);
            if(in_array($member['levelid'],$f_member_level_arr)){
                //获取数据进行处理
                $w_commission=0;
                if($commission>0){
                     $w_commission=$commission*($f_member_level['sy_xj_nums']/100);
                }
                if($orderid == 0)
                {
                    self::addcommission($aid,$f_member['id'],$mid,$w_commission,$remark);
                }else{
                    //添加分佣记录-不进行分佣 这里只是记录
                    Db::name('member_commission_record')->insert(
                        [
                            'aid'=>$aid,
                            'mid'=>$f_member['id'],
                            'frommid'=>$mid,
                            'orderid'=>$orderid,
                            'ogid'=>$ogid,
                            'type'=>'shop',
                            'commission'=>$w_commission,
                            'score'=>0,
                            'remark'=>$remark,
                            'createtime'=>time()
                        ]);
                }
            }

        }
    }
    //加冻结佣金
   public static function adddongjienum($aid,$mid,$score,$remark,$channel=''){
        if($score==0) return ;
        $score = $score;
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
        if(!$member) return ['status'=>0,'msg'=>t('会员').'不存在'];
        if($score < 0 && $member['dongjienum'] < $score*-1) return ['status'=>0,'msg'=>t('冻结佣金').'不足'];

        if(false){}else {
            $after = $member['dongjienum'] + $score;
            Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['dongjienum'=>$after]);
        }

        $data = [];
        $data['aid'] = $aid;
        $data['mid'] = $mid;
        $data['score'] = $score;
        $data['after'] = $after;
        $data['createtime'] = time();
        $data['remark'] = $remark;
        Db::name('member_dongjienum')->insert($data);
        return ['status'=>1,'msg'=>''];
    }

    //加佣金
    public static function addcommission($aid,$mid,$frommid,$commission,$remark,$addtotal=1,$fhtype='',$qu=0){
        if($commission==0) return ;
        
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
        
        if(!$member) return ['status'=>0,'msg'=>t('会员').'不存在'];
        
        // 检查是否开启贡献值扣减功能 - 只有在增加佣金时才扣减贡献值
        if ($commission > 0) {
            $gongxianzhi_setting = Db::name('gongxianzhiset')->where('aid', $aid)->find();
            if ($gongxianzhi_setting && isset($gongxianzhi_setting['info'])) {
                $gongxianzhi_info = json_decode($gongxianzhi_setting['info'], true);
                
                // 如果开启了佣金扣减贡献值功能
                if (isset($gongxianzhi_info['deduct_contribution']) && $gongxianzhi_info['deduct_contribution'] == 1) {
                    // 获取扣减比例，默认100%全部扣减
                    $deduct_ratio = isset($gongxianzhi_info['deduct_ratio']) ? $gongxianzhi_info['deduct_ratio'] : 100;
                    $deduct_ratio = min(100, max(0, $deduct_ratio)); // 确保比例在0-100之间
                    
                    // 计算需要扣减的贡献值
                    $deduct_amount = round($commission * ($deduct_ratio / 100), 2);
                    
                    // 检查用户贡献值是否足够
                    if ($member['contribution_num'] >= $deduct_amount) {
                        // 扣减贡献值
                        $deduct_result = self::addgongxianzhi($aid, $mid, -$deduct_amount, '发放佣金扣减贡献值: '.$remark);
                    //    \think\facade\Log::write('用户ID:'.$mid.'发放佣金扣减贡献值，金额:'.$deduct_amount.'，原因:'.$remark, 'info');
                    } else {
                     //   \think\facade\Log::write('用户ID:'.$mid.'发放佣金需扣减贡献值，但贡献值不足，需扣:'.$deduct_amount.'，实际:'.$member['contribution_num'], 'warning');
                        
                        // 获取贡献值不足时的处理方式 0:拒绝发放 1:按比例发放 2:全额发放 3:按剩余贡献值发放
                        $insufficient_action = isset($gongxianzhi_info['insufficient_action']) ? $gongxianzhi_info['insufficient_action'] : 0;
                        
                        if ($insufficient_action == 0) {
                            // 拒绝发放佣金
                           // \think\facade\Log::write('用户ID:'.$mid.'贡献值不足，拒绝发放佣金，金额:'.$commission, 'info');
                            return ['status'=>0, 'msg'=>'贡献值不足，无法发放佣金'];
                        } elseif ($insufficient_action == 1) {
                            // 按比例发放佣金
                            $ratio = isset($gongxianzhi_info['insufficient_ratio']) ? $gongxianzhi_info['insufficient_ratio'] : 50;
                            $ratio = min(100, max(0, $ratio)); // 确保比例在0-100之间
                            
                            $old_commission = $commission;
                            $commission = round($commission * ($ratio / 100), 2);
                            
                            // 如果有贡献值，扣除全部
                            if ($member['contribution_num'] > 0) {
                                self::addgongxianzhi($aid, $mid, -$member['contribution_num'], '发放佣金扣减贡献值(不足): '.$remark);
                            }
                            
                          //  \think\facade\Log::write('用户ID:'.$mid.'贡献值不足，按比例发放佣金，原金额:'.$old_commission.'，调整后金额:'.$commission.'，比例:'.$ratio.'%', 'info');
                            
                            // 更新备注说明
                            $remark = $remark . '(贡献值不足，按'.$ratio.'%比例发放)';
                        } elseif ($insufficient_action == 3) {
                            // 按剩余贡献值发放佣金
                            $old_commission = $commission;
                            
                            // 计算按照贡献值能发放的佣金金额
                            $contribution_available = $member['contribution_num'];
                            
                            // 按照扣减比例计算需要的贡献值
                            $deduct_ratio = isset($gongxianzhi_info['deduct_ratio']) ? $gongxianzhi_info['deduct_ratio'] : 100;
                            $deduct_ratio = min(100, max(0, $deduct_ratio)); // 确保比例在0-100之间
                            
                            // 计算能发放的佣金比例
                            if ($deduct_amount > 0) {
                                $available_ratio = ($contribution_available / $deduct_amount) * 100;
                                $available_ratio = min(100, max(0, $available_ratio)); // 确保比例在0-100之间
                            } else {
                                $available_ratio = 0; // 如果需要扣减的贡献值为0，则设置比例为0
                            }
                            
                            // 计算能发放的佣金金额
                            $commission = round($commission * ($available_ratio / 100), 2);
                            
                            // 确保佣金不为负数
                            $commission = max(0, $commission);
                            
                            // 扣除相应的贡献值
                            if ($contribution_available > 0) {
                                // 直接扣除所有可用贡献值，而不是按比例计算
                                self::addgongxianzhi($aid, $mid, -$contribution_available, '发放佣金扣减贡献值(按剩余): '.$remark);
                            }
                            
                          //  \think\facade\Log::write('用户ID:'.$mid.'按剩余贡献值发放佣金，原金额:'.$old_commission.'，调整后金额:'.$commission.'，贡献值:'.$contribution_available, 'info');
                            
                            // 更新备注说明
                            $remark = $remark . '(贡献值不足，按剩余贡献值发放)';
                        } else {
                            // 全额发放，不做任何处理
                         //   \think\facade\Log::write('用户ID:'.$mid.'贡献值不足，但设置为全额发放佣金，金额:'.$commission, 'info');
                        }
                    }
                }
            }
        }
        
        $set = Db::name('admin_set')->where('aid',$aid)->find();
        $memberlevel = Db::name('member_level')->where('aid',$aid)->where('id',$member['levelid'])->find();
        if($memberlevel['syc'] == 1)
        {
            if($member['syc'] >=  $commission )
            {
                $gwjf = 0;
                $yj = $commission;
                \app\common\Member::addsyc($aid,$mid,'-'.$yj,'佣金到账扣除'.$yj);
            }else{
                 $syc_shouyi = json_decode($memberlevel['syc_shouyi'],1);
                 $gwjf = $commission *$syc_shouyi['gw']*0.01;
                 $yj = $commission*$syc_shouyi['yj']*0.01;
                 $commission = $yj;
              //   \app\common\Member::heiscore($aid,$mid,'-'.$yj,'佣金到账扣除'.$yj);
                 self::addhei($aid, $mid, $gwjf, '佣金到账,收益池不足,到账金额'.$gwjf);
                 
                 //分佣到账通知
                $type_str = (strpos($remark,'团队') !== false)?'团队分红':'佣金到账';
                self::addcommission_wxnotify($type_str,$yj,$aid,$frommid,$remark);
                 
                 
            }
        }
//      
        //检测佣金金额是否能够抵扣创业值;
         //根据佣金更改创业值;(创业值-佣金值)
          //$m_bus_user = intval($member['bus_total']);
          ////但是后台不在意
          //if(strpos($remark,'后台')!=-1)
          //{
          //    if($m_bus_user-$commission >0)
    //        {
    //              Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['bus_total'=>$m_bus_user-$commission]);
    //        }else{
    //            return ['status'=>0,'msg'=>t('会员').'创业值不足,请充值创业值']; 
    //        }
          //}
              
        //根据佣金增加点数;
        if($commission > 0 && $set['commission2scorepercent'] > 0){
            $oldcommission = $commission;
            $commission = round($commission * (1-$set['commission2scorepercent']*0.01),2);
            $score = $oldcommission - $commission;
            self::addscore($aid,$mid,$score,$remark);
        }

        if($commission > 0 && $addtotal==1){
            $totalcommission = $member['totalcommission'] + $commission;
        }else{
            $totalcommission = $member['totalcommission'];
        }
        $liandongrs = Db::name('liandong_set')->where('aid',$aid)->find();
        if($commission > 0 &&$liandongrs['status'] == 1 && $liandongrs['fcj_bili'] != 0 && strpos($remark,'后台修改') === false && strpos($remark,'解冻') === false&& strpos($remark,'佣金提现') === false && strpos($remark,'后台解冻') === false && $member['dong_status'] !=1 && strpos($remark,'升级解冻') === false)
        {
            $fcj_levelids = explode(',',$liandongrs['fcj_levelids']);
            if(in_array('-1',$fcj_levelids) || in_array($member['levelid'],$fcj_levelids))
            {
                $dongjie_num = $commission*$liandongrs['fcj_bili']*0.01;
                $commission1= $commission;
                $commission = $commission - $dongjie_num;
                self::adddongjienum($aid,$mid,$dongjie_num,$remark.',冻结,冻结比例'.$liandongrs['fcj_bili'].'%','');
                $remark = $remark.',应到账'.$commission1.',现到账'.$commission.',冻结'.$dongjie_num.',冻结比例'.$liandongrs['fcj_bili'].'%';
            }
        }
        $after = $member['commission'] + $commission;
        $update_member = ['totalcommission'=>$totalcommission,'commission'=>$after];
        if($fhtype == 'fenhong') {
            $update_member['total_fenhong_partner'] = $member['total_fenhong_partner'] + $commission;
            $update_member['total_fenhong'] = $member['total_fenhong'] + $commission;
        } elseif($fhtype == 'teamfenhong') {
            
            $update_member['total_fenhong_team'] = $member['total_fenhong_team'] + $commission;
            $update_member['total_fenhong'] = $member['total_fenhong'] + $commission;
        } elseif($fhtype == 'level_teamfenhong') {
            $update_member['total_fenhong_level_team'] = $member['total_fenhong_level_team'] + $commission;
            $update_member['total_fenhong'] = $member['total_fenhong'] + $commission;
        } elseif($fhtype == 'areafenhong') {
            $update_member['total_fenhong_area'] = $member['total_fenhong_area'] + $commission;
            $update_member['total_fenhong'] = $member['total_fenhong'] + $commission;
        }
        Db::name('member')->where('aid',$aid)->where('id',$mid)->update($update_member);

        $data = [];
        $data['aid'] = $aid;
        $data['mid'] = $mid;
        $data['frommid'] = $frommid;
        $data['commission'] = $commission;
        $data['after'] = $after;
        $data['createtime'] = time();
        $data['remark'] = $remark;
        Db::name('member_commissionlog')->insert($data);
        //我得到佣金了,给我的上级上上级 上上级返佣
        if($qu == 0 && $commission>0 )
        {
            self::addfanyong($aid,$mid,$frommid,$commission,'收益抽佣奖励',$remark);
        }
        // 团队分红-新加平级奖
         if(strpos($remark,"团队分红") !== false && strpos($remark,"团队分红-") === false)
        {
           // \think\facade\Log::write('2025-01-03 22:55:53,565-INFO-[Member][addcommission_001] 检测到团队分红，调用统一平级奖处理方法: aid='.$aid.', mid='.$mid.', levelid='.$member['levelid'].', commission='.$commission, 'info');
            
            // 尝试从备注中提取订单时间信息（如果有的话）
            $order_time = 0;
            if (preg_match('/订单时间:(\d+)/', $remark, $matches)) {
                $order_time = intval($matches[1]);
            //    \think\facade\Log::write('2025-01-03 22:55:53,565-INFO-[Member][addcommission_002] 从备注中提取到订单时间: '.date('Y-m-d H:i:s', $order_time), 'info');
            }
            
            \app\common\Fenhong::handleTeamPingjiFenhong($aid, $mid, $member['levelid'], $commission, '团队分红', $order_time);
        }

        return ['status'=>1,'msg'=>''];
    }



/**
 * 添加佣金(不扣除贡献值)
 * @param int $aid 应用ID
 * @param int $mid 会员ID
 * @param int $type 类型
 * @param float $money 金额
 * @param string $remark 备注
 */

 public static function addcommissionWithoutDeduct($aid,$mid,$frommid,$commission,$remark,$addtotal=1,$fhtype='',$qu=0){
    if($commission==0) return ;
    
    $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
    
    if(!$member) return ['status'=>0,'msg'=>t('会员').'不存在'];
    
    
    
    
    $set = Db::name('admin_set')->where('aid',$aid)->find();
    $memberlevel = Db::name('member_level')->where('aid',$aid)->where('id',$memberr['levelid'])->find();
    if($memberlevel['syc'] == 1)
    {
        if($member['syc'] >=  $commission )
        {
            $gwjf = 0;
            $yj = $commission;
            \app\common\Member::addsyc($aid,$mid,'-'.$yj,'佣金到账扣除'.$yj);
        }else{
             $syc_shouyi = json_decode($memberlevel['syc_shouyi'],1);
             $gwjf = $commission *$syc_shouyi['gw']*0.01;
             $yj = $commission*$syc_shouyi['yj']*0.01;
             $commission = $yj;
          //   \app\common\Member::heiscore($aid,$mid,'-'.$yj,'佣金到账扣除'.$yj);
             self::addhei($aid, $mid, $gwjf, '佣金到账,收益池不足,到账金额'.$gwjf);
             
             //分佣到账通知
            $type_str = (strpos($remark,'团队') !== false)?'团队分红':'佣金到账';
            self::addcommission_wxnotify($type_str,$yj,$aid,$formmid,$remark);
             
             
        }
    }
//      
    //检测佣金金额是否能够抵扣创业值;
     //根据佣金更改创业值;(创业值-佣金值)
      //$m_bus_user = intval($member['bus_total']);
      ////但是后台不在意
      //if(strpos($remark,'后台')!=-1)
      //{
      //    if($m_bus_user-$commission >0)
//        {
//              Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['bus_total'=>$m_bus_user-$commission]);
//        }else{
//            return ['status'=>0,'msg'=>t('会员').'创业值不足,请充值创业值']; 
//        }
      //}
          
    //根据佣金增加点数;
    if($commission > 0 && $set['commission2scorepercent'] > 0){
        $oldcommission = $commission;
        $commission = round($commission * (1-$set['commission2scorepercent']*0.01),2);
        $score = $oldcommission - $commission;
        self::addscore($aid,$mid,$score,$remark);
    }

    if($commission > 0 && $addtotal==1){
        $totalcommission = $member['totalcommission'] + $commission;
    }else{
        $totalcommission = $member['totalcommission'];
    }
    $liandongrs = Db::name('liandong_set')->where('aid',$aid)->find();
    if($commission > 0 &&$liandongrs['status'] == 1 && $liandongrs['fcj_bili'] != 0 && strpos($remark,'后台修改') === false && strpos($remark,'解冻') === false&& strpos($remark,'佣金提现') === false && strpos($remark,'后台解冻') === false && $member['dong_status'] !=1 && strpos($remark,'升级解冻') === false)
    {
        $fcj_levelids = explode(',',$liandongrs['fcj_levelids']);
        if(in_array('-1',$fcj_levelids) || in_array($member['levelid'],$fcj_levelids))
        {
            $dongjie_num = $commission*$liandongrs['fcj_bili']*0.01;
            $commission1= $commission;
            $commission = $commission - $dongjie_num;
            self::adddongjienum($aid,$mid,$dongjie_num,$remark.',冻结,冻结比例'.$liandongrs['fcj_bili'].'%','');
            $remark = $remark.',应到账'.$commission1.',现到账'.$commission.',冻结'.$dongjie_num.',冻结比例'.$liandongrs['fcj_bili'].'%';
        }
    }
    $after = $member['commission'] + $commission;
    $update_member = ['totalcommission'=>$totalcommission,'commission'=>$after];
    if($fhtype == 'fenhong') {
        $update_member['total_fenhong_partner'] = $member['total_fenhong_partner'] + $commission;
        $update_member['total_fenhong'] = $member['total_fenhong'] + $commission;
    } elseif($fhtype == 'teamfenhong') {
        
        $update_member['total_fenhong_team'] = $member['total_fenhong_team'] + $commission;
        $update_member['total_fenhong'] = $member['total_fenhong'] + $commission;
    } elseif($fhtype == 'level_teamfenhong') {
        $update_member['total_fenhong_level_team'] = $member['total_fenhong_level_team'] + $commission;
        $update_member['total_fenhong'] = $member['total_fenhong'] + $commission;
    } elseif($fhtype == 'areafenhong') {
        $update_member['total_fenhong_area'] = $member['total_fenhong_area'] + $commission;
        $update_member['total_fenhong'] = $member['total_fenhong'] + $commission;
    }
    Db::name('member')->where('aid',$aid)->where('id',$mid)->update($update_member);

    $data = [];
    $data['aid'] = $aid;
    $data['mid'] = $mid;
    $data['frommid'] = $frommid;
    $data['commission'] = $commission;
    $data['after'] = $after;
    $data['createtime'] = time();
    $data['remark'] = $remark;
    Db::name('member_commissionlog')->insert($data);
    //我得到佣金了,给我的上级上上级 上上级返佣
    if($qu == 0 && $commission>0 )
    {
        self::addfanyong($aid,$mid,$frommid,$commission,'收益抽佣奖励',$remark);
    }
    // 团队分红-新加平级奖
    if(strpos($remark,"团队分红") !== false && strpos($remark,"团队分红-") === false)
    {
        \think\facade\Log::write('2025-01-03 22:55:53,565-INFO-[Member][addcommissionWithoutDeduct_001] 检测到团队分红，调用统一平级奖处理方法: aid='.$aid.', mid='.$mid.', levelid='.$member['levelid'].', commission='.$commission, 'info');
        
        // 尝试从备注中提取订单时间信息（如果有的话）
        $order_time = 0;
        if (preg_match('/订单时间:(\d+)/', $remark, $matches)) {
            $order_time = intval($matches[1]);
        //    \think\facade\Log::write('2025-01-03 22:55:53,565-INFO-[Member][addcommissionWithoutDeduct_002] 从备注中提取到订单时间: '.date('Y-m-d H:i:s', $order_time), 'info');
        }
        
        \app\common\Fenhong::handleTeamPingjiFenhong($aid, $mid, $member['levelid'], $commission, '团队分红', $order_time);
    }

    return ['status'=>1,'msg'=>''];
}



    /**
     * 分红平级奖
     * */
     public static function fafangpingji($aid,$mid,$levelid,$tuanduifenh)
     {
         $levelid = (string)$levelid;
         $curmember = Db::name('member')->field('id,path')->where('aid',$aid)->where('id',$mid)->find(); 
         $pidArr = explode(',',$curmember['path']);
         $pidArr = array_reverse($pidArr);
         $num = 1;
         foreach($pidArr as $v)
         {
             $valuemember = Db::name('member')->field('id,levelid')->where('aid',$aid)->where('id',$v)->find(); 
             $vlauelevel = Db::name('member_level')->field('id,pingjilevelid,pingjilevelidjiangli,pingjilevelid2,pingjilevelidjiangli2,pingjilevelid3,pingjilevelidjiangli3,pingjilevelidjiangli_num')
             ->where('aid',$aid)->where('id',$valuemember['levelid'])->find(); 
             //1.
             if(strpos($vlauelevel['pingjilevelid'],$levelid) !== false)
             {
                 if($tuanduifenh >0){
                     if($num ==1)
                     {
                         $jiangli = $vlauelevel['pingjilevelidjiangli']*$tuanduifenh*0.01;
                         $jiangli =  round($jiangli,2);
                     }else{
                         $jiangli = $tuanduifenh;
                          $jiangli =  round($jiangli,2);
                     }
                     if($vlauelevel['pingjilevelidjiangli_num'] > 0 && $num >  $vlauelevel['pingjilevelidjiangli_num'])
                     {
                         break;
                     }else{
                         self::addcommission($aid,$v,$mid,$jiangli,'团队分红-新加平级奖');
                         $num++;
                         $tuanduifenh = $jiangli;
                     }
                 }else{
                     break;
                 }
             }
             //2
             if(strpos($vlauelevel['pingjilevelid2'],$levelid) !== false)
             {
                 if($tuanduifenh >0){
                     if($num ==1)
                     {
                         $jiangli = $vlauelevel['pingjilevelidjiangli2']*$tuanduifenh*0.01;
                         $jiangli =  round($jiangli,2);
                     }else{
                         $jiangli = $tuanduifenh*0.1;
                          $jiangli =  round($jiangli,2);
                     }
                     self::addcommission($aid,$v,$mid,$jiangli,'团队分红-新加平级奖2');
                     $num++;
                     $tuanduifenh = $jiangli;
                 }else{
                     break;
                 }
             }
             //3.
             if(strpos($vlauelevel['pingjilevelid3'],$levelid) !== false)
             {
                 if($tuanduifenh >0){
                     if($num ==1)
                     {
                         $jiangli = $vlauelevel['pingjilevelidjiangli3']*$tuanduifenh*0.01;
                         $jiangli =  round($jiangli,2);
                     }else{
                         $jiangli = $tuanduifenh*0.1;
                          $jiangli =  round($jiangli,2);
                     }
                     self::addcommission($aid,$v,$mid,$jiangli,'团队分红-新加平级奖');
                     $num++;
                     $tuanduifenh = $jiangli;
                 }else{
                     break;
                 }
             }
             
         }
     }
     /**
      * 分红平级奖2
      * */
     public static function fafangpingji2($aid,$mid,$levelid,$tuanduifenh)
     {
          $levelid = (string)$levelid;
          $curmember = Db::name('member')->field('id,path')->where('aid',$aid)->where('id',$mid)->find(); 
          $pidArr = explode(',',$curmember['path']);
          $pidArr = array_reverse($pidArr);
          $num = 1;
          foreach($pidArr as $v)
          {
             $valuemember = Db::name('member')->field('id,levelid')->where('aid',$aid)->where('id',$v)->find(); 
             $vlauelevel = Db::name('member_level')->field('id,pingjilevelid2,pingjilevelidjiangli2')->where('aid',$aid)->where('id',$valuemember['levelid'])->find(); 
             if(strpos($vlauelevel['pingjilevelid2'],$levelid) !== false)
             {
                 if($tuanduifenh >0){
                     // 所有级别都使用配置的奖励比例
                     $jiangli = $vlauelevel['pingjilevelidjiangli2']*$tuanduifenh*0.01;
                     $jiangli = round($jiangli,2);
                     
                     self::addcommission($aid,$v,$mid,$jiangli,'团队分红-新加平级奖2');
                     $num++;
                     $tuanduifenh = $jiangli;
                 }else{
                     break;
                 }
             }
         }
          
     }
     
     /**
      * 分红平级奖3
      * */
      public static function fafangpingji3($aid,$mid,$levelid,$tuanduifenh)
     {
         $levelid = (string)$levelid;
         $curmember = Db::name('member')->field('id,path')->where('aid',$aid)->where('id',$mid)->find(); 
         $pidArr = explode(',',$curmember['path']);
         $pidArr = array_reverse($pidArr);
         $num = 1;
         foreach($pidArr as $v)
         {
             $valuemember = Db::name('member')->field('id',$v)->find(); 
             $vlauelevel = Db::name('member_level')->field('id,pingjilevelid3,pingjilevelidjiangli3')->where('aid',$aid)->where('id',$valuemember['levelid'])->find(); 
             if(strpos($vlauelevel['pingjilevelid3'],$levelid) !== false)
             {
                 if($tuanduifenh >0){
                     if($num ==1)
                     {
                         $jiangli = $vlauelevel['pingjilevelidjiangli3']*$tuanduifenh*0.01;
                         $jiangli =  round($jiangli,2);
                     }else{
                         $jiangli = $tuanduifenh*0.1;
                          $jiangli =  round($jiangli,2);
                     }
                     self::addcommission($aid,$v,$mid,$jiangli,'团队分红-新加平级奖');
                     $num++;
                     $tuanduifenh = $jiangli;
                 }else{
                     break;
                 }
             }
         }
     } 
    
    public static function addfanyong($aid,$mid,$frommid,$commission,$remark,$remark2)
    {
        //本人
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
        if(!$member) return ['status'=>0,'msg'=>t('会员').'不存在'];
        //一级
        $member1 = Db::name('member')->where('aid',$aid)->where('id',$member['pid'])->lock(true)->find();
        if(!empty($member1))
        {
            $memberlevel1 = Db::name('member_level')->where('aid',$aid)->where('id',$member1['levelid'])->lock(true)->find();
            if($memberlevel1['can_agent'] >0)
            {
                if($memberlevel1['commissionshouyi1'] >0)
                {
                    if($memberlevel1['commissiontype'] == 1)//固定金额
                    {
                        $jiangli = $memberlevel1['commissionshouyi1'];
                    }elseif($memberlevel1['commissiontype'] == 0)//百分比
                    {
                        $jiangli = $commission* $memberlevel1['commissionshouyi1']*0.01;
                    }
                    if($remark2 == '团队分红' || strpos($remark2,'团队') !== false)
                    {
                        if($memberlevel1['tuandui_chouyong'] == 1)
                        {
                            self::addcommission($aid,$member1['id'],$mid,$jiangli,$remark,1,'',1);
                        }
                    }else{
                        self::addcommission($aid,$member1['id'],$mid,$jiangli,$remark,1,'',1);
                    }
                }
                
            }
        }
        //二级
        $member2 = Db::name('member')->where('aid',$aid)->where('id',$member1['pid'])->lock(true)->find();
        if(!empty($member2))
        {
            $memberlevel2 = Db::name('member_level')->where('aid',$aid)->where('id',$member2['levelid'])->lock(true)->find();
            if($memberlevel2['can_agent'] >1)
            {
                 if($memberlevel2['commissionshouyi2'] >0)
                {
                    if($memberlevel2['commissiontype'] == 1)
                    {
                         $jiangli = $memberlevel2['commissionshouyi2'];
                    }elseif($memberlevel2['commissiontype'] == 0)
                    {
                        $jiangli = $commission* $memberlevel2['commissionshouyi2']*0.01;
                    }
                    if($remark2 == '团队分红' || strpos($remark2,'团队') !== false)
                    {
                        if($memberlevel2['tuandui_chouyong'] == 1)
                        {
                             self::addcommission($aid,$member2['id'],$mid,$jiangli,$remark,1,'',1);
                        }
                    }else{
                         self::addcommission($aid,$member2['id'],$mid,$jiangli,$remark,1,'',1);
                    }
                   
                }
            }
        }
       
        //三级
        $member3 = Db::name('member')->where('aid',$aid)->where('id',$member2['pid'])->lock(true)->find();
        if(!empty($member3))
        {
            $memberlevel3 = Db::name('member_level')->where('aid',$aid)->where('id',$member3['levelid'])->lock(true)->find();
            if($memberlevel3['can_agent'] >2)
            {
                if($memberlevel3['commissionshouyi3'] >0)
                {
                    if($memberlevel3['commissiontype'] == 1)
                    {
                         $jiangli = $memberlevel3['commissionshouyi3'];
                    }elseif($memberlevel3['commissiontype'] == 0)
                    {
                         $jiangli = $commission* $memberlevel3['commissionshouyi3']*0.01;
                    }
                    if($remark2 == '团队分红' || strpos($remark2,'团队') !== false)
                    {
                        if($memberlevel3['tuandui_chouyong'] == 1)
                        {
                              self::addcommission($aid,$member3['id'],$mid,$jiangli,$remark,1,'',1);
                        }
                    }else{
                          self::addcommission($aid,$member3['id'],$mid,$jiangli,$remark,1,'',1);
                    }
                   
                }
            }
        }
    }

    public static function addcommission2($aid,$mid,$frommid,$commission,$remark,$addtotal=1,$fhtype=''){
        if($commission==0) return ;
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
        if(!$member) return ['status'=>0,'msg'=>t('会员').'不存在'];

        $set = Db::name('admin_set')->where('aid',$aid)->find();
//        if($commission > 0 && $set['commission2scorepercent'] > 0){
//            $oldcommission = $commission;
//            $commission = round($commission * (1-$set['commission2scorepercent']*0.01),2);
//            $score = $oldcommission - $commission;
//            self::addscore($aid,$mid,$score,$remark);
//        }
//
        $koucyz = 0;
        if($commission > 0 && $addtotal==1){
            if($member['bus_total'] >= $commission)
            {
                //扣除cyz 添加佣金
                $totalcommission = $member['totalcommission2'] + $commission;
                $koucyz = $commission;
            }else{
                $totalcommission = $member['totalcommission2'] +$member['bus_total'];
                $koucyz = $member['bus_total'];
            }
                
        }else{
            $totalcommission = $member['totalcommission2'];
            $koucyz  = 0;
        }
        self::addbusTotal($aid,$mid,$mid,'-'.$koucyz,'到账分红扣除'.t('创业值'));
        $after = $member['commission2'] + $commission;
        $update_member = ['totalcommission2'=>$totalcommission,'commission2'=>$after];
        Db::name('member')->where('aid',$aid)->where('id',$mid)->update($update_member);

        $data = [];
        $data['aid'] = $aid;
        $data['mid'] = $mid;
        $data['frommid'] = $frommid;
        $data['commission'] = $commission;
        $data['after'] = $after;
        $data['createtime'] = time();
        $data['remark'] = $remark;
        Db::name('member_commissionlog')->insert($data);

        return ['status'=>1,'msg'=>''];
    }
    //获取余额
    public static function getmoney($member){
        if(!$member || !$member['id']) return '0.00';
        $member = db('member')->where(['id'=>$member['id']])->find();
        return $member['money'];
    }
    
   
    //获取积分
    public static function getscore($member){
        if(!$member || !$member['id']) return '0';
        $member = db('member')->where(['id'=>$member['id']])->find();

        return $member['score'];
    }
    //获取积分A
    public static function getscoreA($member){
        if(!$member || !$member['id']) return '0';
        $member = db('member')->where(['id'=>$member['id']])->find();

        return $member['scoreA'];
    }
     //获取积分B
    public static function getscoreB($member){
        if(!$member || !$member['id']) return '0';
        $member = db('member')->where(['id'=>$member['id']])->find();

        return $member['scoreB'];
    }


    public static function addw7moneyscore($aid,$member,$type,$money,$remark){
        $w7uniacid = db('admin_set')->where(['aid'=>$aid])->value('w7uniacid');
        if(empty($w7uniacid)) {
            return ['status'=>0,'msg'=>'w7uniacid empty'];
        }
        $fansinfo = Db::connect('w7')->table('ims_mc_mapping_fans')->where("uniacid='{$w7uniacid}' and (openid='{$member['mpopenid']}' or (unionid!='' && unionid is not null && unionid='{$member['unionid']}') or (openid!='' && openid is not null && openid='{$member['wxopenid']}'))")->find();
//        Log::write([
//            'file' => __FILE__,
//            'line' => __LINE__,
//            '$member' => $member,
//            '$fansinfo' => $fansinfo,
//            'sql' =>  Db::connect('w7')->table('ims_mc_mapping_fans')->getLastSql()
//        ]);
        $openid = $member['mpopenid'];
        if(!$openid) $openid = $member['unionid'];
        if(!$openid) $openid = $member['wxopenid'];
        if(!$fansinfo){
            $rec = array();
            $rec['acid'] = $w7uniacid;
            $rec['uniacid'] = $w7uniacid;
            $rec['openid'] = $openid;
            $rec['nickname'] = $member['nickname'];
            $rec['unionid'] = $member['unionid'];
            $rec['follow'] = $member['subscribe'] ? 1 : 0;
            $rec['followtime'] = $member['subscribe_time'] ? $member['subscribe_time'] : $member['createtime'];
            $rec['tag'] = base64_encode(serialize([
                'openid'=>$openid,
                'nickname'=>$member['nickname'],
                'sex'=>$member['sex'],
                'province'=>$member['province'],
                'city'=>$member['city'],
                'country'=>$member['country'],
                'unionid'=>$member['unionid'],
                'subscribe'=>$member['subscribe'],
                'subscribe_time'=>$member['subscribe_time'],
            ]));
            $member2 = array();
            $member2['uniacid'] = $w7uniacid;
            $member2['email'] = md5($openid).'@we7.cc';
            $member2['salt'] = random(8);
            $default_groupid = Db::connect('w7')->table('ims_mc_groups')->where(['uniacid'=>$w7uniacid,'isdefault'=>1])->value('groupid');
            $member2['groupid'] = $default_groupid;
            $member2['createtime'] = time();
            $member2['nickname'] = $member['nickname'];
            $member2['avatar'] = $member['headimg'];
            $member2['nationality'] = $member['country'];
            $member2['resideprovince'] = $member['province'];
            $member2['residecity'] = $member['city'];
            $config = include(ROOT_PATH.'config.php');
            $member2['password'] = md5($openid . $member2['salt'] . $config['authkey']);
            $rec['uid'] = Db::connect('w7')->table('ims_mc_members')->insertGetId($member2);
            Db::connect('w7')->table('ims_mc_mapping_fans')->insertGetId($rec);
        }
        $fansinfo = Db::connect('w7')->table('ims_mc_mapping_fans')->where("uniacid='{$w7uniacid}' and (openid='{$member['mpopenid']}' or (unionid!='' && unionid is not null && unionid='{$member['unionid']}') or (openid!='' && openid is not null && openid='{$member['wxopenid']}'))")->find();
        $uid = $fansinfo['uid'];
        $mcmember = Db::connect('w7')->table('ims_mc_members')->where(['uid'=>$uid])->find();
        if($uid == 0 || !$mcmember){
            $member2 = array();
            $member2['uniacid'] = $w7uniacid;
            $member2['email'] = md5($openid).'@we7.cc';
            $member2['salt'] = random(8);
            $default_groupid = Db::connect('w7')->table('ims_mc_groups')->where(['uniacid'=>$w7uniacid,'isdefault'=>1])->value('groupid');
            $member2['groupid'] = $default_groupid;
            $member2['createtime'] = time();
            $member2['nickname'] = $member['nickname'];
            $member2['avatar'] = $member['headimg'];
            $member2['nationality'] = $member['country'];
            $member2['resideprovince'] = $member['province'];
            $member2['residecity'] = $member['city'];
            $config = include(ROOT_PATH.'config.php');
            $member2['password'] = md5($openid . $member2['salt'] . $config['authkey']);
            $uid = Db::connect('w7')->table('ims_mc_members')->insertGetId($member2);
            Db::connect('w7')->table('ims_mc_mapping_fans')->where(['fanid'=>$fansinfo['fanid']])->update(['uid'=>$uid]);
            $mcmember = Db::connect('w7')->table('ims_mc_members')->where(['uid'=>$uid])->find();
        }
        $after = $mcmember['credit'.$type] + $money;
        Db::connect('w7')->table('ims_mc_members')->where(['uid'=>$uid])->update(['credit'.$type=>$after]);
        $data = array(
            'uid' => $uid,
            'credittype' => 'credit'.$type,
            'uniacid' => $w7uniacid,
            'num' => $money,
            'createtime' => time(),
            'operator' => '',
            'module' => 'ddwx_shop',
            'clerk_id' => '',
            'store_id' => '',
            'clerk_type' => 1,
            'remark' => $remark,
            'real_uniacid' => $uid
        );
        Db::connect('w7')->table('ims_mc_credits_record')->insert($data);

        if($type == 2){
            $data = [];
            $data['aid'] = $aid;
            $data['mid'] = $member['id'];
            $data['money'] = $money;
            $data['after'] = $after;
            $data['createtime'] = time();
            $data['remark'] = $remark;
            Db::name('member_moneylog')->insert($data);
            Db::name('member')->where(['aid'=>$aid,'id'=>$member['id']])->update(['money'=>$after]);
            Wechat::updatemembercard($aid,$member['id']);
        }else{
            $data = [];
            $data['aid'] = $aid;
            $data['mid'] = $member['id'];
            $data['score'] = $money;
            $data['after'] = $after;
            $data['createtime'] = time();
            $data['remark'] = $remark;
            Db::name('member_scorelog')->insert($data);
            Db::name('member')->where(['aid'=>$aid,'id'=>$member['id']])->update(['score'=>$after]);
            Wechat::updatemembercard($aid,$member['id'],$remark);
        }

        return ['status'=>1,'msg'=>''];
    }
    //获取多少级以内的下级含脱离的（去掉缓存版本）
    public static function getdownmids($aid,$mid,$levelnum=0,$levelid=0){
        $downmids = [];
        if($levelid == 0){
            $memberlist = Db::name('member')->field('id,path')->where('aid',$aid)->where('find_in_set('.$mid.',path)')->select()->toArray();
        }else{
            $levelid = str_replace('，',',',$levelid);
            $memberlist = Db::name('member')->field('id,path')->where('aid',$aid)->where('levelid','in',$levelid)->where('find_in_set('.$mid.',path)')->select()->toArray();
            }
        foreach($memberlist as $member){
            if($levelnum == 0){
                $downmids[] = $member['id'];
            }else{
                $path = explode(',',$member['path']);
                $path = array_reverse($path);
                $key = array_search($mid,$path);
                if($key!==false && $key < $levelnum){
                    $downmids[] = $member['id'];
                }
            }
        }
        return $downmids;
    }
        //获取多少级以内的下级 筛选 不含脱离的
    public static function getdownmidsbu($aid,$mid,$levelnum=0,$levelid=0){
        $downmids = [];
//      var_dump($downmids);die;
        if($levelid == 0){
            $memberlist = Db::name('member')->field('id,path')->where('aid',$aid)->where('find_in_set('.$mid.',path)')->where('tuo1','<>',1)->select()->toArray();
        }else{
            $levelid = str_replace('，',',',$levelid);
            $memberlist = Db::name('member')->field('id,path')->where('aid',$aid)->where('levelid','in',$levelid)->where('find_in_set('.$mid.',path)')->where('tuo1','<>',1)->select()->toArray();
            }
        foreach($memberlist as $member){
            if($levelnum == 0){
                $downmids[] = $member['id'];
            }else{
                $path = explode(',',$member['path']);
                $path = array_reverse($path);
                $key = array_search($mid,$path);
                if($key!==false && $key < $levelnum){
                    $downmids[] = $member['id'];
                }
            }
        }
        return $downmids;
    }
    
    public static function getdownmids2($aid,$mid,$cyjj_qudengji,$levelnum=0,$levelid=0)
    {
        $downmids = [];
        if($levelid == 0){
            $memberlist = Db::name('member')->field('id,path,levelid')->where('aid',$aid)->where('find_in_set('.$mid.',path)')->select()->toArray();
        }else{
            $levelid = str_replace('，',',',$levelid);
            $memberlist = Db::name('member')->field('id,path,levelid')->where('aid',$aid)->where('levelid','in',$levelid)->where('find_in_set('.$mid.',path)')->select()->toArray();
        }
        foreach($memberlist as $member){
            if($member['levelid'] != $cyjj_qudengji){
                if($levelnum == 0){
                    $downmids[] = $member['id'];
                }else{
                    $path = explode(',',$member['path']);
                    $path = array_reverse($path);
                    $key = array_search($mid,$path);
                    if($key!==false && $key < $levelnum){
                        $downmids[] = $member['id'];
                    }
                }
          }
        }
        return $downmids;
    }

    public static function getdownlevelmids($aid,$mid,$levelnum=0,$levelid=0){
        $downmids = [];
        if($levelid == 0){
            $memberlist = Db::name('member')->field('id,path')->where('aid',$aid)->where('find_in_set('.$mid.',path)')->select()->toArray();
        }else{
            $levelid = str_replace('，',',',$levelid);
            $memberlist = Db::name('member')->field('id,path')->where('aid',$aid)->where('levelid','in',$levelid)->where('find_in_set('.$mid.',path)')->select()->toArray();
        }
        foreach($memberlist as $member){
            if($levelnum == 0){
                $downmids[] = $member['id'];
            }else{
                $path = explode(',',$member['path']);
                $path = array_reverse($path);
                $key = array_search($mid,$path);
                if($key!==false && $key < $levelnum){
                    $downmids[] = $member['id'];
                }
            }
        }
        return $downmids;
    }
    //获取多少级以内的下级 小区的(即除了人数最多的区的所有区)
    public static function getdownmids_xiao($aid,$mid,$levelnum=0,$levelid=0){
        $childList = Db::name('member')->field('id,path')->where('aid',$aid)->where('pid',$mid)->select()->toArray();

        $downmidsArr = [];
        foreach($childList as $cmember){
            $thisdownmids = self::getdownmids($aid,$cmember['id'],$levelnum,$levelid);
            if(!$thisdownmids){
                $thisdownmids = $cmember['id'];
            }else{
                $thisdownmids[] = $cmember['id'];
            }
            $downmidsArr[] = ['count'=>count($thisdownmids),'mids'=>$thisdownmids];
        }
        $counts = array_column($downmidsArr,'count');
        array_multisort($counts,SORT_DESC,$downmidsArr);

        $downmids = [];
        foreach($downmidsArr as $k=>$v){
            if($k > 0){
                $downmids = array_merge($downmids,$v['mids']);
            }
        }
        return $downmids;
    }

    //获取多少级以内的下级 去除业绩最高的一个
    public static function getdownmids_removemax($aid,$mid,$levelnum=0,$levelid=0){
        $childList = Db::name('member')->field('id,path')->where('aid',$aid)->where('pid',$mid)->select()->toArray();
        $downmidsArr = [];
//      var_dump($downmidsArr);die;
        foreach($childList as $cmember){
            $thisdownmids = self::getdownmids($aid,$cmember['id'],$levelnum,$levelid);
            if(!$thisdownmids){
                $thisdownmids = [$cmember['id']];
            }else{
                $thisdownmids[] = $cmember['id'];
            }
            //\think\facade\Log::write($thisdownmids);
            $fxordermoney = 0 + Db::name('shop_order_goods')->where('status','in','1,2,3')->where('mid','in',$thisdownmids)->sum('totalprice');
            $downmidsArr[] = ['count'=>count($thisdownmids),'mids'=>$thisdownmids,'fxordermoney'=>$fxordermoney];
        }
        //\think\facade\Log::write($downmidsArr);
        $counts = array_column($downmidsArr,'fxordermoney');
        array_multisort($counts,SORT_DESC,$downmidsArr);
        //\think\facade\Log::write($downmidsArr);

        $downmids = [];
        foreach($downmidsArr as $k=>$v){
            if($k > 0){
                $downmids = array_merge($downmids,$v['mids']);
            }
        }
        return $downmids;
    }
    //获取团队的会员id集合 团队中有和他平级或超过他等级的就跳出
    private static $mids = [];

    /**
     * 优化版本：获取团队成员ID集合
     * 使用缓存和批量查询优化性能
     */
    public static function getteammids($aid,$mid,$deep,$levelids,$mids=[],$thisdeep=0){
        $start_time = microtime(true);
        $timestamp = date('Y-m-d H:i:s');

        // 详细记录输入参数
        \think\facade\Log::write($timestamp.'-DEBUG-[Member][getteammids] 输入参数 - aid: '.$aid.'，mid: '.$mid.'，deep: '.$deep.'，levelids: '.implode(',', $levelids), 'info');

        // 检查缓存
        $cache_key = "getteammids_{$aid}_{$mid}_{$deep}_" . md5(implode(',', $levelids));
        \think\facade\Log::write($timestamp.'-DEBUG-[Member][getteammids] 生成缓存键: '.$cache_key, 'info');

        // 再次禁用缓存，问题复现了
        $cached_result = false; // 强制重新计算
        // $cached_result = cache($cache_key); // 等彻底修复后再启用

        if ($cached_result !== false) {
            $cache_time = round((microtime(true) - $start_time) * 1000, 2);
            \think\facade\Log::write($timestamp.'-DEBUG-[Member][getteammids] 使用缓存结果，耗时: '.$cache_time.'ms，成员数量: '.count($cached_result).'，缓存键: '.$cache_key, 'info');

            // 如果缓存结果为0且用户应该有团队成员，记录警告
            if (count($cached_result) == 0) {
                \think\facade\Log::write($timestamp.'-WARNING-[Member][getteammids] 缓存返回0个成员，可能是错误缓存，建议清除缓存重新计算', 'info');
            }

            return $cached_result;
        }

        \think\facade\Log::write($timestamp.'-DEBUG-[Member][getteammids] 开始计算团队成员，aid: '.$aid.'，mid: '.$mid.'，deep: '.$deep.'，levelids: '.implode(',', $levelids), 'info');

        // 使用优化算法
        if ($deep > 50 || count($levelids) > 5) {
            // 大数据量使用基于path的查询
            $result = self::getteammids_optimized($aid, $mid, $deep, $levelids);
        } else {
            // 小数据量使用原始递归方法
            $result = self::getteammids_original($aid, $mid, $deep, $levelids, $mids, $thisdeep);
        }

        // 缓存结果（10分钟）
        cache($cache_key, $result, 600);

        $total_time = round((microtime(true) - $start_time) * 1000, 2);
        \think\facade\Log::write($timestamp.'-DEBUG-[Member][getteammids] 计算完成，总耗时: '.$total_time.'ms，成员数量: '.count($result), 'info');

        return $result;
    }

    /**
     * 优化版本：基于path字段的快速查询
     */
    private static function getteammids_optimized($aid, $mid, $deep, $levelids) {
        $start_time = microtime(true);
        $timestamp = date('Y-m-d H:i:s');

        // 使用path字段进行快速查询
        $levelids_str = implode(',', $levelids);
        \think\facade\Log::write($timestamp.'-DEBUG-[Member][getteammids_optimized_001] 开始优化查询，aid: '.$aid.'，mid: '.$mid.'，deep: '.$deep.'，levelids: '.$levelids_str, 'info');

        $members = Db::name('member')
            ->field('id,path,nickname,levelid')
            ->where('aid', $aid)
            ->where('levelid', 'in', $levelids_str)
            ->where('find_in_set(' . $mid . ',path)')
            ->select()
            ->toArray();

        $query_time = round((microtime(true) - $start_time) * 1000, 2);
        \think\facade\Log::write($timestamp.'-DEBUG-[Member][getteammids_optimized_002] 数据库查询完成，耗时: '.$query_time.'ms，查询到: '.count($members).'条记录，SQL: '.Db::getLastSql(), 'info');

        if (count($members) > 0) {
            \think\facade\Log::write($timestamp.'-DEBUG-[Member][getteammids_optimized_003] 前3个查询结果: '.json_encode(array_slice($members, 0, 3)), 'info');
        }

        $result = [];
        $filtered_count = 0;
        foreach ($members as $member) {
            if ($deep > 0) {
                // 检查层级深度
                $path = explode(',', $member['path']);
                $path = array_reverse($path);
                $key = array_search($mid, $path);

                if ($key !== false && $key < $deep) {
                    $result[] = $member['id'];
                } else {
                    $filtered_count++;
                    if ($filtered_count <= 3) {
                        \think\facade\Log::write($timestamp.'-DEBUG-[Member][getteammids_optimized_004] 成员被层级过滤: ID='.$member['id'].'，path='.$member['path'].'，key='.$key.'，deep='.$deep, 'info');
                    }
                }
            } else {
                $result[] = $member['id'];
            }
        }

        $total_time = round((microtime(true) - $start_time) * 1000, 2);
        \think\facade\Log::write($timestamp.'-DEBUG-[Member][getteammids_optimized_005] 优化查询完成，总耗时: '.$total_time.'ms，最终结果: '.count($result).'个成员，被层级过滤: '.$filtered_count.'个', 'info');

        return $result;
    }

    /**
     * 原始递归方法（用于小数据量）
     * 修复静态变量污染问题
     */
    private static function getteammids_original($aid,$mid,$deep,$levelids,$mids=[],$thisdeep=0){
        // 使用传入的$mids参数而不是静态变量，避免多用户间的数据污染
        if($thisdeep == 0){
            $mids = []; // 初始化时清空
        }
        $thisdeep = $thisdeep+1;
        if($thisdeep > $deep) return $mids;

        $dowmids = Db::name('member')->where('aid',$aid)->where('pid',$mid)->where('levelid','in',$levelids)->column('id');
        if($dowmids){
            foreach($dowmids as $downmid){
                if(!in_array($downmid,$mids)){
                    $mids[] = $downmid;
                    // 递归调用，传递$mids参数
                    $mids = self::getteammids_original($aid,$downmid,$deep,$levelids,$mids,$thisdeep);
                }
            }
        }
        return $mids;
    }

    public static function addHongbaoEverydayEdu($aid,$mid,$money,$remark,$ogid=0){
        if($money==0) return ;
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
        if(!$member) return ['status'=>0,'msg'=>t('会员').'不存在'];

        $after = $member['hongbao_everyday_edu'] + $money;
        Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['hongbao_everyday_edu'=>$after]);

        $data = [];
        $data['aid'] = $aid;
        $data['mid'] = $mid;
        $data['money'] = $money;
        $data['ogid'] = $ogid;
        $data['createtime'] = time();
        $data['remark'] = $remark;
        Db::name('member_hbe_edu_record')->insert($data);
        return ['status'=>1,'msg'=>''];
    }

    public static function addHongbaoLog($aid,$mid,$money,$remark){

        if($money==0) return ;
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->find();
        if(!$member) return ['status'=>0,'msg'=>t('会员').'不存在'];

        $afterTotal = $member['hongbao_ereryday_total'] + $money;

        $data = [];
        $data['aid'] = $aid;
        $data['mid'] = $mid;
        $data['money'] = $money;
        $data['after'] = $afterTotal;
        $data['createtime'] = time();
        $data['remark'] = $remark;
        Db::name('member_hbe_log')->insert($data);
        return ['status'=>1,'msg'=>''];
    }

    //加余额宝
    public static function addyuebaomoney($aid,$mid,$money,$remark,$type=0){

        if($money==0) return ;
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->find();
        if(!$member) return ['status'=>0,'msg'=>t('会员').'不存在'];

        $after = $member['yuebao_money'] + $money;
        Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['yuebao_money'=>$after]);

        $data = [];
        $data['aid'] = $aid;
        $data['mid'] = $mid;
        $data['money'] = $money;
        $data['after'] = $after;
        $data['type']  = $type;
        $data['createtime'] = time();
        $data['remark'] = $remark;
        Db::name('member_yuebao_moneylog')->insert($data);

        return ['status'=>1,'msg'=>''];
    }
    //加元宝
    public static function addyuanbao($aid,$mid,$yuanbao,$remark){
        }
    //加其他余额
    public static function addOtherMoney($aid,$mid,$type,$money,$remark){
        }
        
     /**
 * 获取直推业绩
 */
public static function getZhituiYeji($aid, $mid)
{
    // 获取直推会员
    $memberList = Db::name('member')->field('id')->where('aid', $aid)->where('pid', $mid)->select()->toArray();
    if(empty($memberList)) {
        return 0;
    }
    
    $memberIds = array_column($memberList, 'id');
    $memberStr = implode(',', $memberIds);
    
    // 计算直推会员的订单业绩
    $where = [
        ['order.aid', '=', $aid],
        ['order.status', '>=', 1],
        ['order.status', '<>', 4]
    ];
    
    $yejiCount = Db::name('shop_order')
        ->alias('order')
        ->where($where)
        ->where('order.mid', 'in', $memberStr)
        ->where('good.is_yeji', '=', 1)
        ->join('shop_order_goods good', 'order.id=good.orderid')
        ->sum('good.totalprice');
        
    return $yejiCount ?: 0;
}

/**
 * 获取团队总业绩
 */
public static function getTuanduiYeji($aid, $mid)
{
    // 获取所有下级会员
    $memberAll = self::getdownmids($aid, $mid);
    if(empty($memberAll)) {
        return 0; 
    }
    
    $memberStr = implode(',', $memberAll);
    
    // 计算团队总业绩
    $where = [
        ['order.aid', '=', $aid],
        ['order.status', '>=', 1], 
        ['order.status', '<>', 4]
    ];
    
    $yejiCount = Db::name('shop_order')
        ->alias('order')
        ->where($where)
        ->where('order.mid', 'in', $memberStr)
        ->where('good.is_yeji', '=', 1)
        ->join('shop_order_goods good', 'order.id=good.orderid')
        ->sum('good.totalprice');
        
    return $yejiCount ?: 0;
}

/**
 * 获取我的团队业绩（去除一个最大区域业绩）
 * @param $aid
 * @param $mid
 * @return float|int
 */
public static function getmytuanduiyeji_removemax($aid, $mid)
{
    $yeji = 0;
    $member = Db::name('member')->where(['id' => $mid, 'aid' => $aid])->find();
    if (!$member) {
        return $yeji;
    }
    
    // 获取所有直推下级
    $list = Db::name('member')->where(['aid' => $aid, 'pid' => $mid])->select()->toArray();
    if (empty($list)) {
        return $yeji;
    }

    $areaYejis = []; // 各区域业绩
    
    // 计算每个直推下级的区域业绩
    foreach ($list as $v) {
        $downMids = self::getdownmids($aid, $v['id']);
        if (empty($downMids)) {
            $downMids = [$v['id']];
        } else {
            $downMids[] = $v['id'];
        }
        
        // 计算该区域的总业绩
        $where = [
            ['order.aid', '=', $aid],
            ['order.status', '>=', 1], 
            ['order.status', '<>', 4]
        ];
        
        $areaYeji = Db::name('shop_order')
            ->alias('order')
            ->where($where)
            ->where('order.mid', 'in', $downMids)
            ->where('good.is_yeji', '=', 1)
            ->join('shop_order_goods good', 'order.id=good.orderid')
            ->sum('good.totalprice');
        
        $areaYejis[] = $areaYeji ?: 0;
    }
    
    // 如果有区域业绩
    if (!empty($areaYejis)) {
        // 找出最大的区域业绩
        $maxYeji = max($areaYejis);
        
        // 计算总业绩
        $totalYeji = array_sum($areaYejis);
        
        // 总业绩减去最大区域业绩
        $yeji = $totalYeji - $maxYeji;
    }
    
    return $yeji;
}

    /**
     * A区业绩
     * */
     public static function gettanduiA($aid,$mid)
     {
        //首推第一人无限极业绩相加
        $memberone = Db::name('member')->field('id,nickname,levelid,createtime')->where('aid',$aid)->where('pid',$mid)->order('createtime','asc')->limit(1)->select()->toArray();
        if(!empty($memberone)){
            $memberAll = self::getdownmids($aid,$memberone[0]['id']);
            $memberAll[] = $memberone[0]['id'];
            $memberstr = implode(',',$memberAll);
            $set = Db::name('admin_set')->where('aid',$aid)->find();
            $where = [['order.aid','=',$aid]];
            $where[] = ['order.status','>=',1];
            $where[] = ['order.status','<>',4];
            $ordercount = Db::name('shop_order')->alias('order')->where($where)->where('order.mid','in',$memberstr)->where('good.is_yeji','=',1)->join('shop_order_goods good','order.id=good.orderid')->field('order.mid,order.id,good.id as ordergoodsid,good.jiandianjiangli,good.totalprice')->select()->toArray();
            $yejicount = 0;
            foreach($ordercount as $v)
            {
                $yejicount = $yejicount + $v['totalprice'];
            }
            return $yejicount;
        }else{
            return 0;
        }
     }
     /**
     * B区业绩
     * */
     public static function gettanduiB($aid,$mid)
     {
        //首推第二人无限极业绩相加
        $membertwo = Db::name('member')->field('id,nickname,levelid,createtime')->where('aid',$aid)->where('pid',$mid)->order('createtime','asc')->limit(1,1)->select()->toArray();
        if(!empty($membertwo)){
            $memberAll = self::getdownmids($aid,$membertwo[0]['id']);
            $memberAll[] = $membertwo[0]['id'];
            $memberstr = implode(',',$memberAll);
            $set = Db::name('admin_set')->where('aid',$aid)->find();
            $where = [['order.aid','=',$aid]];
            $where[] = ['order.status','>=',1];
            $where[] = ['order.status','<>',4];
            $ordercount = Db::name('shop_order')->alias('order')->where($where)->where('order.mid','in',$memberstr)->where('good.is_yeji','=',1)->join('shop_order_goods good','order.id=good.orderid')->field('order.mid,order.id,good.id as ordergoodsid,good.jiandianjiangli,good.totalprice')->select()->toArray();
            $yejicount = 0;
            foreach($ordercount as $v)
            {
                $yejicount = $yejicount + $v['totalprice'];
            }
            return $yejicount;
        }else{
            return 0;
        }
     }
     /**
     * C区业绩
     * */
     public static function gettanduiC($aid,$mid)
     {
         //除去前两人无限极业绩相加
         $memberc = Db::name('member')->field('id,nickname,levelid,createtime')->where('aid',$aid)->where('pid',$mid)->order('createtime','asc')->limit(2,9999999999)->select()->toArray();
        if(!empty($memberc)){
            $memberAll = [];
            foreach($memberc as $v)
            {
                 $memberAll[] = $v['id'];
                 $memberAll2 = self::getdownmids($aid,$v['id']);
                 $memberAll = array_merge($memberAll,$memberAll2);
            }
            $memberstr = implode(',',$memberAll);
            $set = Db::name('admin_set')->where('aid',$aid)->find();
            $where = [['order.aid','=',$aid]];
            $where[] = ['order.status','>=',1];
            $where[] = ['order.status','<>',4];
            $ordercount = Db::name('shop_order')->alias('order')->where($where)->where('order.mid','in',$memberstr)->where('good.is_yeji','=',1)->join('shop_order_goods good','order.id=good.orderid')->field('order.mid,order.id,good.id as ordergoodsid,good.jiandianjiangli,good.totalprice')->select()->toArray();
            $yejicount = 0;
            foreach($ordercount as $v)
            {
                $yejicount = $yejicount + $v['totalprice'];
            }
            return $yejicount;
        }else{
            return 0;
        }
     }

     
        
     /**
      * 得到签到分红
      * */
     public function getqianfh($aid,$mid,$signset)
     {
        $starttime = strtotime(date('Y-m-d 00:00:00',strtotime('-1 days')));
        $endtime = strtotime(date('Y-m-d 23:59:59',strtotime('-1 days')));
        //1.获取昨日订单的 30%
        $alltotalprice = Db::name('shop_order')->where('status','in','1,2,3')->where('aid',$aid)->where('paytime','>=',$starttime)->where('paytime','<=',$endtime)->sum('totalprice');
       
        $jiang = $alltotalprice*$signset['jiangfenh']*0.01;
        //2.计算自己的订单总额
         $mytotalprice = Db::name('shop_order')->where('status','in','1,2,3')->where('aid',$aid)->where('mid',$mid)->where('paytime','>=',$starttime)->where('paytime','<=',$endtime)->sum('totalprice');
        //3.计算自己的点数
        $mydian = $mytotalprice/$signset['dianshu'];
        $zongdian = $alltotalprice/$signset['dianshu'];
        if($zongdian != 0){
            $yidian = $jiang/$zongdian;
            //4.计算分红
            $fh = $yidian*$mydian;
            $fh = round($fh,2);
            if($fh > $signset['maxchuang'])
            {
                $fh = $signset['maxchuang'];
            }
        }else{
             $fh = 0;
        }
        return $fh;
     }
     
     /**
      * 预计总分红数
      * */
      public function getqianfh2($aid,$signset)
     {
         $starttime = strtotime(date('Y-m-d 00:00:00',strtotime('-1 days')));
        $endtime = strtotime(date('Y-m-d 23:59:59',strtotime('-1 days')));
        //1.获取昨日订单的 30%
        $alltotalprice = Db::name('shop_order')->where('status','in','1,2,3')->where('aid',$aid)->where('paytime','>=',$starttime)->where('paytime','<=',$endtime)->sum('totalprice');
        $jiang = $alltotalprice*$signset['jiangfenh']*0.01;
        return  round($jiang,2);
     }
     /**
      * 预计总分红数
      * */
      public function getdanqianfh($aid,$signset)
     {
         $starttime = strtotime(date('Y-m-d 00:00:00',strtotime('-1 days')));
        $endtime = strtotime(date('Y-m-d 23:59:59',strtotime('-1 days')));
        //1.获取昨日订单的 30%
        $alltotalprice = Db::name('shop_order')->where('status','in','1,2,3')->where('aid',$aid)->where('paytime','>=',$starttime)->where('paytime','<=',$endtime)->sum('totalprice');
        $jiang = $alltotalprice*$signset['jiangfenh']*0.01;
        //获取会员的所有的分红点数
        $dis=Db::name('Member')->where('aid',aid)->field('sum(fenhong_num) as z_fenhong_num')->find();
        $z_fenhong_num=0;
        if($dis['z_fenhong_num']){
            $z_fenhong_num=$dis['z_fenhong_num'];
        }
        $jiang2 =  $jiang/$z_fenhong_num;
        return  round($jiang2,2);
     }
     
     
     public function getfhdian3($aid,$mid,$signset,$fenhong_num=0)
     {
         $starttime = strtotime(date('Y-m-d 00:00:00',strtotime('-1 days')));
        $endtime = strtotime(date('Y-m-d 23:59:59',strtotime('-1 days')));
        //1.获取昨日订单的 30%
        $alltotalprice = Db::name('shop_order')->where('status','in','1,2,3')->where('aid',$aid)->where('paytime','>=',$starttime)->where('paytime','<=',$endtime)->sum('totalprice');
        $jiang = $alltotalprice*$signset['jiangfenh']*0.01;
        //2.计算自己的订单总额
         $mytotalprice = Db::name('shop_order')->where('status','in','1,2,3')->where('aid',$aid)->where('mid',$mid)->where('paytime','>=',$starttime)->where('paytime','<=',$endtime)->sum('totalprice');
        //3.计算自己的点数
        $mydian = $mytotalprice/$signset['dianshu'];
        //4 加上设置的分红点数
        $mydian=$mydian+$fenhong_num;
        return round($mydian,2);
     }
     
    /**
     * @param $shop
     * @return 获取商品的红包抵现金额
     */
     public function getShopProductScoredkmaxsethuang($aid,$shop){
         $datas=array(
             'types'=>0,  //是否抵现
             'nums'=>0,  //抵现金额
         );
         if($shop['scoredkmaxsethuang']){
             switch ($shop['scoredkmaxsethuang']){
                 case 0:
                     //获取系统的
                     $set = Db::name('admin_set')->where('aid',$aid)->find();
                     if($set['score2moneyhuang']>0){
                         $datas['types']=1;
                         $datas['nums']=$set['scoredkmaxpercenthuang']."%";
                     }
                     break;
                 case 1:
                     //获取百分比
                     $datas['types']=1;
                     $datas['nums']=$shop['scoredkmaxunithuang']."%";
                     break;
                 case 2:
                     //获取抵现元
                     $datas['types']=1;
                     $datas['nums']=$shop['scoredkmaxunithuang']."元";
                     break;
             }
         }

         return $datas;

     }
     
     
    public function getLyjceshi($aid,$mid){
           $order = [
               'times'=>date('Y-m-d H:i:s')
            ];
            Db::name('member_lyj_logs')->insert($order);
    }
     
    public static function user_jiedianren($aid,$mid,$pid)
    {

       $jiedianpid = $pid;
       $jiuxingsetinfo = Db::name('jifechi')->where('aid',$aid)->find();
       $jiuxingsetinfo = json_decode($jiuxingsetinfo['jiuxing'],1);
       if($jiuxingsetinfo['jiuxing_status'] == 1)
       {

           $sethualuocount = $jiuxingsetinfo['hualuo'];
           if($sethualuocount >0)
           {
                //修改当前用户的接点人
                //1.获取我的上级 的下级人数
                $membercount = Db::name('member')->field('id,path,levelid')->where('aid',$aid)->where('jiedianpid',$pid)->count();
                if($membercount >= $sethualuocount )
                {
                    if($jiuxingsetinfo['hualuo_status'] == 0){
                        //如果大于等下 设置的人数 就要向下滑落
                        $jiedianpid = self::getjiedianpid($pid,$sethualuocount,$aid);
                    }elseif($jiuxingsetinfo['hualuo_status'] == 1)
                    {
                        
                       //  公排制
                       $jiedianpid = self::getjiedianpidgongpaizhi($pid,$sethualuocount,$aid);
                       
                    }
                    
                }
           }
           Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['jiedianpid'=>$jiedianpid]);
       }
       
       // 添加联动人设置
       $liandongrs = Db::name('liandong_set')->where('aid',$aid)->find();
       if(!empty($liandongrs) && $liandongrs['status'] == 1) {
           // 联动2+1功能已启用，设置联动人为推荐人
           Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['liandongren'=>$pid]);
       }
       // 检查帮扶模式滑落逻辑
        self::checkBangfuHuala($aid, $pid, $mid);// 检查帮扶模式滑落
       return $jiedianpid;
    }
    
    
    
  public static function checkBangfuHuala($aid, $pid, $mid) 
{
    $timestamp = date('Y-m-d H:i:s');
    Log::write($timestamp.'-INFO-[Member][checkBangfuHuala_001] 开始检查帮扶模式滑落，推荐人ID='.$pid.'，当前会员ID='.$mid);
    
    // 1. 检查是否开启帮扶模式
    $liandongSet = Db::name('liandong_set')->where('aid', $aid)->find();
    if (empty($liandongSet) || $liandongSet['bangfu_status'] != 1) {
        return false;
    }
    
    // 2. 获取推荐人信息
    $parent = Db::name('member')->field('id,levelid')->where('aid', $aid)->where('id', $pid)->find();
    if (empty($parent)) {
        return false;
    }
    
    // 3. 检查推荐人是否属于滑落等级(218)
    $hualaLevelids = explode(',', $liandongSet['huala_levelids']);
    $parentLevelid = (string)$parent['levelid'];
    if (!in_array($parentLevelid, $hualaLevelids)) {
        return false;
    }
    
    // 4. 查找推荐人下级中的财富点位等级会员
    $caifuLevelids = explode(',', $liandongSet['caifu_levelids']);
    $downMembers = Db::name('member')->field('id,levelid')->where('aid', $aid)->where('pid', $pid)->select()->toArray();
    
    $caifuMid = 0;
    foreach ($downMembers as $downMember) {
        if ($downMember['id'] == $mid) {
            continue;
        }
        
        $downMemberLevelid = (string)$downMember['levelid'];
        if (in_array($downMemberLevelid, $caifuLevelids)) {
            $caifuMid = $downMember['id'];
            break;
        }
    }
    
    if ($caifuMid == 0) {
        return false;
    }
    
    // 5. 获取当前会员信息(用于更新路径)
    $member = Db::name('member')->field('id,path')->where('aid', $aid)->where('id', $mid)->find();
    
    // 6. 更新会员信息
    $oldPid = $pid;
    $updateData = [
        'pid' => $caifuMid,
        'ypid' => $oldPid,
        'tuo1' => 2
    ];
    
    $updateResult = Db::name('member')->where('aid', $aid)->where('id', $mid)->update($updateData);
    
    // 7. 记录滑落日志
    $logData = [
        'aid' => $aid,
        'mid' => $mid,
        'ytpid' => $oldPid,
        'newpid' => $caifuMid,
        'createtime' => time(),
        'type' => 2
    ];
    
    Db::name('liandong_tuoli_log')->insert($logData);
    
    // 8. 更新路径
    $oldPath = $member['path'];
    $caifuMemberInfo = Db::name('member')->field('path')->where('aid', $aid)->where('id', $caifuMid)->find();
    if ($caifuMemberInfo) {
        $newPath = $caifuMemberInfo['path'] . ',' . $caifuMid;
        Db::name('member')->where('aid', $aid)->where('id', $mid)->update(['path' => $newPath]);
        
        // 更新所有下级的路径
        $downlineMembers = Db::name('member')->field('id,path')->where('aid', $aid)->where('path', 'like', $oldPath . ',' . $mid . '%')->select();
        foreach ($downlineMembers as $downlineMember) {
            $downlineOldPath = $downlineMember['path'];
            $downlineNewPath = str_replace($oldPath . ',' . $mid, $newPath . ',' . $mid, $downlineOldPath);
            Db::name('member')->where('aid', $aid)->where('id', $downlineMember['id'])->update(['path' => $downlineNewPath]);
        }
    }
    
   // Log::write($timestamp.'-INFO-[Member][checkBangfuHuala_002] 会员滑落成功: 会员ID='.$mid.' 原推荐人='.$oldPid.' 新推荐人='.$caifuMid);
    return true;
}


     public static function getjiedianpid($pid,$sethualuocount,$aid)
     {
        $memberlister =  Db::name('member')->field('id')->where('aid',$aid)->where('jiedianpid',$pid)->select()->toArray();
        foreach($memberlister as $v)
        {
            $membercount = Db::name('member')->field('id,path,levelid')->where('aid',$aid)->where('jiedianpid',$v['id'])->count(); 
            if($membercount < $sethualuocount)
            {
                 return $v['id'];
            }
        }
        foreach($memberlister as $v)
        {
            return self::getjiedianpid($v['id'],$sethualuocount,$aid);
        }
     }
     
     public static function getjiedianpidgongpaizhi($pid,$sethualuocount,$aid)
     {
        //1.查看是我推荐的第几个人
        $count = Db::name('member')->field('id')->where('aid',$aid)->where('pid',$pid)->count();
        $beishu = floor($count/$sethualuocount);
        $number = $count-$sethualuocount* $beishu;
        if($number == 0)
        {
            $number = floor($sethualuocount);
        }
        $memberlister =  Db::name('member')->field('id')->where('aid',$aid)->where('jiedianpid',$pid)->select()->toArray();
        $member =  $memberlister[$number-1];
        $membercount = Db::name('member')->field('id,path,levelid')->where('aid',$aid)->where('jiedianpid',$member['id'])->count(); 
        if($membercount < $sethualuocount)
        {
             return $member['id'];
        }
        //获取无限下级第一个
        $jiedianpid = self::getjiedianpidone($member['id'],$sethualuocount,$aid);
        return $jiedianpid;
     }
     public static function  getjiedianpidone($pid,$sethualuocount,$aid)
     {
         $memberlister =  Db::name('member')->field('id')->where('aid',$aid)->where('jiedianpid',$pid)->select()->first();
         $membercount = Db::name('member')->field('id,path,levelid')->where('aid',$aid)->where('jiedianpid',$memberlister['id'])->count(); 
         if($membercount < $sethualuocount)
         {
             return $memberlister['id'];
         }else{
              return self::getjiedianpidone($memberlister['id'],$sethualuocount,$aid);
         }
         
     }
     
     public static function getjieidanpid($aid,$mid,$totalprice,$xingji,$orderid,$oldxingji)
     {
        $jiuxingsetinfo = Db::name('jifechi')->where('aid',$aid)->find();
        $jiuxingsetinfo = json_decode($jiuxingsetinfo['jiuxing'],1);
        if($jiuxingsetinfo['jiuxing_status'] == 1)
        {
            if ($xingji >0) {
                if($xingji == 1)
                {
                     //如果下单人星级是1 那就直接给一级上级完全返
                    $xiadan_member =  Db::name('member')->field('id,jiedianpid')->where('aid',$aid)->where('id',$mid)->find(); 
                    $parentid = $xiadan_member['jiedianpid'];
                    if($parentid > 0)
                    {
                        //   && $parent_member['xingji'] >= $xingji)
                        $parent_member =  Db::name('member')->field('id,jiedianpid,xingji')->where('aid',$aid)->where('id',$parentid)->find(); 
                        if(!empty($parent_member)){
                            $jiangli = $totalprice*$jiuxingsetinfo['jiangli']*0.01;
                            if($parent_member['xingji'] >0){
                                //打钱进佣金
                                Db::name('member_commission_record')->insert(['aid'=>$aid,'mid'=>$parentid,'frommid'=>$mid,'orderid'=>$orderid,'ogid'=>0,'type'=>'shop','commission'=>$jiangli,'remark'=>'九星奖励下一级消费'.$totalprice.'元,奖励'.$jiangli,'createtime'=>time()]);
                            }else{
                                $data=['aid'=>$aid,'mid'=>$parentid,'formid'=>$mid,'money'=>$jiangli,'afterxingji'=>$xingji,'xingji'=>$oldxingji,'createtime'=>time()];
                                Db::name('member_xingjilog')->insert($data);
                                
                            }
                        }
                    }
                }elseif($xingji >=2 && $xingji <=9)
                {
                    //如果下单人星级不是1
                    $iskou = 1;
                    $jiangli = $jiuxingsetinfo['xiaofeimoney']*$jiuxingsetinfo['jiangli']*0.01;
                    $xingcha = $xingji - $oldxingji;
                    if($xingcha != 1){
                        //多级返
                        if($oldxingji == 0)
                        {
                            $memberlist =  self::getdownjieidanparent($aid,$mid,1,[],$xingji);
                        }else{
                            $memberlist =  self::getdownjieidanparent($aid,$mid,1,[],$xingji,[],$oldxingji);
                        }
                        foreach($memberlist as $key=>$parentid)
                        {
                              $num = 0;
                              $jiangli = $jiuxingsetinfo['xiaofeimoney']*$jiuxingsetinfo['jiangli']*0.01;
                              $parent_member =  Db::name('member')->field('id,nickname,jiedianpid,xingji')->where('aid',$aid)->where('id',$parentid)->find(); 
                              if(!empty($parent_member))
                              {
                                  if($oldxingji !=0)
                                  {
                                     $num = $key+$oldxingji+1;
                                  }else
                                  {
                                      $num = $key+1;
                                  }
                                    if($parent_member['xingji'] >0 && $parent_member['xingji'] >= $num){
                                        
                                        $membercount = Db::name('member')->where('aid',$aid)->where('pid',$parentid)->where('xingji','>=',1)->count();
                                       // var_dump($membercount);
                                        if($num == 1)
                                        {
                                            
                                        }else{
                                            if($membercount < $jiuxingsetinfo['meizhitui'])
                                            {
                                                $jiangli = $jiangli*$jiuxingsetinfo['zhekou']*0.1;
                                            }
                                        }
                                        // var_dump($jiangli);
                                        Db::name('member_commission_record')->insert(['aid'=>$aid,'mid'=>$parentid,'frommid'=>$mid,'orderid'=>$orderid,'ogid'=>0,'type'=>'shop','commission'=>$jiangli,'remark'=>'九星奖励会员消费'.$totalprice.'元,奖励'.$jiangli,'createtime'=>time()]);
                                    }else{
                                        $data=['aid'=>$aid,'mid'=>$parentid,'formid'=>$mid,'money'=>$jiangli,'afterxingji'=>$xingji,'xingji'=>$oldxingji,'createtime'=>time()];
                                        Db::name('member_xingjilog')->insert($data);
                                        
                                    }
                              }
                              
                        }
                    }else{
                        $memberlist =  self::getdownjieidanparent($aid,$mid,1,[],$xingji);
                        $key= $xingji -1;
                        if (isset($memberlist[$key])) {
                            //  && $parent_member['xingji'] >= $xingji
                            $parentid = $memberlist[$key];
                             $parent_member =  Db::name('member')->field('id,jiedianpid,xingji')->where('aid',$aid)->where('id',$parentid)->find(); 
                             if(!empty($parent_member)){
                                  if($parent_member['xingji'] >0 && $parent_member['xingji'] >= $xingji){
                                      $membercount = Db::name('member')->where('aid',$aid)->where('pid',$parentid)->where('xingji','>=',1)->count();

                                      if($membercount < $jiuxingsetinfo['meizhitui']){
                                          $jiangli = $jiangli*$jiuxingsetinfo['zhekou']*0.1;
                                      }
                                    Db::name('member_commission_record')->insert(['aid'=>$aid,'mid'=>$parentid,'frommid'=>$mid,'orderid'=>$orderid,'ogid'=>0,'type'=>'shop','commission'=>$jiangli,'remark'=>'九星奖励会员消费'.$totalprice.'元,奖励'.$jiangli,'createtime'=>time()]);
                                  }else{
                                        $data=['aid'=>$aid,'mid'=>$parentid,'formid'=>$mid,'money'=>$jiangli,'afterxingji'=>$xingji,'xingji'=>$oldxingji,'createtime'=>time()];
                                        Db::name('member_xingjilog')->insert($data);
                                        
                                    }
                             }
                        }
                    }
                }
            }
        }
     }
     
    //  $xingji
     public static function getdownjieidanparent($aid,$mid,$deeplevel,$leveldata,$zuidacount=9,$parent=[],$oldxingji=0){
        $curmember =  Db::name('member')->field('id,jiedianpid')->where('aid',$aid)->where('id',$mid)->find(); 
        if($deeplevel > $zuidacount){
            return $parent;
        }else{
            $memberparent = Db::name('member')->field('id,nickname,headimg,pid,realname,tel,levelid')->where('aid',$aid)->where('id',$curmember['jiedianpid'])->find();
            if($oldxingji ==0){
               $parent[]=$memberparent['id'];
            }elseif($oldxingji > 0 && $deeplevel > $oldxingji){
                $parent[]=$memberparent['id'];
            }
            $deeplevel++;
            return self::getdownjieidanparent($aid,$memberparent['id'],$deeplevel,$leveldata,$zuidacount,$parent,$oldxingji);
        }
    }
    
    
     public static function getdownxiaji($mid,$deeplevel,$leveldata,$xingji,$aid,$str=''){
        if($deeplevel > $xingji) return [];
        $deeplevel++;
        $memberlist = Db::name('member')->field('id')->where('aid',$aid)->where('jiedianpid',$mid)->select()->toArray();
        foreach($memberlist as $k=>$member){
            $memberlist[$k]['children'] = self::getdownxiaji($member['id'],$deeplevel,$leveldata,$xingji,$aid,$str);
//          $memberlist[$k]['downcount'] = count($memberlist[$k]['children']);
//          $memberlist[$k]['name'] = $member['nickname'] . "\r\n" . $leveldata[$member['levelid']]['name'].'(ID:'.$member['id'].' 下级:'.$memberlist[$k]['downcount'].'人)';
        
        }
//      if($this->deeplevel < $deeplevel) $this->deeplevel = $deeplevel;
        return $memberlist;
    }
    
    public $deeplevel = 0;
    
    public static function getdownxiaji22($mid,$aid){
        $downcount = 0;
        $memberlist = self::getdownxiajicount22($mid,$aid,0);
        $memberlist = self::array_flatten($memberlist);
        return count($memberlist);
    }
    public static function array_flatten($array){
        $result=array();
        array_walk_recursive($array,function($value)use(&$result){
            if($value)
            {
                array_push($result,$value);
            }
            
            });
            return $result;
        }
    public static function getdownxiajicount22($mid,$aid,$deeplevel,$downcount = [])
    {
        $downcount = [];
        if($deeplevel > 9) return json([]);
        $deeplevel++;
        $memberlist = Db::name('member')->field('id')->where('aid',$aid)->where('jiedianpid',$mid)->select()->toArray();
        foreach($memberlist as $k=>$member){
            $children =  self::getdownxiajicount22($member['id'],$aid,$deeplevel,$downcount);
            $memberlist[$k]['children'] = $children;
//          $memberlist[$k]['downcount'] = count($children);
        }
        return $memberlist;
            
    }
    /**
     * 得到云库存是否开启
     * */
     public static function getyunkucun($aid)
     {
         $returnsttaus = 0;
        $ykcrs = Db::name('yunkucun_sysset')->where('aid',$aid)->find();
        if(!empty($ykcrs))
        {
            if($ykcrs['status'] == 1)
            {
                $returnsttaus = 1;
            }
        }
        return $returnsttaus;
     }
    /**
     * 判断是否是云库存商品
     * */
     public static function isyunproduct($aid,$proid)
     {
        $ysp = Db::name('yunkucun_shop_product')->where('proid',$proid)->where('aid',$aid)->find();
        if(!empty($ysp))
        {
            return true;
        }else{
            return false;
        }
     }
     /**
      * 判断我的上级云库存够不够我下单
      * */
    public static function isshnagjiyunkuncun($aid,$proid,$ggid,$num,$pid,$kuncuntype=0)
    {
        $numArr = Db::name('yunkucun_users')->where('aid',$aid)->where('mid',$pid)->where('proid',$proid)->where('ggid',$ggid)->find();
        $product = Db::name('shop_product')->where('aid',$aid)->where('id',$proid)->find();
        $guige = Db::name('shop_guige')->where('aid',$aid)->where('id',$ggid)->find();
        if($kuncuntype == 1)
        {
            if($product['kuncun_type'] == 1)
            {
                return ['status'=>1,'msg'=>'成功!'];
            }else{
              if(empty($numArr))
                {
                    return ['status'=>0,'msg'=>'商品:'.$product['name'].',规格:'.$guige['name'].',上级云库存不足,请联系代理补货后下单'];
                }else{
                    if($numArr['num'] < $num)
                    {
                         return ['status'=>0,'msg'=>'商品:'.$product['name'].',规格:'.$guige['name'].',上级云库存不足,请联系代理补货后下单'];
                    }else{
                         return ['status'=>1,'msg'=>'成功!'];
                    }
                }   
            }
        }else{
           if(empty($numArr))
            {
                return ['status'=>0,'msg'=>'商品:'.$product['name'].',规格:'.$guige['name'].',上级云库存不足,请联系代理补货后下单'];
            }else{
                if($numArr['num'] < $num)
                {
                     return ['status'=>0,'msg'=>'商品:'.$product['name'].',规格:'.$guige['name'].',上级云库存不足,请联系代理补货后下单'];
                }else{
                     return ['status'=>1,'msg'=>'成功!'];
                }
            } 
        }
    }
    //修改我的云库存
    public static function yunkucunlog($aid,$mid,$proid,$ggid,$num,$ordergoods,$remark='')
    {
        $ysp = Db::name('yunkucun_shop_product')->where('proid',$proid)->where('aid',$aid)->where('bid',$ordergoods['bid'])->find();
        if(!empty($ysp)){
            if($num==0) return ;
            $numArr = Db::name('yunkucun_users')->where('aid',$aid)->where('mid',$mid)->where('proid',$proid)->where('ggid',$ggid)->find();
            $prodata = Db::name('shop_product')->where('id',$proid)->where('aid',$aid)->field('kuncun_type')->find();
            $kuncun_type = 0;
            if(!empty($prodata) && $prodata['kuncun_type'] ==1)
            {
                $kuncun_type = 1;
            }
            if(!empty($numArr))
            {
                if($num <  0)
                {
                    //减去5件 我就3件
                    if(abs($num) > $numArr['num'])
                    {
                        return ['status'=>0,'msg'=>'上级云库存不足'];
                    }else{
                       $after=$numArr['num']+$num;
                    }
                }else{
                    $after=$numArr['num']+$num;
                }
                Db::name('yunkucun_users')->where('aid',$aid)->where('id',$numArr['id'])->update(['num'=>$after,'kuncun_type'=>$kuncun_type]);
            }else{
                if($num <= 0)
                {
                    return ['status'=>0,'msg'=>'上级云库存不足'];
                }else{
                    $after = $num;
                    $insertgoods  =[
                        'aid'=>$ordergoods['aid'],
                        'bid'=>$ordergoods['bid'],
                        'mid'=>$mid,
                        'proid'=>$ordergoods['proid'],
                        'ggid'=>$ordergoods['ggid'],
                        'ggname'=>$ordergoods['ggname'],
                        'proname'=>$ordergoods['name'],
                        'pic'=>$ordergoods['pic'],
                        'procode'=>$ordergoods['procode'],
                        'num'=>$ordergoods['num'],
                        'updatetime'=>time(),
                        'kuncun_type'=>$kuncun_type,
                        ];
                     Db::name('yunkucun_users')->insert($insertgoods);
                }
            }
            $data = [];
            $data['aid'] = $aid;
            $data['bid'] = $ordergoods['bid'];
            $data['mid'] = $mid;
            $data['proid'] = $ordergoods['proid'];
            $data['proname'] = $ordergoods['name'];
            $data['ggid'] = $ordergoods['ggid'];
            $data['ggname'] = $ordergoods['ggname'];
            $data['num'] = $num;
            $data['after'] = $after;
            $data['createtime'] = time();
            $data['remark'] = $remark;
            $data['kuncun_type'] = $kuncun_type;
            Db::name('yunkucun_memberlog')->insert($data);
            return ['status'=>1,'msg'=>''];
        }else{
            return ['status'=>0,'msg'=>'操作失败'];
        }
    }
        
//          var_dump($nahuoArr);die;
//  }
    public static function shangjiyunkucunnahuoxunhuan($aid,$path,$buydata,$curmemberlevel,$mid,$nahuoArr = [],$kuncuntype=0)
    {
        if($curmemberlevel['yun_nahuo'] == '0'){
            $nahuoid = 0;
            return ['nahuoid'=>$nahuoid,'nahuoArr'=>$nahuoArr];
        }
        $yun_nahuoArr = $curmemberlevel['yun_nahuo'];
        $yun_nahuoArr = explode(',',$yun_nahuoArr);
     
         foreach($path as $v)
        {
          //  var_dump($v);
            $curmember = Db::name('member')->where('aid',$aid)->where('id',$v)->find();
            $curmemberlevel =  Db::name('member_level')->where('aid',$aid)->where('id',$curmember['levelid'])->find();
           
            if(in_array($curmember['levelid'],$yun_nahuoArr) !== false)
            {
                $nahuoArr[] = $v;
                $bool = self::isshnagjiyunkuncun3($aid,$buydata,$v,$kuncuntype);
                if($bool)
                {
                    $nahuoid = $v;
                    return ['nahuoid'=>$nahuoid,'nahuoArr'=>$nahuoArr];
                }else{
                    if($curmember['pid'] != 0)
                    {
                         $curmemberpath = explode(',',$curmember['path']);
                            $curmemberpath = array_reverse($curmemberpath);
                         return self::shangjiyunkucunnahuoxunhuan($aid,$curmemberpath,$buydata,$curmemberlevel,$curmember['id'],$nahuoArr);
                    }else{
                        $nahuoid = 0;
                        return ['nahuoid'=>0,'nahuoArr'=>$nahuoArr];
                    }
                }
            }
        }
         return ['nahuoid'=>0,'nahuoArr'=>$nahuoArr];
    }
    /**
     * 走货开启
     * */
     
     public static function isshnagjiyunkuncun2($aid,$path,$buydata,$curmemberlevel,$mid,$nahuoArr=[],$kuncuntype=0)
     {
        $nahuoid = 0;
        $pathArr = explode(',',$path); //7,8,9
        $pathArr = array_reverse($pathArr);
        if($curmemberlevel['yun_nahuo'] == '0'){
            $nahuoid = 0;
        }else{
           $nahuoArrzong = self::shangjiyunkucunnahuoxunhuan($aid,$pathArr,$buydata,$curmemberlevel,$mid, [],$kuncuntype);
        }
        $nahuoid = $nahuoArrzong['nahuoid'];
    //  var_dump($nahuoid);die;
        $nahuoArr = $nahuoArrzong['nahuoArr'];
       $moneyArr2 = self::isshnagjiyunkuncun4($nahuoid,$nahuoArr,$buydata,$aid,$mid,$kuncuntype);
       return ['nahuoid'=>$nahuoid,'nahuoArr'=>$moneyArr2];
     }
     
     /**
      * 走货开启3循环计算每人佣金
      * */
      public static function isshnagjiyunkuncun4($nahuoid,$nahuoArr,$buydata,$aid,$mid,$kuncuntype=0)
      {
           array_unshift($nahuoArr,$mid);
           $moneyArr = $moneyArr2 = [];
           foreach ($nahuoArr as $k=>$v)
           {
              $moneyArr[$k] = self::isshnagjiyunkuncun5($buydata,$v,$aid,$kuncuntype);
           }
           foreach($nahuoArr as $k=>$v)
           {
              $num = $k+1;
              if($nahuoid == $v)
              {
                  $moneyArr2[$nahuoArr[$k]] = $moneyArr[$k-1];
              }else{
                  $moneyArr2[$nahuoArr[$num]] = $moneyArr[$k] - $moneyArr[$num];
              }
            }
            return $moneyArr2;
          
      }
      /**
       * 走货开启4循环计算每人佣金2
       * */
       public static function isshnagjiyunkuncun5($buydata,$nahuoidv,$aid,$kuncuntype=0)
       {
             $count1 = 0;
             foreach ($buydata as $data)
             {
                if($data['prodata']){
                    $prodata = explode('-',$data['prodata']);
                }else{
                    return $this->json(['status'=>0,'msg'=>'产品数据错误']);
                }
                foreach($prodata as $key=>$pro)
                {
                    $sdata = explode(',',$pro); //32,52,2/测试规格56
                    $product  = Db::name('shop_product')->where('aid',$aid)->where('id',$sdata[0])->find();
                    $guige = Db::name('shop_guige')->where('aid',$aid)->where('id',$sdata[1])->find();
                    if(!empty($guige['lvprice_data']))
                    {
                         $nahuomember = Db::name('member')->where('aid',$aid)->where('id',$nahuoidv)->find();
                         $guigeArr = json_decode($guige['lvprice_data'],1);
                         if($kuncuntype == 1 )
                         {
                             if($product['kuncun_type'] == 1)
                            {
                                $count1 =  $count1+ $guigeArr[$nahuomember['levelid']] *$sdata[2];
                            }
                         }else{
                            $count1 =  $count1+ $guigeArr[$nahuomember['levelid']] *$sdata[2];
                         }
                    }else{
                        if($kuncuntype == 1 )
                        {
                            if($product['kuncun_type'] == 1)
                            {
                                $count1 =  $count1+ $guige['sell_price']*$sdata[2];
                            }
                            
                        }else{
                            $count1 =  $count1+ $guige['sell_price']*$sdata[2];
                        }
                    }
                }
             }
             return $count1;
       }
      
     /**
      * 走货开启2循环
      * */
     public static function isshnagjiyunkuncun3($aid,$buydata,$v,$kuncuntype=0)
     {
         //var_dump('判断人'.$v);
         
         $returnbool = false;
         foreach ($buydata as $data)
         {
            if($data['prodata']){
                $prodata = explode('-',$data['prodata']);
            }else{
                return $this->json(['status'=>0,'msg'=>'产品数据错误']);
            }
            foreach($prodata as $key=>$pro)
            {
                $sdata = explode(',',$pro); 
                $yunbool = \app\common\Member::isshnagjiyunkuncun($aid,$sdata[0],$sdata[1],$sdata[2],$v,$kuncuntype);
                //var_dump('当前商品'.$sdata[0]);
                //var_dump($yunbool);
                $productgood = Db::name('shop_product')->where('id',$sdata[0])->find();
                if($productgood['kuncun_type'] != 1)
{
                if($yunbool['status'] == 1)
                {
                    $returnbool=true;
                }else{
                    $returnbool = false;
                    break;
                }
            }
            }
         }
         return $returnbool;
     }
     /**
      * 扣组合库存
      * */
      public static function kouzuhekuucn($product,$num,$aid)
      {
        if(!empty($product['dddata']))
        {
            $dddata_new = [];
            $dddata = json_decode($product['dddata'],1);
            foreach ($dddata as $kgoods=>$vgoods)
            {
                $num = $vgoods['num']*$num;
                Db::name('shop_guige')->where('aid',$aid)->where('id',$vgoods['ggid'])->update(['stock'=>Db::raw("stock-$num"),'sales'=>Db::raw("sales+$num")]);
                Db::name('shop_product')->where('aid',$aid)->where('id',$kgoods)->update(['stock'=>Db::raw("stock-$num"),'sales'=>Db::raw("sales+$num")]);
            }
        }
      }
      
      /**
       * 扣组合上级库存2
       * */
      public function kouzuhekuucn2($product,$num,$aid,$mid)
      {
        if(!empty($product['dddata']))
        {
            $dddata = json_decode($product['dddata'],1);
            foreach ($dddata as $kgoods=>$vgoods)
            {
                $productgood = Db::name('shop_product')->where('id',$kgoods)->find();
                $gg = Db::name('shop_guige')->where('id',$vgoods['ggid'])->find();
                $ordergoods2 =[
                    'aid'=>$aid,
                    'bid'=>0,
                    'mid'=>$mid,
                    'proid'=>$kgoods,
                    'ggid'=>$vgoods['ggid'],
                    'ggname'=>$gg['name'],
                    'name'=>$productgood['name'],
                    'pic'=>$productgood['pic'],
                    'procode'=>'',
                    'num'=>$vgoods['num']*$num
                    ];
                    $num1 = $vgoods['num']*$num;
                \app\common\Member::yunkucunlog($aid,$mid,$kgoods,$vgoods['ggid'],'-'.$num1,$ordergoods2,'下级下单组合商品,扣除');
            }
        }
      }
      
      /**
       * 云库存给培养奖励
       * */
      public function givepeiyang($aid,$ordertotalprice,$mid,$orderid,$ylevelid,$b=0)
      {
           $curmember = Db::name('member')->where('aid',$aid)->where('id',$mid)->find();
           $order = Db::name('shop_order')->where('id',$orderid)->find();
           if($curmember['suo1'] == 0)
           {
               $suo1 = 0;
           }else{
               $suo1 = $curmember['suo1'];
           }
           if($curmember['suo2'] == 0)
           {
               $suo2 = 0;
           }else{
               $suo2 = $curmember['suo2'];
           }
           $curlevelid = $curmember['levelid'];
           $nowlv = Db::name('member_level')->where('aid',$aid)->where('id',$ylevelid)->find();
           $giveid1 = 0;
           $giveid2 = 0;
           $path = $curmember['path'];
           $pathArr = explode(',',$path);
           $pathArr = array_reverse($pathArr);
           foreach ($pathArr as $v)
           {
                $parentmember = Db::name('member')->where('aid',$aid)->where('id',$v)->find();
                if($suo1 == 0){
                    if($nowlv['peiyang_levelid1'] >0){
                        $jiangli_levelArr1= explode(',',$nowlv['peiyang_levelid1']);
                        if(in_array($parentmember['levelid'],$jiangli_levelArr1) !== false && $giveid1 == 0)
                        {
                            $giveid1 = $v;
                             if($giveid1 && $giveid2){
                                break;
                            }
                        }
                    }
                }
                if($suo2 == 0){
                    if($nowlv['peiyang_levelid2'] >0)
                    {
                         $jiangli_levelArr2= explode(',',$nowlv['peiyang_levelid2']);
                         if(in_array($parentmember['levelid'],$jiangli_levelArr2) !== false  && $giveid2 == 0)
                        {
                            $giveid2 = $v;
                             if($giveid1 && $giveid2){
                                break;
                            }
                        }
                    }
                }
           }
           if($suo1 !=0)
           {
                $jiangli1 = $ordertotalprice*$curmember['suo1jiang']*0.01;
                if($jiangli1 >0 )
                {
                   $str = '';
                   if($b == 1)
                   {
                       $str = '补货-';
                   }
                    Db::name('member_commission_record')->insert(['jl_type'=>1,'aid'=>$aid,'mid'=>$suo1,'frommid'=>$mid,'orderid'=>$orderid,'ogid'=>0,'type'=>'shop','commission'=>$jiangli1,'remark'=>'用户'.$mid.'-'.$str.'云库存培养奖励1,订单编号:'.$order['ordernum'],'createtime'=>time()]);
                }
           }elseif($giveid1 != 0){
               $jiangli1 = $ordertotalprice*$nowlv['peiyang_jiangli1']*0.01;
               if($giveid1 >0 && $jiangli1 >0 )
               {
                   $str = '';
                   if($b == 1)
                   {
                       $str = '补货-';
                   }
                    Db::name('member_commission_record')->insert(['jl_type'=>1,'aid'=>$aid,'mid'=>$giveid1,'frommid'=>$mid,'orderid'=>$orderid,'ogid'=>0,'type'=>'shop','commission'=>$jiangli1,'remark'=>'用户'.$mid.'-'.$str.'云库存培养奖励1,订单编号:'.$order['ordernum'],'createtime'=>time()]);
               }
           }
          if($suo2 !=0)
           {
                $jiangli2 = $ordertotalprice*$curmember['suo2jiang']*0.01;
                if( $jiangli2 >0 )
                {
                    $str = '';
                   if($b == 1)
                   {
                       $str = '补货-';
                   }
                  Db::name('member_commission_record')->insert(['jl_type'=>1,'aid'=>$aid,'mid'=>$suo2,'frommid'=>$mid,'orderid'=>$orderid,'ogid'=>0,'type'=>'shop','commission'=>$jiangli2,'remark'=>'用户'.$mid.'-'.$str.'云库存培养奖励2,订单编号:'.$order['ordernum'],'createtime'=>time()]);
                }
           }elseif($giveid2 != 0){
               $jiangli2 = $ordertotalprice*$nowlv['peiyang_jiangli2']*0.01;
               if($giveid2 >0 && $jiangli2 >0 )
               {
                   $str = '';
                   if($b == 1)
                   {
                       $str = '补货-';
                   }
                  Db::name('member_commission_record')->insert(['jl_type'=>1,'aid'=>$aid,'mid'=>$giveid2,'frommid'=>$mid,'orderid'=>$orderid,'ogid'=>0,'type'=>'shop','commission'=>$jiangli2,'remark'=>'用户'.$mid.'-'.$str.'云库存培养奖励2,订单编号:'.$order['ordernum'],'createtime'=>time()]);
               }
          }

      }
      
      public static function givepeiyang2($aid,$ordertotalprice,$mid,$orderid,$ylevelid,$b=0)
      {
           $giveid1 = 0;
           $giveid2 = 0;
           $jiangli1 = 0;
           $jiangli2 = 0;
           $curmember = Db::name('member')->where('aid',$aid)->where('id',$mid)->find();
           if($curmember['suo1'] == 0)
           {
               $suo1 = 0;
           }else{
               $suo1 = $curmember['suo1'];
           }
           if($curmember['suo2'] == 0)
           {
               $suo2 = 0;
           }else{
               $suo2 = $curmember['suo2'];
           }
           $curlevelid = $curmember['levelid'];
           $nowlv = Db::name('member_level')->where('aid',$aid)->where('id',$ylevelid)->find();
           $path = $curmember['path'];
           $pathArr = explode(',',$path);
           $pathArr = array_reverse($pathArr);
           foreach ($pathArr as $v)
           {
                $parentmember = Db::name('member')->where('aid',$aid)->where('id',$v)->find();
                 if($suo1 == 0){
                    if($nowlv['peiyang_levelid1'] >0){
                        $jiangli_levelArr1= explode(',',$nowlv['peiyang_levelid1']);
                        if(in_array($parentmember['levelid'],$jiangli_levelArr1) !== false && $giveid1 == 0)
                        {
                            $giveid1 = $v;
                            if($giveid1 && $giveid2){
                                break;
                            }
                            
                        }
                    }
                 }
                 if($suo2 == 0){
                    if($nowlv['peiyang_levelid2'] >0)
                    {
                         $jiangli_levelArr2= explode(',',$nowlv['peiyang_levelid2']);
                         if(in_array($parentmember['levelid'],$jiangli_levelArr2) !== false  && $giveid2 == 0)
                        {
                            $giveid2 = $v;
                           if($giveid1 && $giveid2){
                                break;
                            }
                        }
                    }
                  }
           }
           if($suo1 != 0){
               $jiangli1 = $ordertotalprice*$curmember['suo1jiang']*0.01;
           }elseif($giveid1 != 0){
               $jiangli1 = $ordertotalprice*$nowlv['peiyang_jiangli1']*0.01;
          }
          if($suo2 != 0){
                $jiangli2 = $ordertotalprice*$curmember['suo2jiang']*0.01;
          }elseif($giveid2 != 0){
               $jiangli2 = $ordertotalprice*$nowlv['peiyang_jiangli2']*0.01;
          }
           return $jiangli1+$jiangli2;
      }
      
//    获取最近上级等级允许分销的id

     public static function getparentid($aid,$mid)
     {
        $curmember = Db::name('member')->where('aid',$aid)->where('id',$mid)->find();
        $pathstr = $curmember['path'];
        $pathArr = explode(',',$pathstr);
        $pathArr = array_reverse($pathArr);
        foreach ($pathArr as $v)
        {
             $parentmember = Db::name('member')->where('aid',$aid)->where('id',$v)->find();
             $parentmember_level = Db::name('member_level')->where('aid',$aid)->where('id',$parentmember['levelid'])->find();
             if($parentmember_level['can_agent'] == 0)
             {
                 if($parentmember_level['fxzhuli'] ==0)
                 {
                     return [];
                 }
             }else{
                 return $parentmember;
             }
        }
     }
     
     /**
      * 直推奖抽佣
      * */
      public static function getzhituichouyong($aid,$mid,$yongjin,$orderid,$ogid,$ordernum)
      {
         //  \think\facade\Log::write('【直推奖抽佣】开始: aid='.$aid.', mid='.$mid.', yongjin='.$yongjin, 'info');
           $jaingli1 = 0;
           $jaingli2 = 0;
           $curmember = Db::name('member')->where('aid',$aid)->where('id',$mid)->find();
         //  \think\facade\Log::write('【直推奖抽佣】当前用户: '.json_encode($curmember, JSON_UNESCAPED_UNICODE), 'info');
           
           $curmember_level = Db::name('member_level')->where('aid',$aid)->where('id',$curmember['levelid'])->find();
        //   \think\facade\Log::write('【直推奖抽佣】当前用户等级: '.json_encode($curmember_level, JSON_UNESCAPED_UNICODE), 'info');
           
           $parentmember = Db::name('member')->where('aid',$aid)->where('id',$curmember['pid'])->find();
        //   \think\facade\Log::write('【直推奖抽佣】上级用户: '.json_encode($parentmember, JSON_UNESCAPED_UNICODE), 'info');
           
         //  \think\facade\Log::write('【直推奖抽佣】条件判断: zt_sy_yong1='.($curmember_level['zt_sy_yong1'] ?? '未设置').' > 0 && pid='.$curmember['pid'].' > 0', 'info');
           
        //   $parentmember_level = Db::name('member_level')->where('aid',$aid)->where('id',$parentmember['levelid'])->find();
           if($curmember_level['zt_sy_yong1'] >0 && $curmember['pid'] >0)
           {
          //     \think\facade\Log::write('【直推奖抽佣】条件1匹配: zt_sy_yong1='.$curmember_level['zt_sy_yong1'], 'info');
               if($curmember_level['zt_sy_levelid1'])
               {
                    $jiangli_levelArr1 = explode(',',$curmember_level['zt_sy_levelid1']);
             //       \think\facade\Log::write('【直推奖抽佣】等级限制: '.implode(',', $jiangli_levelArr1).' 上级等级: '.$parentmember['levelid'], 'info');
                    if(in_array($parentmember['levelid'],$jiangli_levelArr1))
                    {
                        //计算奖励
                        $jaingli1 = $yongjin * $curmember_level['zt_sy_yong1'] * 0.01;
                        //\think\facade\Log::write('【直推奖抽佣】等级匹配,计算奖励1: '.$jaingli1, 'info');
                    } else {
                       // \think\facade\Log::write('【直推奖抽佣】等级不匹配，不计算奖励', 'info');
                    }
               }else{
                   //计算奖励
                    $jaingli1 = $yongjin * $curmember_level['zt_sy_yong1'] * 0.01;
                    //\think\facade\Log::write('【直推奖抽佣】无等级限制,计算奖励1: '.$jaingli1, 'info');
               }
               if($orderid == 0)
               {
                    self::addcommission($aid,$parentmember['id'],$mid,$jaingli1,'下级用户:'.$mid.',得到直推佣金奖励抽佣1');
                   // \think\facade\Log::write('【直推奖抽佣】添加佣金1完成', 'info');
               }else{
                   Db::name('member_commission_record')->insert(['aid'=>$aid,'mid'=>$parentmember['id'],'frommid'=>$mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$jaingli1,'score'=>0,'remark'=>'下级用户:'.$mid.',得到直推佣金奖励抽佣1,订单编号:'.$ordernum,'createtime'=>time()]);
                   //\think\facade\Log::write('【直推奖抽佣】记录佣金1完成', 'info');
                }
           } else {
                if ($curmember_level['zt_sy_yong1'] <= 0) {
                   // \think\facade\Log::write('【直推奖抽佣】条件1不满足: zt_sy_yong1 不大于0', 'info');
                }
                if ($curmember['pid'] <= 0) {
                   // \think\facade\Log::write('【直推奖抽佣】条件1不满足: 用户无上级', 'info');
                }
           }
           
           if($curmember_level['zt_sy_yong2'] >0 && $curmember['pid'] >0)
           {
               if($curmember_level['zt_sy_levelid2'])
               {
                    $jiangli_levelArr2 = explode(',',$curmember_level['zt_sy_levelid2']);
                    if(in_array($parentmember['levelid'],$jiangli_levelArr2) )
                    {
                        //计算奖励
                        $jaingli2 = $yongjin*$curmember_level['zt_sy_yong2']*0.01;
                    }
               }else{
                   $jaingli2 = $yongjin*$curmember_level['zt_sy_yong2']*0.01;
               }
                if($orderid == 0)
               {
                    self::addcommission($aid,$parentmember['id'],$mid,$jaingli2,'下级用户:'.$mid.',得到直推佣金奖励抽佣2');
               }else{
                   Db::name('member_commission_record')->insert(['aid'=>$aid,'mid'=>$parentmember['id'],'frommid'=>$mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$jaingli2,'score'=>0,'remark'=>'下级用户:'.$mid.',得到直推佣金奖励抽佣2,订单编号:'.$ordernum,'createtime'=>time()]);
               }
           }
           return true;
      }
      
    /**
   * 间推抽佣
   * */
     public static function getjiantuichouyong($aid,$mid,$yongjin,$orderid,$ogid,$ordernum)
     {
         //\think\facade\Log::write('【间推抽佣】开始: aid='.$aid.', mid='.$mid.', yongjin='.$yongjin, 'info');
         $jaingli1 = 0;
         $curmember = Db::name('member')->where('aid',$aid)->where('id',$mid)->find();
         $curmember_level = Db::name('member_level')->where('aid',$aid)->where('id',$curmember['levelid'])->find();
         
        // \think\facade\Log::write('【间推抽佣】用户等级配置: jt_sy_yong='.($curmember_level['jt_sy_yong'] ?? '未设置').', jt_sy_ceng='.($curmember_level['jt_sy_ceng'] ?? '未设置').', jt_sy_levelid='.($curmember_level['jt_sy_levelid'] ?? '未设置'), 'info');
         
         if($curmember_level['jt_sy_yong'] >0  && $curmember['pid'] >0)
         {
           //  \think\facade\Log::write('【间推抽佣】条件匹配: jt_sy_yong='.$curmember_level['jt_sy_yong'].' jt_sy_ceng='.$curmember_level['jt_sy_ceng'], 'info');
             $jiangliArr = [];
             $pathstr = $curmember['path'];
             $pathArr = explode(',',$pathstr);
             $pathArr = array_reverse($pathArr);
             unset($pathArr[0]);
             $countarr = count($pathArr);
            // \think\facade\Log::write('【间推抽佣】路径: '.$pathstr.', 上级数量: '.$countarr, 'info');
             
             if($curmember_level['jt_sy_ceng'] && $curmember_level['jt_sy_ceng']  < $countarr)
             {
                 $length = $countarr -$curmember_level['jt_sy_ceng'];
                 array_splice($pathArr,$curmember_level['jt_sy_ceng'],$length);
                // \think\facade\Log::write('【间推抽佣】应用层级限制,截取后上级数量: '.count($pathArr), 'info');
             }
             foreach($pathArr as $v)
             {
                 $parentmember = Db::name('member')->where('aid',$aid)->where('id',$v)->find();
               //  \think\facade\Log::write('【间推抽佣】处理上级: id='.$v.', levelid='.$parentmember['levelid'], 'info');
                 
                 if($curmember_level['jt_sy_levelid'])
                 {
                     $jiangli_levelArr1 = explode(',',$curmember_level['jt_sy_levelid']);
                     if(in_array($parentmember['levelid'],$jiangli_levelArr1))
                     {
                         //计算奖励
                          $jaingli1 = $yongjin * $curmember_level['jt_sy_yong'] * 0.01;
                         // \think\facade\Log::write('【间推抽佣】等级匹配,计算奖励: '.$jaingli1, 'info');
                     }
                 }else{
                     //计算奖励
                     $jaingli1 = $yongjin * $curmember_level['jt_sy_yong'] * 0.01;
                    // \think\facade\Log::write('【间推抽佣】无等级限制,计算奖励: '.$jaingli1, 'info');
                 }
                 if($jaingli1 >0)
                 {
                    if($orderid == 0)
                    {
                            self::addcommission($aid,$v,$mid,$jaingli1,'下级用户:'.$mid.',得到佣金奖励,间接抽佣');
                           // \think\facade\Log::write('【间推抽佣】添加佣金完成: 上级id='.$v.', 金额='.$jaingli1, 'info');
                    }else{
                              Db::name('member_commission_record')->insert(['aid'=>$aid,'mid'=>$v,'frommid'=>$mid,'orderid'=>$orderid,'ogid'=>$ogid,'type'=>'shop','commission'=>$jaingli1,'score'=>0,'remark'=>'用户:'.$mid.'得到佣金奖励,间接抽佣,订单编号:'.$ordernum,'createtime'=>time()]);
                            //  \think\facade\Log::write('【间推抽佣】记录佣金完成: 上级id='.$v.', 金额='.$jaingli1, 'info');
                    }
                 }
             }
         }
        // \think\facade\Log::write('【间推抽佣】结束', 'info');
          return true;
     }
     public static function getshangji($aid,$mid,$num,$num1=1)
     {
       //  var_dump('第'.$num.'轮');var_dump($num1);
         $curmember =  Db::name('member')->where('aid',$aid)->where('id',$mid)->field('id,gongpaipid')->find();
         $parentid = $curmember['gongpaipid'];
         if($num1 < $num)
         {
             $num1 = $num1+1;
             $parentid = self::getshangji($aid,$curmember['gongpaipid'],$num,$num1);
             return $parentid;
         }else{
           //  var_dump(444);
             return $parentid;
         }
         
         
     }
     public static function setgongpai($aid,$mid)
     {
         $set = Db::name('admin_set')->where('aid',$aid)->find();
       //  var_dump(1);
         if($set['gongpaizhi_shi'] > 0 && $set['gongpaizhi_ren'] >0 && $set['gongpaizhi_xiaofei'] >0 && $set['gongpaizhi_ren_lun']>0)
         {
           //  var_dump(2);
             $jaingli = 0;
             $manzucount = 0;
             $curmember = Db::name('member')->where('aid',$aid)->where('id',$mid)->find();
             if($curmember['pid'] == 0)
             {
                 return;
             }
            //  var_dump('判断的上级'.$curmember['pid']);
             $parentmember = Db::name('member')->where('aid',$aid)->where('id',$curmember['pid'])->find();
            //  var_dump($parentmember['gongpainum']);
             if($parentmember['gongpainum'] == 1)
             {
                //  var_dump('进入大于第2轮的用户id'.$parentmember['id']);
                 return;
             }
             //第一轮 
             $allxiaji = Db::name('member')->where('aid',$aid)->where('pid',$curmember['pid'])->field('id,isxuni')->select()->toArray();//我上级的所有下级
           //  var_dump('我有多少下级'.count($allxiaji));die;
             foreach ($allxiaji as $v)
             {
                 if($manzucount >= $set['gongpaizhi_ren'])
                 {
                     break;
                 }else{
                     $xiajixiaofei = Db::name('shop_order')->where('aid',$aid)->where('mid',$v['id'])->where('status','in','1,2,3')->sum('totalprice');
                     if($xiajixiaofei >=  $set['gongpaizhi_xiaofei'])
                     {
                         $manzucount = $manzucount+1;
                     }
                   //  elseif($v['isxuni'] ==1)
                   //  {
                   //      $manzucount = $manzucount+1;
                   //  }
                 }
             }
           //  var_dump('满足的下级数'.$manzucount);
             if($manzucount >= $set['gongpaizhi_ren'])
             {
                 $gongpaizhi_lunshu = json_decode($set['gongpaizhi_lunshu'],1);
                 if($gongpaizhi_lunshu['gongpaizhi_lunshu'][1] ==1)
                 {
                     //判断消费 满足继续 不满足 返回
                     $gongpaizhi_jiangli_lunshu = json_decode($set['gongpaizhi_jiangli_lunshu'],1);
                     if($gongpaizhi_jiangli_lunshu[1]['xiaofei'] >0)
                     {
                        $mytotalprice = Db::name('shop_order')->where('aid',$aid)->where('mid',$mid)->where('status','in','1,2,3')->sum('totalprice');
                        if($mytotalprice < $gongpaizhi_jiangli_lunshu[1]['xiaofei'])
                        {
                            return;
                        }
                     }
                     
                     
                 }
               //  var_dump('已达到设置人数');
               //  var_dump('要出去的用户id'.$curmember['pid']);
                 
               //出去 //设置公排状态
               $gongpaipid = self::getgongpaipid($aid,$curmember['pid'],$set);
               //var_dump('要出去用户的上级'.$gongpaipid);die;
               //修改公排上级
               Db::name('member')->where('aid', $aid)->where('id', $curmember['pid'])->update(['gongpaipid'=>$gongpaipid,'gongpainum'=>1,'gongpaitime'=>time()]);
               //用户进入第一轮,然后查看上级是否够了 升级第二轮
               if($gongpaipid > 0 ){
                    //第2轮
                    $gongpaipid2 = self::getdayu2gongpai($aid,$parentmember['id'],$set);
                    // var_dump($gongpaipid2);
                    if($gongpaipid2 > 0)
                    {
                        // //进入第三轮
                        $gongpaipid3 = self::getdayu2gongpai($aid,$gongpaipid2,$set,3);
                        if($gongpaipid3 >0)
                        {
                           $gongpaipid4 = self::getdayu2gongpai($aid,$gongpaipid3,$set,4); 
                           if($gongpaipid4 >0)
                           {
                               $gongpaipid5 = self::getdayu2gongpai($aid,$gongpaipid4,$set,5); 
                               if($gongpaipid5 >0)
                               {
                                   $gongpaipid6 = self::getdayu2gongpai($aid,$gongpaipid5,$set,6); 
                                   if($gongpaipid6 >0)
                                   {
                                       $gongpaipid7 = self::getdayu2gongpai($aid,$gongpaipid6,$set,7);
                                       if($gongpaipid7 >0)
                                       {
                                           $gongpaipid8 = self::getdayu2gongpai($aid,$gongpaipid6,$set,8);
                                       }
                                   }
                               }
                           }
                        }
                     }
               }
               
             }
         }
         return true;
     }
     
     public static function getdayu2gongpai($aid,$mid,$set,$lun=2)
     {
       //  var_dump('第几轮'.$lun);
       //  var_dump('判断用户id'.$mid);
         if($lun == 2)
         {
             $gongpaipid = 'gongpaipid2';
             $gongpainum = 'gongpainum2';
             $gongpaitime = 'gongpaitime2';
             $curmemberpid = 'gongpaipid';
         }elseif($lun == 3)
         {
             $gongpaipid = 'gongpaipid3';
             $gongpainum = 'gongpainum3';
             $gongpaitime = 'gongpaitime3';
             $curmemberpid = 'gongpaipid2';
         }elseif($lun == 4)
         {
             $gongpaipid = 'gongpaipid4';
             $gongpainum = 'gongpainum4';
             $gongpaitime = 'gongpaitime4';
              $curmemberpid = 'gongpaipid3';
         }elseif($lun == 5)
         {
             $gongpaipid = 'gongpaipid5';
             $gongpainum = 'gongpainum5';
             $gongpaitime = 'gongpaitime5';
             $curmemberpid = 'gongpaipid4';
         }elseif($lun == 6)
         {
             $gongpaipid = 'gongpaipid6';
             $gongpainum = 'gongpainum6';
             $gongpaitime = 'gongpaitime6';
              $curmemberpid = 'gongpaipid5';
         }elseif($lun == 7)
         {
             $gongpaipid = 'gongpaipid7';
             $gongpainum = 'gongpainum7';
             $gongpaitime = 'gongpaitime7';
             $curmemberpid = 'gongpaipid6';
         }elseif($lun == 8)
         {
             $gongpaipid = 'gongpaipid8';
             $gongpainum = 'gongpainum8';
             $gongpaitime = 'gongpaitime8';
             $curmemberpid = 'gongpaipid7';
         }
         
         //出来的用户
       //  $parentmember['id'] gongpaipid
        $curmember = Db::name('member')->where('aid',$aid)->where('id',$mid)->find();
       // var_dump('要升级的'.$curmember[$curmemberpid]);
        if($curmember[$curmemberpid] > 0)
        {
            $allxiajicount = 0;
            $allxiajicount = Db::name('member')->where('aid',$aid)->where($curmemberpid,$curmember[$curmemberpid])->count();
           // var_dump($allxiajicount);
            $allxiajiArr = Db::name('member')->where('aid',$aid)->where($curmemberpid,$curmember[$curmemberpid])->field('*')->select()->toArray();
            foreach($allxiajiArr as $v)
            {
                $allxiaji1 = 0;
                $allxiaji1 = Db::name('member')->where('aid',$aid)->where($curmemberpid,$v['id'])->count();
               // var_dump($allxiaji1);
                $allxiajicount = $allxiajicount+$allxiaji1;
            }
            $allxiaji = $allxiajicount;
           // var_dump($allxiaji);
           // var_dump($curmember['gongpainum']);die;
            
             $gongpaizhi_ren = $set['gongpaizhi_ren'];
           //  var_dump($allxiajicount);
            if($allxiaji >= $gongpaizhi_ren)
            {
                $gongpaizhi_lunshu = json_decode($set['gongpaizhi_lunshu'],1);
                 if($gongpaizhi_lunshu['gongpaizhi_lunshu'][$lun] ==1)
                 {
                     //判断消费 满足继续 不满足 返回
                     $gongpaizhi_jiangli_lunshu = json_decode($set['gongpaizhi_jiangli_lunshu'],1);
                     if($gongpaizhi_jiangli_lunshu[$lun]['xiaofei'] >0)
                     {
                        $mytotalprice = Db::name('shop_order')->where('aid',$aid)->where('mid',$mid)->where('status','in','1,2,3')->sum('totalprice');
                        if($mytotalprice < $gongpaizhi_jiangli_lunshu[$lun]['xiaofei'])
                        {
                            return 0;
                        }
                     }
                     
                     
                 }
               // var_dump(77777777);
                //大于 进入下一轮
               // 获取第2轮父级id
               $gongpaipid1 = self::getgongpaipid($aid,$curmember[$curmemberpid],$set,$lun);
               //var_dump('进入第2轮的父id'.$gongpaipid1);
               Db::name('member')->where('aid', $aid)->where('id', $curmember[$curmemberpid])->update([$gongpaipid=>$gongpaipid1,$gongpainum=>1,$gongpaitime=>time()]);
               //修改完成后
            //   获取当前轮数所有上级
                $parentArr = \app\common\Member::getdownjieidanparent2($aid,$curmember[$curmemberpid],$gongpaipid);
                $set = Db::name('admin_set')->where('aid',$aid)->find();
                $gongpaizhi_jiangli_lunshu = json_decode($set['gongpaizhi_jiangli_lunshu'],1);
                foreach($parentArr as $k=>$v)
                {
                    if($v)
                    {
                       if($k == 0)
                       {
                           //直推钱
                           \app\common\Member::addcommission($aid,$v,$curmember[$curmemberpid],$gongpaizhi_jiangli_lunshu[$lun]['zhi'],'用户id'.$curmember[$curmemberpid].',进入公排'.$lun.',直推拿钱'.$gongpaizhi_jiangli_lunshu[$lun]['zhi']);
                       }else{
                           //间推钱
                           \app\common\Member::addcommission($aid,$v,$curmember[$curmemberpid],$gongpaizhi_jiangli_lunshu[$lun]['jian'],'用户id'.$curmember[$curmemberpid].',进入公排'.$lun.',间推拿钱'.$gongpaizhi_jiangli_lunshu[$lun]['jian']);
                       }
                    }
                }
               return $gongpaipid1;
            }else{
               return 0; 
            }
        }else{
            return 0;  
        }
     }
     public static function getdownjieidanparent2($aid,$mid,$gongpaipid,$deeplevel=1,$leveldata=[],$zuidacount=99,$parent=[]){
        $curmember =  Db::name('member')->field('id,'.$gongpaipid)->where('aid',$aid)->where('id',$mid)->find(); 
        if($deeplevel > $zuidacount){
            return $parent;
        }else{
            $memberparent = Db::name('member')->field('id,nickname,headimg,pid,realname,tel,levelid')->where('aid',$aid)->where('id',$curmember[$gongpaipid])->find();
            $parent[]=$memberparent['id'];
            $deeplevel++;
            return self::getdownjieidanparent2($aid,$memberparent['id'],$gongpaipid,$deeplevel,$leveldata,$zuidacount,$parent);
        }
    }
    
     public static function getgongpaipid($aid,$mid,$set,$lun=1)
     {
       //  var_dump('2第几轮'.$lun);
         if($lun == 1)
         {
             $gongpainum = 'gongpainum';
             $gongpaipid = 'gongpaipid';
             $gongpaitime = 'gongpaitime';
             
         }elseif($lun == 2)
         {
             $gongpainum = 'gongpainum2';
             $gongpaipid = 'gongpaipid2';
             $gongpaitime = 'gongpaitime2';
         }elseif($lun == 3)
         {
             $gongpainum = 'gongpainum3';
             $gongpaipid = 'gongpaipid3';
             $gongpaitime = 'gongpaitime3';
         }elseif($lun == 4)
         {
             $gongpainum = 'gongpainum4';
             $gongpaipid = 'gongpaipid4';
             $gongpaitime = 'gongpaitime4';
         }elseif($lun == 5)
         {
             $gongpainum = 'gongpainum5';
             $gongpaipid = 'gongpaipid5';
             $gongpaitime = 'gongpaitime5';
         }elseif($lun == 6)
         {
             $gongpainum = 'gongpainum6';
             $gongpaipid = 'gongpaipid6';
             $gongpaitime = 'gongpaitime6';
         }elseif($lun == 7)
         {
             $gongpainum = 'gongpainum7';
             $gongpaipid = 'gongpaipid7';
             $gongpaitime = 'gongpaitime7';
         }elseif($lun == 8)
         {
             $gongpaipid = 'gongpaipid8';
             $gongpainum = 'gongpainum8';
             $gongpaitime = 'gongpaitime8';
         }
         $returnpid = $dingjipid = 0;
         $curmember = Db::name('member')->where('aid',$aid)->where('id',$mid)->find();
         if($curmember['pid'] == 0)
         {
            $dingjipid = $mid;
         }else{
            $pathstr = $curmember['path'];
            $pathArr = explode(',',$pathstr);
            if($lun == 1)
            {
                $dingjipid = $pathArr[count($pathArr)-1];
                $s = 0;
                if($curmember['isxuni'] == 1 && $lun == 1)
                {
                    // $dingjipid = $curmember['pid'];//主账号
                    $xuniArr = Db::name('member')->where('aid',$aid)->where('pid',$curmember['pid'])->where('isxuni',1)->field('id')->order('id asc')->find();
                    if($xuniArr['id'] != $curmember['id'])
                    {
                        $s =1;
                        $dingjipid = $xuniArr['id'];
                    }else{
        
                        $s =1;
                        $rrr = Db::name('member')->where('aid',$aid)->where('id',$dingjipid)->field('id,pid,gongpainum')->order('id asc')->find();
                        if($rrr['gongpainum'] != 1)
                        {
                            $xuniArr = Db::name('member')->where('aid',$aid)->where('pid',$rrr['pid'])->where('isxuni',1)->field('id')->order('id asc')->find();
                            if(!empty($xuniArr))
                            {
                                $dingjipid = $xuniArr['id'];
                            }
                        }else{
                            $dingjipid = $rrr['pid'];
                        }
                    }
                }
            }else{
                 $dingjipid = $pathArr[0];
            }

         }
         if($s == 1)
         {
             $memberlist = Db::name('member')->field('id,path')->where('aid',$aid)->where($gongpainum,1)->order($gongpaitime.' asc')->where('id',$dingjipid)->select()->toArray();
         }else{
             $memberlist = Db::name('member')->field('id,path')->where('aid',$aid)->where($gongpainum,1)->where($gongpaipid,0)->order($gongpaitime.' asc')->where('id',$dingjipid)->select()->toArray();
         }
        //  var_dump($memberlist);die;
         if(empty($memberlist))
         {
             if($lun == 1){
                 $membersdingji = Db::name('member')->field('id,path')->where('aid',$aid)->where('id',$dingjipid)->find();
                 if(empty($membersdingji) ||  $membersdingji['path'] == '')
                 {
                     $memberlist =  [];
                 }else{
                    $membersdingjipathArr = explode(',',$membersdingji['path']);
                    $membersdingjipathArr = array_reverse($membersdingjipathArr);
                    if(!empty($membersdingjipathArr))
                    {
                        foreach ($membersdingjipathArr as $dingk =>$dingv)
                        {
                                $memberlist = Db::name('member')->field('id,path')->where('aid',$aid)->where('id','<',$mid)->where($gongpainum,1)->order($gongpaitime.' asc')->where('find_in_set('.$dingv.',path)')->select()->toArray();
                                if(!empty($memberlist))
                                {
                                    break;
                                }
                        }
                    }
                 }
             }else{
                  $memberlist = Db::name('member')->field('id,path')->where('aid',$aid)->where($gongpainum,1)->order($gongpaitime.' asc')->where('find_in_set('.$dingjipid.',path)')->select()->toArray();
             }
         }
         if(!empty($memberlist))
         {
             $dingjipid = $memberlist[0]['id']; 
         }else{
              return  0;
         }
         $allcount = Db::name('member')->where('aid',$aid)->where($gongpaipid,$dingjipid)->count();//顶级下级人数
         var_dump('公排下级人数'.$allcount);
         $gongpaizhi_ren = $set['gongpaizhi_ren_lun'];
    //      if($lun == 0)
       // {
       //     $gongpaizhi_ren = $set['gongpaizhi_ren'];
       // }else{
       //     $gongpaizhi_ren = $set['gongpaizhi_ren_lun'];
       // }
       // var_dump($gongpaizhi_ren);die;
         if($allcount < $gongpaizhi_ren)
         {
             return  $dingjipid;
         }else{
             var_dump('顶级以满足,循环查找下面的');
            $allcountarray = Db::name('member')->where('aid',$aid)->where($gongpaipid,$dingjipid)->field('id')->select()->toArray();
            foreach($allcountarray as $v)
            {
                 $allcount1 = Db::name('member')->where('aid',$aid)->where($gongpaipid,$v['id'])->count();
                 var_dump('用户'.$v['id'].'公排人数'.$allcount1);
                 if($allcount1 < $gongpaizhi_ren)
                 {
                    $returnpid = $v['id'];
                    break;
                 }
            }
           // var_dump($returnpid);
            if($returnpid == 0)
            {
                 $returnpid = self::getxiajicount($aid,$allcountarray,$set,$lun);
            }
           // var_dump($returnpid);die;
            return $returnpid;
             
         }
     }
     
     public static function getxiajicount($aid,$allcountarray,$set,$lun=1)
     {
       //   var_dump('第'.$lun.'轮333');
         if($lun == 1)
         {
             $gongpainum = 'gongpainum';
             $gongpaipid = 'gongpaipid';
             $gongpaitime = 'gongpaitime';
             
         }elseif($lun == 2)
         {
             $gongpainum = 'gongpainum2';
             $gongpaipid = 'gongpaipid2';
             $gongpaitime = 'gongpaitime2';
         }elseif($lun == 3)
         {
             $gongpainum = 'gongpainum3';
             $gongpaipid = 'gongpaipid3';
             $gongpaitime = 'gongpaitime3';
         }elseif($lun == 4)
         {
             $gongpainum = 'gongpainum4';
             $gongpaipid = 'gongpaipid4';
             $gongpaitime = 'gongpaitime4';
         }elseif($lun == 5)
         {
             $gongpainum = 'gongpainum5';
             $gongpaipid = 'gongpaipid5';
             $gongpaitime = 'gongpaitime5';
         }elseif($lun == 6)
         {
             $gongpainum = 'gongpainum6';
             $gongpaipid = 'gongpaipid6';
             $gongpaitime = 'gongpaitime6';
         }elseif($lun == 7)
         {
             $gongpainum = 'gongpainum7';
             $gongpaipid = 'gongpaipid7';
             $gongpaitime = 'gongpaitime7';
         }elseif($lun == 8)
         {
             $gongpaipid = 'gongpaipid8';
             $gongpainum = 'gongpainum8';
             $gongpaitime = 'gongpaitime8';
         }
        $returnpid = 0;
        $gongpaizhi_ren = $set['gongpaizhi_ren_lun'];
       //  if($lun == 0)
       // {
       //     $gongpaizhi_ren = $set['gongpaizhi_ren'];
       // }else{
       //     $gongpaizhi_ren = $set['gongpaizhi_ren_lun'];
       // }
       $allcountarraymer = [];
        foreach($allcountarray as $v)
        {
           // var_dump('判断的用户id'.$v['id']);
             $allcountarray2 = Db::name('member')->where('aid',$aid)->where($gongpaipid,$v['id'])->order($gongpaitime.' asc')->field('id')->select()->toArray();
           //  var_dump($allcountarray2);
             $allcountarraymer = array_merge($allcountarraymer,$allcountarray2);
           //  var_dump($allcountarraymer);
             foreach ($allcountarray2 as $v2)
             {
               //  var_dump('判断用户2-'.$v2['id']);
                  $allcount2 = Db::name('member')->where('aid',$aid)->where($gongpaipid,$v2['id'])->count();
               //   var_dump('用户2数量'.$allcount2);
                  if($allcount2 < $gongpaizhi_ren)
                  {
                       $returnpid = $v2['id'];
                       break;
                  }
               //   var_dump($allcount2);
             }
             if($allcount2 < $gongpaizhi_ren)
              {
                   break;
              }
              
        }
        if($returnpid == 0)
        {
            $returnpid = self::getxiajicount($aid,$allcountarraymer,$set,$lun);
            if($returnpid > 0)
            {
                return $returnpid;
            }
        }else{
            return $returnpid;
        }
        
     }
     //切割时间
     public static function qiegetime($starttime,$arr=[])
     {
         $time = time(); 
         if($starttime)
         {
             $currentTime  = date('Y-m-d H:i:s',$starttime);
             $newTime = strtotime($currentTime . ' +5 days');
             $newTime2  = date('Y-m-d H:i:s',$newTime);
             $arr[]= $newTime2;
             if($newTime <= $time)
             {
                 //继续切割
                 return self::qiegetime($newTime,$arr);
             }else{
                 return $arr;
             }
         }else{
             return false;
         }
     }

     //给排队奖励
     public static function givepaiduijiang($aid,$mid,$orderid,$ogid,$ordernum,$qu = 0)
     {
         $giveordergood = Db::name('paidui_list')->where('aid',$aid)->where('orderid',$orderid)->where('ordergoodid',$ogid)->where('qu',$qu)->find();
         if(!empty($giveordergood))
         {
            $order_gooods= Db::name('shop_order_goods')->where('aid',$aid)->where('orderid', $giveordergood['orderid'])->where('id',$ogid)->find();

             $member =  Db::name('member')->where('aid',$aid)->where('id',$giveordergood['mid'])->field('pid,path,levelid')->find();
             if($member['path'])
             {
                //加速
                 $zhi_pid = $member['pid'];
                 $jian_pid = 0;
                 $jian_pidArr = explode(',',$member['path']);
                 if(count($jian_pidArr) >=2)
                 {
                     $jian_pid = $jian_pidArr[count($jian_pidArr)-2];
                 }
                 if($zhi_pid > 0)
                 {
                     $zhi_pid_level =  Db::name('member_level')->where('aid',$aid)->where('id',$member['levelid'])->field('paidui_zhijiasu')->find();
                     if(!empty($zhi_pid_level) && $zhi_pid_level['paidui_zhijiasu'] >0)
                     {
                         $jiang_zhi = $give_zhi = round($order_gooods['totalprice']*$zhi_pid_level['paidui_zhijiasu']*0.01,2);
                         $pai_list_zhi = Db::name('paidui_list')->where('aid',$aid)->where('mid',$zhi_pid)->where('paidui_jiang','>',0)
                         ->where('status','<>',2)->where('status','<>',3)->order('id asc')->select()->toArray();
                         foreach($pai_list_zhi as $k=>$v)
                         {
                             if($give_zhi  >0){
                                
                                 $shiji_dedao_zhi = $v['shiji_dedao']+$give_zhi;
                                 if($shiji_dedao_zhi >=$v['paidui_jiang'] )
                                 {
                                     $shiji_dedao_zhi = $v['paidui_jiang'];
                                     $status =2;//完成
                                     $gaidong = $v['paidui_jiang']-$v['shiji_dedao'];
                                     $give_zhi = $give_zhi - $gaidong;
                                     $bool = self::addscore3($aid,$v['mid'],$v['bid'],$gaidong,'排队免单直推加速返现,订单已完成,队列id:'.$v['id'].',订单编号:'.$ordernum);
                                 }else{
                                     $shiji_dedao_zhi = $v['shiji_dedao']+$give_zhi;
                                     $status =1;//进行中
                                     $give_zhi = 0;
                                      $bool = self::addscore3($aid,$v['mid'],$v['bid'],$jiang_zhi,'排队免单直推加速返现,队列id'.$v['id'].',订单编号:'.$ordernum);
                                 }
                                 Db::name('paidui_list')->where('aid',$aid)->where('id',$v['id'])->update(['shiji_dedao'=>$shiji_dedao_zhi,'status'=>$status]);
                             }else{
                                 break;
                             }
                         }
                     }
                 }
                 if($jian_pid >0)
                 {
                     $jianmember =  Db::name('member')->where('aid',$aid)->where('id',$jian_pid)->field('pid,path,levelid')->find();
                     $jian_pid_level =  Db::name('member_level')->where('aid',$aid)->where('id',$jianmember['levelid'])->field('paidui_jianjiasu')->find();
                     if(!empty($jian_pid_level) && $jian_pid_level['paidui_jianjiasu'] >0)
                     {
                         $jiang_jian = $give_jian = round($order_gooods['totalprice']*$jian_pid_level['paidui_jianjiasu']*0.01,2);
                         $pai_list_jian = Db::name('paidui_list')->where('aid',$aid)->where('mid',$jian_pid)->where('paidui_jiang','>',0)
                         ->where('status','<>',2)->where('status','<>',3)->order('id asc')->select()->toArray();
                         foreach($pai_list_jian as $k=>$v)
                         {
                             if($give_jian  >0){
                                
                                 $shiji_dedao_jian = $v['shiji_dedao']+$give_jian;
                                 if($shiji_dedao_jian >=$v['paidui_jiang'] )
                                 {
                                     $shiji_dedao_jian = $v['paidui_jiang'];
                                     $status =2;//完成
                                     $gaidong = $v['paidui_jiang']-$v['shiji_dedao'];
                                     $give_jian = $give_jian - $gaidong;
                                     $bool = self::addscore3($aid,$v['mid'],$v['bid'],$gaidong,'排队免单间推加速返现,订单已完成,队列id:'.$v['id'].',订单编号:'.$ordernum);
                                 }else{
                                     $shiji_dedao_jian = $v['shiji_dedao']+$give_jian;
                                     $status =1;//进行中
                                     $give_jian = 0;
                                     $bool = self::addscore3($aid,$v['mid'],$v['bid'],$jiang_jian,'排队免单间推加速返现,队列id'.$v['id'].',订单编号:'.$ordernum);
                                 }
                                 Db::name('paidui_list')->where('aid',$aid)->where('id',$v['id'])->update(['shiji_dedao'=>$shiji_dedao_jian,'status'=>$status]);
                             }else{
                                 break;
                             }
                         }
                     }
                 }
             }
             
            //  var_dump($giveordergood);
// 确保有需要分配的金额
if ($giveordergood['paidui_give'] > 0) {
    $wherebid = 0;
    
    // 设置条件，决定 $wherebid 和其他变量的值
    if ($giveordergood['bid'] > 0 && $giveordergood['type_status'] > 0 && $giveordergood['type'] > 0) {
        $wherebid = $giveordergood['bid'];
        $type_status = 1;
        $type = $giveordergood['type'];
    } else {
        $type_status = $giveordergood['type_status'];
        $type = $giveordergood['type'];
    }
    
    // 初始化分配金额为当前订单的 paidui_give
    $give = $giveordergood['paidui_give']; // 本次可分配的金额
    $jiangli = 0;

    // 查询所有未完成的订单
    $dedaolist = Db::name('paidui_list')
        ->where('aid', $aid)
        ->where('paidui_jiang', '>', 0)
        ->where('status', '<>', 2) // 未完成状态
        ->where('status', '<>', 3)
        ->where(function($query) use ($wherebid) {
            if ($wherebid > 0) {
                $query->where('bid', $wherebid);
            }
        })
        ->order('id asc')
        ->select()
        ->toArray();
// var_dump($dedaolist);die;
    // 遍历符合条件的订单列表，进行奖励分配
    foreach ($dedaolist as $v) {
        if (bccomp($give, '0', 2) > 0) { // 确保有剩余金额可以分配
            // 计算当前订单还可以得到的奖励金额
            $gaide = bcsub($v['paidui_jiang'], $v['shiji_dedao'], 2);
            
            // 判断当前剩余的奖励金额是否可以全部分配给当前订单
            if (bccomp($gaide, $give, 2) >= 0) {
                $jiangli = $give;
                $give = '0';
            } else {
                $jiangli = $gaide;
                $give = bcsub($give, $gaide, 2);
            }
            
            // 更新当前订单的已得到奖励金额
            $shiji_dedao = bcadd($v['shiji_dedao'], $jiangli, 2);
            
            // 判断订单是否已完成
            if (bccomp($shiji_dedao, $v['paidui_jiang'], 2) == 0) {
                $status = 2; // 完成
                $bool = self::addscore3($aid, $v['mid'], $v['bid'], $jiangli, '排队免单返现,订单已完成,队列id:'.$v['id'].',订单编号:'.$ordernum);
                // 完成时 give_shengyu 应该是 0
                $give_shengyu = 0;
            } else {
                $status = 1; // 进行中
                $bool = self::addscore3($aid, $v['mid'], $v['bid'], $jiangli, '排队免单返现,队列id'.$v['id'].',订单编号:'.$ordernum);
                // 未完成时更新 give_shengyu
                $give_shengyu = bcsub($v['paidui_jiang'], $shiji_dedao, 2);
            }
            
            // 更新数据库中订单的已得到金额和状态
            Db::name('paidui_list')
                ->where('aid', $aid)
                ->where('id', $v['id'])
                ->update(['shiji_dedao' => $shiji_dedao, 'status' => $status, 'give_shengyu' => $give_shengyu]);
        } else {
            break; // 如果没有剩余奖励金额，则退出循环
        }
    }
}

// 检查是否是复购行为（即同一用户之前已有订单）
$memberOrderCount = Db::name('shop_order')->where('aid', $aid)->where('mid', $mid)->where('status', 1)->count();
if ($memberOrderCount > 1) {
    // 获取用户当前等级的复购加速比例
    
    $memberInfo = Db::name('member')->where('aid', $aid)->where('id', $mid)->field('levelid')->find();
    $fugouLevel = Db::name('member_level')->where('aid', $aid)->where('id', $memberInfo['levelid'])->field('paidui_fugoujiasu')->find();
    
    if (!empty($fugouLevel) && $fugouLevel['paidui_fugoujiasu'] > 0) {
        // 查找用户自己正在排队中的记录
     
        $pai_list_fugou = Db::name('paidui_list')->where('aid', $aid)->where('mid', $mid)
                         ->where('paidui_jiang', '>', 0)
                         ->where('status', '<>', 2)
                         ->where('status', '<>', 3)
                         ->order('id', 'asc')
                         ->select()->toArray();
           
        if (!empty($pai_list_fugou)) {
            // 获取订单信息计算加速金额
            $orderInfo = Db::name('shop_order')->where('aid', $aid)->where('id', $orderid)->find();
            if (!empty($orderInfo)) {
                $jiang_fugou = $give_fugou = round($orderInfo['totalprice'] * $fugouLevel['paidui_fugoujiasu'] * 0.01, 2);
                
                foreach ($pai_list_fugou as $k => $v) {
                    if ($give_fugou > 0) {
                        $shiji_dedao_fugou = $v['shiji_dedao'] + $give_fugou;
                        
                        if ($shiji_dedao_fugou >= $v['paidui_jiang']) {
                            $shiji_dedao_fugou = $v['paidui_jiang'];
                            $status = 2; // 完成
                            $gaidong = $v['paidui_jiang'] - $v['shiji_dedao'];
                            $give_fugou = $give_fugou - $gaidong;
                            $bool = self::addscore3($aid, $v['mid'], $v['bid'], $gaidong, '排队免单复购加速返现,订单已完成,队列id:' . $v['id'] . ',订单编号:' . $ordernum);
                        } else {
                            $shiji_dedao_fugou = $v['shiji_dedao'] + $give_fugou;
                            $status = 1; // 进行中
                            $bool = self::addscore3($aid, $v['mid'], $v['bid'], $give_fugou, '排队免单复购加速返现,队列id:' . $v['id'] . ',订单编号:' . $ordernum);
                            $give_fugou = 0;
                        }
                        
                        Db::name('paidui_list')->where('aid', $aid)->where('id', $v['id'])->update(['shiji_dedao' => $shiji_dedao_fugou, 'status' => $status]);
                    } else {
                        break;
                    }
                }
            }
        }
    }
}

return true;


     }
}
 
 
 
public static function givepaiduijiangModel($aid, $mid, $orderid, $ordernum, $setting)
{
    // 从数据库中获取订单信息
    $giveOrderInfo = Db::name('paidui_list')->where('aid', $aid)->where('id', $orderid)->find();
    if (!empty($giveOrderInfo)) {
        $bid = $giveOrderInfo['bid'];
        // 排队奖励总数，这里使用了paidui_jiang字段
        $commission = $giveOrderInfo['paidui_jiang'];
        //排队返现总数
        $paiduifanxianzongshu = $giveOrderInfo['paidui_give'];
        // 订单本人返现计算
        if ($setting['consumers_proportion'] != 0) {
            // 计算用户返现
            $cashback = bcmul($paiduifanxianzongshu, $setting['consumers_proportion'], 5);
            // 添加返现记录
            self::addscore3($aid, $mid, $bid, $cashback, '排队免单-分成模型1-返现20%,订单编号:' . $ordernum);
            // 计算剩余金额
            $remainderValue = bcsub($commission, $cashback, 5);
            // 更新数据库中用户的实际得到金额和剩余金额
            Db::name('paidui_list')
                ->where('aid', $aid)
                ->where('id', $orderid)
                ->update(['shiji_dedao' => $cashback, 'give_shengyu' => $remainderValue]);
        }

        $alreadyDivide = [];

        // 店铺分成计算
        if ($setting['shop_proportion'] != 0) {
            // 计算店铺分成金额
            $shopCommission = bcmul($paiduifanxianzongshu, $setting['shop_proportion'], 5);

            // 单店第一奖励金额（确认计算正确）
            $shopFirst = $setting['shop_first'] != 0 ? bcmul($shopCommission, $setting['shop_first'], 5) : 0;
           
            // 计算店铺剩余分成金额
            $shopRemainder = bcsub($shopCommission, $shopFirst, 5);  // 确保减去的是 shopFirst
 
            // 查询符合条件的店铺订单
            $queryShopList = Db::name('paidui_list')
                ->where('aid', $aid)
                ->where('bid', $bid)
                ->where('paidui_jiang', '>', 0)
                ->where('give_shengyu', '>', 0)
                ->whereNotIn('status', '2, 3')
                ->order('id asc');

            $shopList = $queryShopList->select()->toArray();

            $shopCount = count($shopList);

            // 判断是均分还是加权分成
            if (!empty($setting['shop_is_share'])) {
                // 均分模式
                $shopIsShare = true;
                $shopShare = bcdiv($shopRemainder, $shopCount, 5);
               
            } else {
                // 加权模式，根据总权重计算
                $weighting = $queryShopList->sum('paidui_jiang');
            }

            foreach ($shopList as $key => $value) {
                if (!$shopIsShare) {
                    // 计算加权比例
                    $tempProportion = bcdiv($value['paidui_jiang'], $weighting, 5);
                    $shopShare = bcmul($tempProportion, $shopRemainder, 5);
                }
                // 计算当前订单的额外金额
               
                $additionalAmount = ($key == 0) ? bcadd($shopFirst, $shopShare, 5) : $shopShare;
  //var_dump( $additionalAmount);die;
                if ($additionalAmount != 0) {
                    // 计算订单的实际得到金额
                    $tempValue = bcadd($value['shiji_dedao'], $additionalAmount, 5);
                    // var_dump(  $tempValue);die;
                    $status = $tempValue >= $value['paidui_jiang'] ? 2 : 1;
                    $actualValue = $status === 2 ? $value['paidui_jiang'] : $tempValue;
                    $changeValue = $status === 2 ? bcsub($value['paidui_jiang'], $value['shiji_dedao'], 5) : $additionalAmount;
                    $remainderValue = $status === 2 ? 0 : bcsub($value['give_shengyu'], $changeValue, 5);

                    // 记录日志信息
                    $logMessage = '排队免单返现-分成模型1-店铺-';
                    $logMessage .= $status === 2 ? ($key == 0 ? '顺位第一,订单已完成' : '阳光普照,订单已完成') : ($key == 0 ? '顺位第一' : '阳光普照');
                    $logMessage .= ',队列id:' . $value['id'] . ',订单编号:' . $ordernum;

                    // 添加分成记录
                    self::addscore3($aid, $value['mid'], $value['bid'], $changeValue, $logMessage);

                    // 更新数据库中店铺的实际得到金额、剩余金额和状态
                    Db::name('paidui_list')
                        ->where('aid', $aid)
                        ->where('id', $value['id'])
                        ->update(['shiji_dedao' => $actualValue, 'give_shengyu' => $remainderValue, 'status' => $status]);
                }

                // 记录已经分成的订单
                $alreadyDivide[] = $value['id'];
            }
        }

        // 读取多城市配置
        $admin_set = Db::name('admin_set')->where(['aid' => $aid])->field('area_on,area_set')->find();
        if (empty($admin_set) && empty($admin_set['area_on']) && empty($admin_set['area_set'])) {
            // 未开多城市，直接返回
            return true;
        }

        // 城市分成计算
        if ($setting['region_proportion'] != 0) {
            // 分区模式
            $area_set = json_decode($admin_set['area_set'], true);
            $areamode = $area_set['areamode'];

            $areaArray = [
                'province',
                'city',
                'district'
            ];
            $areaMode = $areaArray[$areamode];
            $areaModeId = $areaMode . '_id';

            // 获取当前订单的地区
            if (empty($giveOrderInfo['qu'])) {
                // 如果订单没有指定地区，通过商品信息获取地区
                $giveOrderArea = Db::name('shop_order_goods')
                    ->alias('g')
                    ->field('p.province_id, p.city_id, p.district_id, p.province, p.city, p.district')
                    ->join('shop_product p', 'p.id = g.proid')
                    ->where('g.aid', $aid)->where('g.orderid', $giveOrderInfo['orderid'])->where('g.id', $giveOrderInfo['ordergoodid'])->find();
            } else {
                // 通过商家信息获取地区
                $giveOrderArea = Db::name('business')->field('province_id, city_id, district_id, province, city, district')->where('aid', $aid)->where('id', $bid)->find();
            }
            $giveOrderAreaId = $giveOrderArea[$areaModeId];

            // 计算地区分成金额
            $regionCommission = bcmul($paiduifanxianzongshu, $setting['region_proportion'], 5);

            // 地区第一奖励金额
            $regionFirst = $setting['region_first'] != 0 ? bcmul($regionCommission, $setting['region_first'], 5) : 0;
            // 计算地区剩余分成金额
            $regionRemainder = bcsub($regionCommission, $regionFirst, 5);

            // 查询符合条件的订单
            $queryShopList = Db::name('paidui_list')
                ->where('aid', $aid)
                ->where('paidui_jiang', '>', 0)
                ->where('give_shengyu', '>', 0)
                ->whereNotIn('status', '2, 3')
                ->order('id asc');

            // 地区订单列表
            $regionOrderList = $queryShopList->select()->toArray();

            $commissionsList = [];
            $weighting = 0;
            if (!empty($regionOrderList)) {
                foreach ($regionOrderList as $key => $value) {
                    // 如果订单没有指定地区，通过商品信息获取地区
                    if (empty($value['qu'])) {
                        $orderArea = Db::name('shop_order_goods')
                            ->alias('g')
                            ->field('p.province_id, p.city_id, p.district_id, p.province, p.city, p.district')
                            ->join('shop_product p', 'p.id = g.proid')
                            ->where('g.aid', $aid)->where('g.orderid', $value['orderid'])->where('g.id', $value['ordergoodid'])->find();
                    }
                    // 通过买单信息获取地区
                    else {
                        $orderArea = Db::name('maidan_order')
                            ->alias('m')
                            ->field('b.province_id, b.city_id, b.district_id, b.province, b.city, b.district')
                            ->join('business b', 'b.id = m.bid')
                            ->where('m.aid', $aid)->where('m.id', $value['orderid'])->find();
                    }

                    // 如果地区匹配，加入分成列表
                    if ($giveOrderAreaId == $orderArea[$areaModeId]) {
                        $commissionsList[] = $value;
                        // 加权paidui_jiang
                        $weighting = bcadd($weighting, $value['paidui_jiang'], 5);
                    }
                }
            }

            if (!empty($commissionsList)) {
                // 判断是均分还是加权分成
                if (!empty($setting['region_is_share'])) {
                    // 均分模式
                    $regionIsShare = true;
                    $regionShare = bcdiv($regionRemainder, count($commissionsList), 5);
                }

                foreach ($commissionsList as $key => $value) {
                    if (!$regionIsShare) {
                        // 计算加权比例
                        $tempProportion = bcdiv($value['paidui_jiang'], $weighting, 5);
                        $regionShare = bcmul($tempProportion, $regionRemainder, 5);
                    }
                    // 计算当前订单的额外金额
                    $additionalAmount = ($key == 0) ? bcadd($regionFirst, $regionShare, 5) : $regionShare;

                    if ($additionalAmount != 0) {
                        // 计算订单的实际得到金额
                        $tempValue = bcadd($value['shiji_dedao'], $additionalAmount, 5);
                        $status = $tempValue >= $value['paidui_jiang'] ? 2 : 1;
                        $actualValue = $status === 2 ? $value['paidui_jiang'] : $tempValue;
                        $changeValue = $status === 2 ? bcsub($value['paidui_jiang'], $value['shiji_dedao'], 5) : $additionalAmount;
                        $remainderValue = $status === 2 ? 0 : bcsub($value['give_shengyu'], $changeValue, 5);

                        // 记录日志信息
                        $logMessage = '排队免单返现-分成模型1-地区-';
                        $logMessage .= $status === 2 ? ($key == 0 ? '顺位第一,订单已完成' : '阳光普照,订单已完成') : ($key == 0 ? '顺位第一' : '阳光普照');
                        $logMessage .= ',队列id:' . $value['id'] . ',订单编号:' . $ordernum;

                        // 添加分成记录
                        self::addscore3($aid, $value['mid'], $value['bid'], $changeValue, $logMessage);

                        // 更新数据库中订单的实际得到金额、剩余金额和状态
                        Db::name('paidui_list')
                            ->where('aid', $aid)
                            ->where('id', $value['id'])
                            ->update(['shiji_dedao' => $actualValue, 'give_shengyu' => $remainderValue, 'status' => $status]);
                    }
                }
            }
        }
    }
    return true;
}




     
     //脱离
     public static function liandongtuoli($aid,$mid)
     {
         $liandongrs = Db::name('liandong_set')->where('aid',$aid)->find();
         if(empty($liandongrs))
         {
             return;
         }elseif($liandongrs['status'] == 0)
         {
             return;
         }else{
             $member = Db::name('member')->field('id,path,levelid,pid,tuo1,ytpid,ytpath')->where('aid',$aid)->where('id',$mid)->find();
             $curlevel = Db::name('member_level')->where('aid',$aid)->where('id',$member['levelid'])->find();
           //   var_dump($curlevel['tuoli_jiedianren']);die;
            if($curlevel['tuoli_jiedianren'] == 0){
                 //下级脱离
                 if($curlevel['tuoli_member_count'] > 0)
                 {
                     $xiaji = Db::name('member')->field('id,path,levelid,pid,tuo1,ytpid,ytpath')->where('aid',$aid)->where('tuo1',0)->where('pid',$mid)->select()->toArray();
                     if(!empty($xiaji))
                     {
                         if($curlevel['tuoli_member_son_levelid'])
                         {
                            $xiajiArr = [];
                            $sonlevel = explode(',',$curlevel['tuoli_member_son_levelid']);
                            foreach($xiaji as $v)
                            {
                                if(in_array($v['levelid'],$sonlevel))
                                {
                                    $xiajiArr[] = $v;
                                }
                            }
                         }else{
                             $xiajiArr = $xiaji;
                         }
                         //符合等级的下级  $xiajiArr
                         $xiajiArr = array_slice($xiajiArr,0,$curlevel['tuoli_member_count']);
                         //获取上级
                         $xiajipid = 0;
                         $xiajipidArr = [];
                         if($curlevel['tuoli_member_parent_levelid'])
                         {
                             $parentlevel = explode(',',$curlevel['tuoli_member_parent_levelid']);
                             $parentArr = array_reverse(explode(',',$member['path']));
                             foreach($parentArr as  $v)
                             {
                                 $vmember = Db::name('member')->field('id,path,levelid,pid,tuo1,ytpid,ytpath')->where('aid',$aid)->where('id',$v)->find();
                                 if(in_array($vmember['levelid'],$parentlevel))
                                {
                                    $xiajipid = $v;
                                    $xiajipidArr = $vmember;
                                    break;
                                }
                             }
                         }else{
                             $vmember = Db::name('member')->field('aid',$aid)->where('id',$v)->find();
                             $xiajipid = $member['pid'];
                             $xiajipidArr = $vmember;
                         }
                         if($xiajipid)
                         {
                             if($xiajipidArr['path'])
                             {
                                 $path = $xiajipidArr['path'].','.$xiajipid;
                             }else{
                                 $path = $xiajipid;
                             }
                         }else{
                             $path = ''; 
                         }
                         foreach($xiajiArr as $v)
                         {
                             $update_data = [];
                             $update_data['pid'] =$xiajipid;
                             $update_data['path'] = $path;
                             $update_data['ytpid'] = $mid;
                             $update_data['ytpath'] = $v['path'];
                             $update_data['tuo1'] = 1;
                             $res = Db::name('member')->where('aid',$aid)->where('id',$v['id'])->update($update_data);
                             $allpath = trim($v['path'].','.$path.','.$v['id']);//更改我的所有下级
                             m::updatepath($allpath,1);
                             $insertdata=[];
                             $insertdata['aid'] = $aid;
                             $insertdata['mid'] = $v['id'];
                             $insertdata['ytpid'] = $mid;
                             $insertdata['newpid'] = $xiajipid;
                             $insertdata['createtime'] = time();
                             $insertdata['type'] = 0;
                             Db::name('liandong_tuoli_log')->insert($insertdata);
                         }
                         
                     }
                 }else{
                     return;
                 }
                 //我
                 if($curlevel['tuoli_shengji_prent1'] == 1)
                 {
                    //脱离上级//  升级后脱离上级 1是0否
                    $mypid = 0;//脱离上级 归于平台
                    $update_data = [];
                    $update_data['pid'] =0;
                    $update_data['path'] = '';
                    $update_data['ytpid'] = $member['pid'];
                    $update_data['ytpath'] = $member['path'];
                    $update_data['tuo1'] = 1;
                    $res = Db::name('member')->where('aid',$aid)->where('id',$member['id'])->update($update_data);
                    $allpath = trim($member['id']);//更改我的所有下级
                    m::updatepath($allpath,1);
                    $insertdata=[];
                    $insertdata['aid'] = $aid;
                    $insertdata['mid'] = $mid;
                    $insertdata['ytpid'] = $member['pid'];
                    $insertdata['newpid'] = $mypid;
                    $insertdata['createtime'] = time();
                    $insertdata['type'] = 0;
                    Db::name('liandong_tuoli_log')->insert($insertdata);
                 }
                 //回归上级
                 if($curlevel['tuoli_shengji_prent2'] == 1)
                 {
                     
                     //回到原来的推荐人下面
                     if($member['tuo1'] ==1)
                     {
                         $ytpidarr = Db::name('member')->where('aid',$aid)->where('id',$member['ytpid'])->find();
                         $pathArr = m::getpath($ytpidarr,[]);
                         $pathArr = array_reverse($pathArr);
                         $pathstr = implode(',',$pathArr);
                         $pathstr = $pathstr.','.$member['ytpid'];
                         $update_data = [];
                         $update_data['pid'] =$member['ytpid'];
                         $update_data['path'] = $pathstr;
                        //  $update_data['ytpid'] = 0;
                        //  $update_data['ytpath'] = '';
                         $update_data['tuo1'] = 2;
                         $res = Db::name('member')->where('aid',$aid)->where('id',$member['id'])->update($update_data);
                         $allpath = trim($member['id']);//更改我的所有下级
                         m::updatepath($allpath);
                         $insertdata=[];
                         $insertdata['aid'] = $aid;
                         $insertdata['mid'] = $mid;
                         $insertdata['ytpid'] = $member['pid'];
                         $insertdata['newpid'] = $member['ytpid'];
                         $insertdata['createtime'] = time();
                         $insertdata['type'] = 1;
                         Db::name('liandong_tuoli_log')->insert($insertdata);
                     }
                 }
                 return true;
            }else{
                //     
                if($curlevel['tuoli_jiedian_member_count'] >0)
                {
                    if($curlevel['tuoli_jiedian_member_son_levelid'])
                    {
                        $xiaji = Db::name('member')->field('id,path,levelid,liandongren,tuo1,ytpid,ytpath')->where('aid',$aid)->where('tuo1',0)->where('liandongren',$mid)
                        ->where('levelid','in',$curlevel['tuoli_jiedian_member_son_levelid'])->select()->toArray();
                    }else{
                         $xiaji = Db::name('member')->field('id,path,levelid,liandongren,tuo1,ytpid,ytpath')->where('aid',$aid)->where('tuo1',0)->where('liandongren',$mid)->select()->toArray();
                    }
                    if(!empty($xiaji)){
                        $xiajiArr = array_slice($xiaji,0,$curlevel['tuoli_jiedian_member_count']);
                        //获取上级
                        $parentArr = self::getdownliandongrenparent($aid,$mid,0);
                        $updatepid = 0;
                        foreach ($parentArr as $v)
                        {
                            if($v)
                            {
                                $member_parent =  Db::name('member')->field('id,path,levelid,liandongren,tuo1,ytpid,ytpath')->where('aid',$aid)->where('id',$v)->where('levelid','in',$curlevel['tuoli_jiedian_member_parent_levelid'])->find();
                                if(!empty($member_parent))
                                {
                                    $updatepid = $v;
                                    break;
                                }
                            }
                        }
                        if($updatepid >0)
                        {
                            $num = 0;
                            foreach($xiajiArr as $v)
                             {
                                 $num = $num+1;
                                 $update_data = [];
                                 $update_data['liandongren'] =$updatepid;
                                 $update_data['yliandongren'] =$mid;
                                 $update_data['tuo1'] = 1;
                                 $res = Db::name('member')->where('aid',$aid)->where('id',$v['id'])->update($update_data);
                                 $insertdata=[];
                                 $insertdata['aid'] = $aid;
                                 $insertdata['mid'] = $v['id'];
                                 $insertdata['ytpid'] = $mid;
                                 $insertdata['newpid'] = $updatepid;
                                 $insertdata['createtime'] = time();
                                 $insertdata['type'] = 3;
                                 Db::name('liandong_tuoli_log')->insert($insertdata);
                             }
                             if($curlevel['tuoli_liandongjiang'] >0){
                                 $tuoli_liandongjiang = $curlevel['tuoli_liandongjiang']*$num;
                                 self::addcommission($aid,$updatepid,0,$tuoli_liandongjiang,'联动奖');
                                 $member_parent = Db::name('member')->field('id,path,levelid,liandongren,tuo1,ytpid,ytpath')->where('aid',$aid)->where('id',$updatepid)->find();
                                 if($member_parent['path'])
                                 {
                                     $curlevel2 = Db::name('member_level')->where('aid',$aid)->where('id',$member_parent['levelid'])->find();
                                     $pathArr = array_reverse(explode(',',$member_parent['path']));
                                     if($curlevel2['tuoli_liandongjiangshang1'] >0 && isset($pathArr[0]))
                                     {
                                         $yiji = $tuoli_liandongjiang *$curlevel2['tuoli_liandongjiangshang1']*0.01;
                                         self::addcommission($aid,$pathArr[0],$updatepid,$yiji,'联动奖-抽佣');
                                     }
                                      if($curlevel2['tuoli_liandongjiangshang2'] >0 && isset($pathArr[1]))
                                     {
                                         $erji= $tuoli_liandongjiang *$curlevel2['tuoli_liandongjiangshang2'] *0.01;
                                         self::addcommission($aid,$pathArr[1],$updatepid,$erji,'联动奖-抽佣');
                                     }
                                      if($curlevel2['tuoli_liandongjiangshang3'] >0 && isset($pathArr[2]))
                                     {
                                         $sanji= $tuoli_liandongjiang *$curlevel2['tuoli_liandongjiangshang3']*0.01;
                                         self::addcommission($aid,$pathArr[2],$updatepid,$sanji,'联动奖-抽佣');
                                     }
                                 }
                             }
                        }
                    }
                }else{
                     return;
                }
                return true;
            }
         }
         return;
     }
      public static function getdownliandongrenparent($aid,$mid,$deeplevel,$parent=[],$oldxingji=0){
        $curmember =  Db::name('member')->field('id,liandongren')->where('aid',$aid)->where('id',$mid)->find(); 
        if($deeplevel > 99){
            return $parent;
        }else{
            $memberparent = Db::name('member')->field('id,nickname,headimg,pid,realname,tel,levelid')->where('aid',$aid)->where('id',$curmember['liandongren'])->find();
            if($oldxingji ==0){
               $parent[]=$memberparent['id'];
            }elseif($oldxingji > 0 && $deeplevel > $oldxingji){
                $parent[]=$memberparent['id'];
            }
            $deeplevel++;
            return self::getdownliandongrenparent($aid,$memberparent['id'],$deeplevel,$parent,$oldxingji);
        }
    }
     
    //判断是否解冻
    public static function  jiedongpanduan($aid,$mid)
    {
       $liandongrs = Db::name('liandong_set')->where('aid',$aid)->find();
       if(empty($liandongrs))
       {
             return;
        }elseif($liandongrs['status'] == 0)
        {
           return;
        }else{ 
            $member = Db::name('member')->field('id,path,levelid,pid,tuo1,ytpid,ytpath,dongjienum,dong_status')->where('aid',$aid)->where('id',$mid)->find();
            $fcj_levelids = explode(',',$liandongrs['fcj_levelids']);
            if($member['dong_status'] != 1){
                //判断自己升级解冻自己
                if(in_array('-1',$fcj_levelids) || in_array($member['levelid'],$fcj_levelids))
                {
                    
                }else{
                    //不参与冻结 解冻之前的 然后设置成解冻的
                    self::jiedong2($member,$aid,'本人升级解冻');
                }
            }
            if($liandongrs['fcj_type'] == 0)
            {
                 if($member['tuo1'] != 0){
                     $curmember = Db::name('member')->field('id,path,levelid,pid,tuo1,ytpid,ytpath,dongjienum,dong_status')->where('aid',$aid)->where('id',$member['ytpid'])->find();
                     if($curmember['dong_status'] != 1)
                     {
                         $tioajian = $liandongrs['fcj_dan_levelid'];
                         $tioajian = explode(',',$tioajian);
                         $manzu = 0;
                         $memberlist = Db::name('member')->field('id,levelid,ytpid')->where('aid',$aid)->where('ytpid',$member['ytpid'])->where('tuo1','<>',0)->select()->toArray();
                         $memberlistcount = count($memberlist);
                         if($memberlistcount >0)
                         {
                            foreach ($memberlist as $v)
                            {
                                 if(in_array($v['levelid'],$tioajian))
                                 {
                                     $manzu = $manzu+1;
                                 }
                            }
                            if($manzu >= $memberlistcount)
                            {
                                //滿足條件
                                self::jiedong2($curmember,$aid,'直推的脱离会员升级解冻');
                            }
                            
                         }
                     }
                     
                 }
            }elseif($liandongrs['fcj_type'] == 1)
            {
                $pathArr = explode(',',$member['path']);
                if(count($pathArr) >0 )
                {
                    $tioajian0 = $liandongrs['fcj_quan_count'];
                    $tioajian1 = $liandongrs['fcj_quan_levelid'];
                   // $tioajian1 = explode(',',$tioajian1);
                    foreach($pathArr as $v)
                    {
                        if($v){
                            $curmember = Db::name('member')->field('id,path,levelid,pid,tuo1,ytpid,ytpath,dongjienum,dong_status')->where('aid',$aid)->where('id',$v)->find();
                            if($curmember['dong_status'] != 1)
                            {
                                $memberlist = Db::name('member')->field('id,levelid,pid,path')->where('aid',$aid)->where('find_in_set('.$v.',path)')->where('levelid','in',$tioajian1)->select()->toArray();
                                $memberlistcount = count($memberlist);
                                if($memberlistcount >= $tioajian0)
                                {
                                    if($liandongrs['fcj_quan_order'] >0)
                                    {
                                        $quan_order_member = Db::name('member')->field('id')->where('aid',$aid)->where('find_in_set('.$v.',path)')->select()->toArray();
                                        $quan_order_member = array_column($quan_order_member,'id');
                                        $quan_order_member = implode(',',$quan_order_member);
                                        if($liandongrs['fcj_quan_goodsid']){
                                             $quan_order = Db::name('shop_order_goods')->where('aid',$aid)->where('proid','in',$liandongrs['fcj_quan_goodsid'])->where('status','in','1,2,3')->where('mid','in',$quan_order_member)->count();//fcj_quan_goodsid
                                        }else{
                                             $quan_order = Db::name('shop_order_goods')->where('aid',$aid)->where('status','in','1,2,3')->where('mid','in',$quan_order_member)->count();//fcj_quan_goodsid
                                        }
                                        ///获取我的直推下级
                                       
                                        if($quan_order >= $liandongrs['fcj_quan_order'])
                                        {
                                            self::jiedong2($curmember,$aid,'伞下会员升级且伞下全部会员订单总量满足解冻'); 
                                        }
                                        
                                    }else{
                                       self::jiedong2($curmember,$aid,'伞下会员升级解冻'); 
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    public static function jiedong2($info,$aid,$remark='')
    {
        if($info['dongjienum'] >0 ){
            \app\common\Member::adddongjienum($aid,$info['id'],'-'.$info['dongjienum'],$remark);
            \app\common\Member::addcommission($aid,$info['id'],0,$info['dongjienum'],$remark);
            Db::name('member')->where('aid',$aid)->where('id',$info['id'])->update(['dong_status'=>1,'dong_time'=>time()]);
            return true;
        }else{
            Db::name('member')->where('aid',$aid)->where('id',$info['id'])->update(['dong_status'=>1,'dong_time'=>time()]);
        }
         return true;
    }
    /**
     * 易货-拓展费增减
     * */
    public static function addtuozhanfei($aid,$mid,$money,$remark)
    {
        if($money==0) return ;
        $member = Db::name('member')->where('aid',$aid)->where('id',$mid)->lock(true)->find();
        if(!$member) return ['status'=>0,'msg'=>t('会员').'不存在'];

        if(false){}else {
            $after = $member['tuozhanfei'] + $money;
            Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['tuozhanfei'=>$after]);
        }

        $data = [];
        $data['aid'] = $aid;
        $data['mid'] = $mid;
        $data['money'] = $money;
        $data['after'] = $after;
        $data['createtime'] = time();
        $data['remark'] = $remark;
        Db::name('member_tuozhanfeilog')->insert($data);

        $tmplcontent = [];
        $tmplcontent['first'] = '您的'.t('拓展费').'发生变动，变动金额：'.$money;
        $tmplcontent['remark'] = '点击进入查看~';
        $tmplcontent['keyword1'] = date('Y-m-d H:i'); //变动时间
        $tmplcontent['keyword2'] = $remark;  //变动类型
        $tmplcontent['keyword3'] = (string) round($money,2);  //变动金额
        $tmplcontent['keyword4'] = (string) round($after,2);  //当前余额
        $rs = \app\common\Wechat::sendtmpl($aid,$mid,'tmpl_moneychange',$tmplcontent,m_url('pages/my/usercenter', $aid));
        return ['status'=>1,'msg'=>''];
    }
    
	 /**
	  * 生成订单
	  * */
	  public static function addorder($aid,$price,$mid,$tel)
	  {
	      if($price == 2400)
	      {
	          $goodsid = 199;
	          
	      }elseif($price == 9600)
	      {
	          $goodsid = 196;
	      }else{
	          return false;
	      }
	      $num = 1;
	      $product = Db::name('shop_product')->where('aid',$aid)->where('id',$goodsid)->find();
	      $guigeid = Db::name('shop_guige')->where('aid',$aid)->where('proid',$goodsid)->find();
	      $guigeid = $guigeid['id'];
	      $post=[
	          'linkman'=>'',
	          'tel'=>$tel,
	          'buydata'=>[
	              [
	                 'bid'=>$product['bid'],
                     'couponrid'=> "",
                     'cuxiaoid'=>0,
                     'formdata'=>[],
                     'freight_id'=> 2,
                     'freight_time'=> "",
                     'prodata'=> $goodsid.",".$guigeid.",".$num,
                     'storeid'=> 0,
                     'type11key'=> 0,
	                  ],
	              ],
	          ];
	         // var_dump($mid);die;
	      $Testh =   new Testh($aid);
	      $c = $Testh->createOrder($mid,$post);
	      return $c;
	      
	      
	  }
     
}
