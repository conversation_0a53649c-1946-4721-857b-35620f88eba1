-- 工作流参数配置表
-- 用于存储每个工作流的自定义参数字段配置

-- 创建工作流参数配置表
CREATE TABLE `ddwx_coze_workflow_params` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `aid` int(11) NOT NULL DEFAULT '0' COMMENT '应用ID',
  `workflow_id` varchar(100) NOT NULL COMMENT '工作流ID',
  `param_key` varchar(100) NOT NULL COMMENT '参数键名',
  `param_name` varchar(200) NOT NULL COMMENT '参数显示名称',
  `param_type` varchar(50) NOT NULL DEFAULT 'text' COMMENT '参数类型：text=文本,number=数字,select=选择,textarea=多行文本,file=文件,image=图片,date=日期,switch=开关',
  `param_options` text COMMENT '参数选项（JSON格式，用于select类型）',
  `default_value` varchar(500) DEFAULT NULL COMMENT '默认值',
  `placeholder` varchar(200) DEFAULT NULL COMMENT '输入提示',
  `description` varchar(500) DEFAULT NULL COMMENT '参数说明',
  `is_required` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否必填：0=否,1=是',
  `validation_rule` varchar(200) DEFAULT NULL COMMENT '验证规则',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序顺序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0=禁用,1=启用',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间戳',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间戳',
  PRIMARY KEY (`id`),
  KEY `idx_workflow_id` (`workflow_id`),
  KEY `idx_aid` (`aid`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作流参数配置表';

-- 插入示例参数配置
INSERT INTO `ddwx_coze_workflow_params` (`aid`, `workflow_id`, `param_key`, `param_name`, `param_type`, `param_options`, `default_value`, `placeholder`, `description`, `is_required`, `validation_rule`, `sort_order`, `status`, `create_time`, `update_time`) VALUES
(1, '7519352650686890038', 'BOT_USER_INPUT', '用户输入内容', 'textarea', NULL, '', '请输入您的问题或需求', '用户向AI提出的问题或需求描述', 1, 'required', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1, '7519352650686890038', 'gender', '性别', 'select', '{"options":[{"value":"男","label":"男"},{"value":"女","label":"女"}]}', '男', NULL, '选择用户的性别', 1, 'required', 2, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1, '7519352650686890038', 'image_url', '图片链接', 'image', NULL, '', '请上传或输入图片链接', '相关的图片资源链接', 0, 'url', 3, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1, '7519352650686890038', 'zhiye', '职业', 'text', NULL, '医生', '请输入职业', '用户的职业信息', 0, NULL, 4, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 为工作流表添加参数配置模式字段
ALTER TABLE `ddwx_coze_workflow` ADD COLUMN `param_config_mode` tinyint(1) NOT NULL DEFAULT '0' COMMENT '参数配置模式：0=JSON模式,1=自定义字段模式' AFTER `params_description`;

-- 显示表结构确认
SHOW COLUMNS FROM `ddwx_coze_workflow_params`;
SHOW COLUMNS FROM `ddwx_coze_workflow`;
